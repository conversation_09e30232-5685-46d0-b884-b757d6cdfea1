<script setup lang="ts">
import { computed, ref, toRef, watch } from 'vue'
import Panel from 'primevue/panel'
import { useToast } from "primevue/usetoast";
import { Form as VeeForm, useForm } from 'vee-validate'
import * as yup from 'yup'
import mV from '../../utils/validationMessages'
import type { OAcademicaEtapa1Pregrado } from '../interfaces/OAcademica';
import { acreditacionOptions, elegible_beca_pedagogiaOptions, area_destinoOPtions, requisito_ingresoOptions } from '../helpers/selectValuesOacademica'
import Dropdown from 'primevue/dropdown'
import useOAcademica from '../composables/useOAcademica'

interface Props {
    oAcademicaEtapa1Pregrado: OAcademicaEtapa1Pregrado,
    showDialog: boolean
}

const toast = useToast();
const props = defineProps<Props>();
const emit = defineEmits(['closeDialog']);
const oAcademicaEtapa1Pregrado = ref<OAcademicaEtapa1Pregrado>({...props.oAcademicaEtapa1Pregrado});


const { oAcademicaEtapa1PregradoMutation } = useOAcademica(props.oAcademicaEtapa1Pregrado.oa_sies_id);

watch(oAcademicaEtapa1PregradoMutation.isSuccess, (value) => {
  if (value) {
    toast.add({ life: 3000, severity: 'success', summary: 'Datos actualizados', detail: 'Los datos han sido actualizados correctamente' });
    oAcademicaEtapa1PregradoMutation.reset();
  }

});

const schema = yup.object({
    acreditacion: yup.number().typeError(mV.number).required(mV.required).oneOf([1, 2], "Debe ser Sí(1) o No(2)"),
    elegible_beca_pedagogia: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1, 2, 3], mV.oneOf),
    requisito_ingreso: yup.number().required(mV.required).oneOf([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], mV.oneOf),
    semestres_reconocidos: yup.number().typeError(mV.number).required(mV.required),
    area_destino_agricultura: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    area_destino_ciencias: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    area_destino_cs_sociales: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    area_destino_educacion: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    area_destino_humanidades: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    area_destino_ingenieria: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    area_destino_salud: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    area_destino_servicios: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    malla_curricular: yup.string().required(mV.required),
    mail_difusion_carrera: yup.string().email(mV.email).required(mV.required),
})

// Configuración del formulario
const { handleSubmit, resetForm, setValues, setFieldValue, errors } = useForm({
    validationSchema: schema,
    initialValues: oAcademicaEtapa1Pregrado.value, // Usamos los valores iniciales desde las props
});

// Manejador del formulario
const onSubmit = handleSubmit((values) => {
    console.log('Valores validados:', values);// Emitimos los datos validados al padre
    oAcademicaEtapa1PregradoMutation.mutate(values);
    
});



</script>

<template>
    <div>

        <VeeForm class="mt-5" @submit="onSubmit">
            <div class="formgrid grid">
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel class="w-full">

                        <Dropdown id="acreditacion" v-model="oAcademicaEtapa1Pregrado.acreditacion"
                            :options="acreditacionOptions" optionLabel="name" optionValue="value"
                            :class="{ 'p-invalid': errors.acreditacion }"
                            @update:modelValue="setFieldValue('acreditacion', oAcademicaEtapa1Pregrado.acreditacion)"
                            class="w-full" />
                        <label for="acreditacion">Acreditación</label>
                        <!-- <InputText name="acreditacion" id="acreditacion" type="text" v-model="oAcademicaEtapa1Pregrado.acreditacion"
                            :class="{ 'p-invalid': errors.acreditacion }"
                            @input="setFieldValue('acreditacion', oAcademicaEtapa1Pregrado.acreditacion)" class="w-full" /> -->
                        <small v-if="errors.acreditacion" class="p-error">{{ errors.acreditacion }}</small>
                    </FloatLabel>
                </div>

                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <Dropdown id="elegible_beca_pedagogia"
                            v-model="oAcademicaEtapa1Pregrado.elegible_beca_pedagogia"
                            :options="elegible_beca_pedagogiaOptions" optionLabel="name" optionValue="value"
                            :class="{ 'p-invalid': errors.elegible_beca_pedagogia }"
                            @update:modelValue="setFieldValue('elegible_beca_pedagogia', oAcademicaEtapa1Pregrado.elegible_beca_pedagogia)"
                            class="w-full" />
                        <label for="elegible_beca_pedagogia">Elegible Beca Pedagogía</label>
                        <small v-if="errors.elegible_beca_pedagogia" class="p-error">{{
                            errors.elegible_beca_pedagogia
                            }}</small>
                    </FloatLabel>
                </div>

                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <label for="requisito_ingreso">Requisito Ingreso</label>
                        <Dropdown name="requisito_ingreso" id="requisito_ingreso" :options="requisito_ingresoOptions"
                            optionLabel="name" optionValue="value" v-model="oAcademicaEtapa1Pregrado.requisito_ingreso"
                            :class="{ 'p-invalid': errors.requisito_ingreso }"
                            @update:modelValue="setFieldValue('requisito_ingreso', oAcademicaEtapa1Pregrado.requisito_ingreso)"
                            class="w-full" />
                        <small v-if="errors.requisito_ingreso" class="p-error">{{ errors.requisito_ingreso
                            }}</small>
                    </FloatLabel>
                </div>

                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <label for="semestre_reconocidos">Semestre Reconocidos</label>
                        <InputNumber name="semestre_reconocidos" id="semestre_reconocidos" type="text"
                            v-model="oAcademicaEtapa1Pregrado.semestres_reconocidos"
                            :class="{ 'p-invalid': errors.semestres_reconocidos }"
                            @input="setFieldValue('semestres_reconocidos', oAcademicaEtapa1Pregrado.semestres_reconocidos)"
                            class="w-full" />
                        <small v-if="errors.semestres_reconocidos" class="p-error">{{ errors.semestres_reconocidos
                            }}</small>
                    </FloatLabel>
                </div>

                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <label for="area_destino_agricultura">Área Destino Agricultura</label>
                        <Dropdown name="area_destino_agricultura" id="area_destino_agricultura" type="text"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            v-model="oAcademicaEtapa1Pregrado.area_destino_agricultura"
                            :class="{ 'p-invalid': errors.area_destino_agricultura }"
                            @update:modelValue="setFieldValue('area_destino_agricultura', oAcademicaEtapa1Pregrado.area_destino_agricultura)"
                            class="w-full" />
                        <small v-if="errors.area_destino_agricultura" class="p-error">{{
                            errors.area_destino_agricultura
                            }}</small>
                    </FloatLabel>
                </div>

                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <label for="area_destino_ciencias">Área Destino Ciencias</label>
                        <Dropdown name="area_destino_ciencias" id="area_destino_ciencias" type="text"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            v-model="oAcademicaEtapa1Pregrado.area_destino_ciencias"
                            :class="{ 'p-invalid': errors.area_destino_ciencias }"
                            @update:modelValue="setFieldValue('area_destino_ciencias', oAcademicaEtapa1Pregrado.area_destino_ciencias)"
                            class="w-full" />
                        <small v-if="errors.area_destino_ciencias" class="p-error">{{ errors.area_destino_ciencias
                            }}</small>
                    </FloatLabel>
                </div>

                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <label for="area_destino_cs_sociales">Área Destino Ciencias Sociales</label>
                        <Dropdown name="area_destino_cs_sociales" id="area_destino_cs_sociales" type="text"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            v-model="oAcademicaEtapa1Pregrado.area_destino_cs_sociales"
                            :class="{ 'p-invalid': errors.area_destino_cs_sociales }"
                            @update:modelValue="setFieldValue('area_destino_cs_sociales', oAcademicaEtapa1Pregrado.area_destino_cs_sociales)"
                            class="w-full" />
                        <small v-if="errors.area_destino_cs_sociales" class="p-error">{{
                            errors.area_destino_cs_sociales
                            }}</small>
                    </FloatLabel>
                </div>

                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <label for="area_destino_educacion">Área Destino Educación</label>
                        <Dropdown name="area_destino_educacion" id="area_destino_educacion" type="text"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            v-model="oAcademicaEtapa1Pregrado.area_destino_educacion"
                            :class="{ 'p-invalid': errors.area_destino_educacion }"
                            @update:modelValue="setFieldValue('area_destino_educacion', oAcademicaEtapa1Pregrado.area_destino_educacion)"
                            class="w-full" />
                        <small v-if="errors.area_destino_educacion" class="p-error">{{ errors.area_destino_educacion
                            }}</small>
                    </FloatLabel>
                </div>

                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <label for="area_destino_humanidades">Área Destino Humanidades</label>
                        <Dropdown name="area_destino_humanidades" id="area_destino_humanidades" type="text"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            v-model="oAcademicaEtapa1Pregrado.area_destino_humanidades"
                            :class="{ 'p-invalid': errors.area_destino_humanidades }"
                            @update:modelValue="setFieldValue('area_destino_humanidades', oAcademicaEtapa1Pregrado.area_destino_humanidades)"
                            class="w-full" />
                        <small v-if="errors.area_destino_humanidades" class="p-error">{{
                            errors.area_destino_humanidades
                            }}</small>
                    </FloatLabel>
                </div>

                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <label for="area_destino_ingenieria">Área Destino Ingeniería</label>
                        <Dropdown name="area_destino_ingenieria" id="area_destino_ingenieria" type="text"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            v-model="oAcademicaEtapa1Pregrado.area_destino_ingenieria"
                            :class="{ 'p-invalid': errors.area_destino_ingenieria }"
                            @update:modelValue="setFieldValue('area_destino_ingenieria', oAcademicaEtapa1Pregrado.area_destino_ingenieria)"
                            class="w-full" />
                        <small v-if="errors.area_destino_ingenieria" class="p-error">{{
                            errors.area_destino_ingenieria
                            }}</small>
                    </FloatLabel>
                </div>

                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <label for="area_destino_salud">Área Destino Salud</label>
                        <Dropdown name="area_destino_salud" id="area_destino_salud" type="text"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            v-model="oAcademicaEtapa1Pregrado.area_destino_salud"
                            :class="{ 'p-invalid': errors.area_destino_salud }"
                            @update:modelValue="setFieldValue('area_destino_salud', oAcademicaEtapa1Pregrado.area_destino_salud)"
                            class="w-full" />
                        <small v-if="errors.area_destino_salud" class="p-error">{{ errors.area_destino_salud
                            }}</small>
                    </FloatLabel>
                </div>

                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <label for="area_destino_servicios">Área Destino Servicios</label>
                        <Dropdown name="area_destino_servicios" id="area_destino_servicios" type="text"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            v-model="oAcademicaEtapa1Pregrado.area_destino_servicios"
                            :class="{ 'p-invalid': errors.area_destino_servicios }"
                            @update:modelValue="setFieldValue('area_destino_servicios', oAcademicaEtapa1Pregrado.area_destino_servicios)"
                            class="w-full" />
                        <small v-if="errors.area_destino_servicios" class="p-error">{{ errors.area_destino_servicios
                            }}</small>
                    </FloatLabel>
                </div>

                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <label for="malla_curricular">Malla Curricular</label>
                        <InputText name="malla_curricular" id="malla_curricular" type="text"
                            v-model="oAcademicaEtapa1Pregrado.malla_curricular"
                            :class="{ 'p-invalid': errors.malla_curricular }"
                            @input="setFieldValue('malla_curricular', oAcademicaEtapa1Pregrado.malla_curricular)"
                            class="w-full" />
                        <small v-if="errors.malla_curricular" class="p-error">{{ errors.malla_curricular }}</small>
                    </FloatLabel>
                </div>

                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <label for="mail_difusion_carrera">Mail Difusión Carrera</label>
                        <InputText name="mail_difusion_carrera" id="mail_difusion_carrera" type="text"
                            v-model="oAcademicaEtapa1Pregrado.mail_difusion_carrera"
                            :class="{ 'p-invalid': errors.mail_difusion_carrera }"
                            @input="setFieldValue('mail_difusion_carrera', oAcademicaEtapa1Pregrado.mail_difusion_carrera)"
                            class="w-full" />
                        <small v-if="errors.mail_difusion_carrera" class="p-error">{{ errors.mail_difusion_carrera
                            }}</small>
                    </FloatLabel>
                </div>

            </div>
            <div class="flex justify-content-end gap-2">
                <ProgressSpinner aria-label="Loading" v-if="oAcademicaEtapa1PregradoMutation.isPending.value" style="width: 38px; height: 38px;" strokeWidth="7" />
                <Button type="button" label="Cancelar" severity="secondary" @click="emit('closeDialog')" v-if="!oAcademicaEtapa1PregradoMutation.isPending.value"></Button>
                <Button label="submit" type="submit" v-if="!oAcademicaEtapa1PregradoMutation.isPending.value">GUARDAR</Button>
            </div>
        </VeeForm>

    </div>
</template>



<style scoped></style>