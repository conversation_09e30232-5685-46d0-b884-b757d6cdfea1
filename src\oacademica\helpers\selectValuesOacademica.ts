

export const area_destinoOPtions = [
  { name: "No existe beneficio", value: 0 },
  { name: "Sí existe beneficio", value: 1 },
];
export const modalidadOPtions = [
  { name: "Presencial", value: 1 },
  { name: "Semipresencial", value: 2 },
  { name: "No presencial", value: 3 },
];
export const codJornadaOPtions = [
  { name: "<PERSON><PERSON><PERSON>", value: 1 },
  { name: "Vespertina", value: 2 },
  { name: "No Semi Presencial (B-Learning)", value: 3 },
  { name: "A Distancia (E-Learning)", value: 4 },
  { name: "Otra", value: 5 },
];
export const codTipoPlanCarreraOPtions = [
  { name: "Plan Regular", value: 1 },
  { name: "Plan Especial", value: 2 },
  { name: "Plan Regular de Continuidad", value: 3 },
];
export const regimenOptions = [
  { name: "Semestres", value: 1 },
  { name: "Trimestres", value: 2 },
  { name: "<PERSON><PERSON><PERSON>", value: 3 },
  { name: "Bimestres", value: 4 },
];
export const codNivelGlobaloptions = [
  { name: "Pregrado", value: 1 },
  { name: "Posgrado", value: 2 },
  { name: "Postítulo", value: 3 },
];

export const codNivelCarreraOptions = [
  { name: "Bachillerato, Ciclo inicial o Plan Común", value: 0 },
  { name: "Técnica de Nivel Superior", value: 1 },
  { name: "Profesional sin Licenciatura", value: 2 },
  { name: "Licenciatura no conducente a título", value: 3 },
  { name: "Profesional con Licenciatura", value: 4 },
  { name: "Diplomado (desde un semestre)", value: 5 },
  { name: "Postítulo", value: 6 },
  { name: "Especialidad Médica u Odontológica", value: 7 },
  { name: "Magíster", value: 8 },
  { name: "Doctorado", value: 9 },
];

export const acreditacionOptions = [
  { name: "Sí", value: 1 },
  { name: "No", value: 2 },
];
export const elegible_beca_pedagogiaOptions = [
  { name: "No elegible", value: 0 },
  { name: "Programa de Pedagogía Elegible", value: 1 },
  { name: "Programa de Licenciatura Elegible", value: 2 },
  { name: "Programa de Formación Pedagogica Elegible", value: 3 },
];
export const ped_med_odont_otroOptions = [
  { name: "Pedagogía", value: "P" },
  { name: "Medicina", value: "M" },
  { name: "Odontología", value: "D" },
  { name: "Otro", value: "O" },
];

export const requisito_ingresoOptions = [
  { name: "Educación Media", value: 1 },
  { name: "Técnico de Nivel Superior", value: 2 },
  { name: "Bachillerato", value: 3 },
  { name: "Ciclo Básico", value: 4 },
  { name: "Plan Común", value: 5 },
  { name: "Título Profesional", value: 6 },
  { name: "Licenciatura", value: 7 },
  { name: "Especialidad Médica u Odontológica", value: 8 },
  { name: "Postítulo", value: 9 },
  { name: "Magíster", value: 10 },
];
export const area_actualOptions = [
  { name: "Agricultura", value: 1 },
  { name: "Ciencias", value: 2 },
  { name: "Ciencias Sociales, Enseñanza Comercial y Derecho", value: 3 },
  { name: "Educación", value: 4 },
  { name: "Humanidades y Artes", value: 5 },
  { name: "Ingeniería, Industria y Construcción", value: 6 },
  { name: "Salud y Servicios Sociales", value: 7 },
  { name: "Servicios", value: 8 },
];

export const formatoValorOptions = [
  { name: "Peso Chileno", value: 1 },
  { name: "UF", value: 2 },
];