<template>
    <!-- Loading -->
    <div v-if="globalLoading">
        <Loading /> <!-- or any loading indicator component -->
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading">
            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <div class='surface-card shadow-2 p-3 border-round'>

                    <div class="flex justify-content-between mb-3">
                        <div>
                            <h2>Bienvenido</h2>
                        </div>
                        <div class="flex align-items-center justify-content-center bg-blue-100 border-round"
                            style="width:2.5rem;height:2.5rem">
                            <i class="pi pi-users text-blue-500 text-xl"></i>
                        </div>
                    </div>
                    <p>{{ userName }}</p>
                    <!-- <p>{{ userEmail }}</p>
                    <p v-if="isUserAuthenticated">userIsAuthenticated</p>
                    <p v-else="isUserAuthenticated">Not Authenticated</p>
                    <p>usuario autorizado: {{ isUserAuthenticated }}</p>
                    <p>Permisos del usuario: {{ permissions }}</p>
                    <p>Permisos del usuario LISTA: {{ permissionsList }}</p>-->
                </div>
            </div>
        </div>

        <!-- Cards -->
        <div v-if="!isLoading">

            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <h2>Indicadores</h2>
                <div class="grid">
                    <div class="col-12 md:col-6 lg:col-3">
                        <div class='surface-card shadow-2 p-3 border-round'>
                            <div class="flex justify-content-between mb-3">
                                <div>
                                    <span class="block font-medium mb-3">Matrícula</span>
                                    <div class="font-medium text-xl">7.868</div>
                                </div>
                                <div class="flex align-items-center justify-content-center bg-blue-100 border-round"
                                    style="width:2.5rem;height:2.5rem">
                                    <i class="pi pi-users text-blue-500 text-xl"></i>
                                </div>
                            </div>
                            <span class="text-orange-500 font-medium">1.500 nuevos </span>
                            <span class="">matriculados</span>
                        </div>
                    </div>
                    <div class="col-12 md:col-6 lg:col-3">
                        <div class='surface-card shadow-2 p-3 border-round'>
                            <div class="flex justify-content-between mb-3">
                                <div>
                                    <span class="block font-medium mb-3">Titulados</span>
                                    <div class="font-medium text-xl">2.100</div>
                                </div>
                                <div class="flex align-items-center justify-content-center bg-orange-100 border-round"
                                    style="width:2.5rem;height:2.5rem">
                                    <i class="pi pi-users text-orange-500 text-xl"></i>
                                </div>
                            </div>
                            <span class="text-orange-500 font-medium">%25</span>
                            <span class=""> más desde el año pasado</span>
                        </div>
                    </div>
                    <div class="col-12 md:col-6 lg:col-3">
                        <div class='surface-card shadow-2 p-3 border-round'>
                            <div class="flex justify-content-between mb-3">
                                <div>
                                    <span class="block font-medium mb-3">Oferta Académica</span>
                                    <div class="font-medium text-xl">230</div>
                                </div>
                                <div class="flex align-items-center justify-content-center bg-blue-100 border-round"
                                    style="width:2.5rem;height:2.5rem">
                                    <i class="pi pi-inbox text-blue-500 text-xl"></i>
                                </div>
                            </div>
                            <span class="text-orange-500 font-medium">52</span>
                            <span class=""> nuevos inscritos</span>
                        </div>
                    </div>
                    <div class="col-12 md:col-6 lg:col-3">
                        <div class='surface-card shadow-2 p-3 border-round'>
                            <div class="flex justify-content-between mb-3">
                                <div>
                                    <span class="block font-medium mb-3">Postgrados</span>
                                    <div class="font-medium text-xl">38</div>
                                </div>
                                <div class="flex align-items-center justify-content-center bg-orange-100 border-round"
                                    style="width:2.5rem;height:2.5rem">
                                    <i class="pi pi-comment text-orange-500 text-xl"></i>
                                </div>
                            </div>
                            <span class="text-orange-500 font-medium">12 </span>
                            <span class="">nuevos en 2024</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Power BI Chart -->
        <div v-if="!isLoading">
            <div class='charts px-1 py-1 md:px-6 lg:px-8'>
                <h2></h2>
                <div :class="['iframe-container', theme]">
                    <iframe title="Pregrado - Gratuidad_Nueva_Versión"
                        src="https://app.powerbi.com/view?r=eyJrIjoiZjRmOGI1ZDUtNjE2Mi00MzQ5LWI1ZmYtMTAwNWRiMWQ1NGZiIiwidCI6IjlkMzA3YWIyLWUzMzItNDNjMy05M2FhLTNlNzI0OGZmZTc3ZCIsImMiOjR9"
                        frameborder="0" allowFullScreen="true"></iframe>
                </div>
            </div>
        </div>
    </div>
    <Toast position="bottom-left" group="bl" />

</template>

<script>
import { useAuthStore } from '../../store/auth';
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import { useToast } from "primevue/usetoast";

export default {

    name: "Dashboard",

    setup() {
        const authStore = useAuthStore();
        const toast = useToast();

        // Obtain the stored user data from pinia and the global loading state
        const userName = computed(() => decryptData(authStore.userName));
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const permissions = computed(() => decryptData(authStore.permissions));
        const permissionsList = computed(() => decryptData(authStore.permissionsList));
        const isUserAuthenticated = computed(() => decryptData(authStore.userIsAuthenticated));
        const globalLoading = computed(() => decryptData(authStore.isLoading));

        // Theme for changing the actual theme from dark to light or viceversa
        const theme = ref("dark");

        // Local "Loading" for the current view
        const isLoading = ref(false);

        // Return data object
        return {
            theme,
            authStore,
            userName,
            userEmail,
            permissions,
            permissionsList,
            isUserAuthenticated,
            isLoading,
            globalLoading
        };
    }
}
</script>

<style>
.iframe-container {
    position: relative;
    width: 100%;
    padding-top: 56.25%;
    /* 16:9 Aspect Ratio */
}

.iframe-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}

/* Theme Classes */
.light-theme {
    --border-color: #ccc;
    background-color: #fff;
}

.dark-theme {
    --border-color: #444;
    background-color: #333;
}
</style>