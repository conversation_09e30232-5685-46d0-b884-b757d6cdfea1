<template>
    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div
            v-if="!isLoading && (hasAccess('Encargado', 'Mu-pregrado') || hasAccess('Usuario General', 'Mu-pregrado') || hasAccess('Administrador Sistema', 'Administracion PIU'))">
            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <form @submit.prevent="submitForm">
                    <div class='surface-card shadow-3 p-3 border-round'>
                        <div
                            style="display: flex;justify-content: space-between;align-items: center;padding-left: 0.1rem;">
                            <h2>Matrícula Unificada Pregrado</h2>
                            <Button
                                v-if="hasAccess('Encargado', 'Mu-pregrado') || hasAccess('Usuario General', 'Mu-pregrado') || hasAccess('Administrador Sistema', 'Administracion PIU')"
                                @click="dialogVisibleCrear = true">Nuevo</Button>
                        </div>

                        <DataTable :value="mu_pregrado_data" stripedRows showGridlines paginator :rows="10"
                            sort-field="anio_proceso" :sort-order=1 tableStyle="min-width: 50rem">
                            <Column field="creador_email" header="Autor" class="center-header"></Column>
                            <Column field="anio_proceso" header="Proceso" class="center-header"></Column>
                            <Column field="etapa_actual" header="Etapa Actual" class="center-header"></Column>
                            <Column field="estudiante_count" header="N° Registros" class="center-header"></Column>

                            <Column field="is_finalized" header="Estado" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.is_finalized == null || slotProps.data.is_finalized == false"
                                        v-tooltip.bottom="pending_text">
                                        <Button disabled icon="pi pi-hourglass"
                                            style="justify-content: center;color: orangered;" class="p-button-text" />
                                    </div>
                                    <div v-else v-tooltip.bottom="finalized_text">
                                        <Button disabled icon="pi pi-verified"
                                            style="justify-content: center;color: green"
                                            class="p-button-text green-icon" />
                                    </div>
                                </template>
                            </Column>

                            <Column field="Fechas" header="Fechas Etapas" class="center-header" style="width: 15%;">

                                <template #body="slotProps">
                                    <div v-if="hasAccess('Encargado', 'Mu-pregrado') || hasAccess('Usuario General', 'Mu-pregrado') || hasAccess('Administrador Sistema', 'Administracion PIU')"
                                        style="display: flex; justify-content: center; align-items: center;">
                                        <Button icon="pi pi-calendar" style="justify-content: center;"
                                            class="p-button-text"
                                            @click="goToDatesMU(slotProps.data.anio_proceso, slotProps.data.etapa_actual, slotProps.data.is_finalized)" />
                                        <div v-if="slotProps.data.fecha_etapa_count == 5"
                                            v-tooltip.bottom="'Fechas definidas'">
                                            <Button icon="pi pi-check" class="p-button-text" style="color: green;"
                                                disabled></Button>
                                        </div>
                                        <div v-else v-tooltip.bottom="'Fechas no definidas'">
                                            <Button icon="pi pi-times" class="p-button-text" style="color: red;"
                                                disabled></Button>
                                        </div>
                                    </div>
                                </template>
                            </Column>
                            <Column field="ver" header="Ver" class="center-header">

                                <template #body="slotProps">
                                    <Button
                                        v-if="slotProps.data.fecha_etapa_count === 5 && (hasAccess('Encargado', 'Mu-pregrado') || hasAccess('Usuario General', 'Mu-pregrado') || hasAccess('Administrador Sistema', 'Administracion PIU'))"
                                        icon="pi pi-eye" style="justify-content: center;" class="p-button-text"
                                        @click="goToDetails(slotProps.data.anio_proceso)" />
                                </template>
                            </Column>
                            <Column
                                v-if="hasAccess('Encargado', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU')"
                                field="eliminar" header="Eliminar" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.is_finalized != true && (hasAccess('Encargado', 'Mu-pregrado') || hasAccess('Administrador Sistema', 'Administracion PIU'))">
                                        <Button icon="pi pi-trash" class="p-button-text"
                                            @click="dialogVisibleEliminar = true, selectedYear = slotProps.data.anio_proceso" />
                                    </div>
                                </template>
                            </Column>
                            <Column
                                v-if="hasAccess('Encargado', 'Mu-pregrado') || hasAccess('Usuario General', 'Mu-pregrado') || hasAccess('Administrador Sistema', 'Administracion PIU')"
                                field="Exportar" header="Exportar" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.is_finalized == true">
                                        <Button icon="pi pi-download" style="" class="p-button-text"
                                            @click="obtenerMuPregradoSeleccionado(slotProps.data.anio_proceso, slotProps.data.fecha_etapa_count)" />
                                    </div>
                                </template>
                            </Column>
                        </DataTable>

                    </div>
                </form>
            </div>

        </div>
        <div v-else class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
                <Loading />
            </div>
        </div>
        <Toast position="bottom-left" group="bl" />
    </div>
    <Dialog v-model:visible="dialogVisibleCrear" modal header="Pregrado" :style="{ width: '30rem' }"
        v-on:hide="resetForm()">
        <div v-if="dialogIsLoading && !muPregradoisCorrect">
            <Loading />
        </div>

        <!-- Form to create a new report -->
        <div v-if="!dialogIsLoading && !muPregradoisCorrect">
            <span class="p-text-secondary block mb-5">Crear nuevo registro de Matrícula Unificada</span>

            <!-- Year input with validation -->
            <div class="flex align-items-center gap-3 mb-3" style="justify-content: space-between;">
                <div>
                    <label for="anio" class="font-semibold w-6rem">Año Proceso </label>
                    <i class="pi pi-info-circle" style="justify-self: center" v-tooltip.bottom="tooltipText" />
                </div>
                <div>
                    <InputNumber v-model="inputYear" inputId="anio" :useGrouping="false" style="width: 100%;"
                        :min="1950" :max="2100" :class="{ 'p-invalid': inputYearIsInvalid }" />
                </div>

            </div>
            <!-- Validation error message -->
            <div style="text-align: center;" v-if="errorMessageYearIsInvalid" class="p-error block mb-3">{{
                errorMessageYearIsInvalid }}
            </div>
            <h4>Etapa 1 :</h4>
            <div>
                <div class="flex align-items-center gap-3 mb-3" style="justify-content: space-between;">
                    <div>
                        <label for="fechaInicio" class="font-semibold w-6rem">Fecha Inicio </label>
                        <i class="pi pi-info-circle" style="justify-content: center" v-tooltip.bottom="tooltipText" />
                    </div>
                    <Calendar v-if="inputYear" v-model="fecha_inicio_1" dateFormat="dd/mm/yy" style="width: 58%;"
                        :disabled="!inputYear" :minDate="minDateFechaInicio" :maxDate="maxDateFechaInicio"
                        :invalid="fecha_inicio_1_isInvalid" />
                    <Calendar v-else dateFormat="dd/mm/yy" style="width: 58%;" :disabled="!inputYear" />


                </div>
                <div style="text-align: center;" v-if="errorMsg_FechaInicio" class="p-error block mb-3">{{
                    errorMsg_FechaInicio }}
                </div>
                <div class="flex align-items-center gap-3 mb-3" style="justify-content: space-between;">
                    <div>
                        <label for="fechaTermino" class="font-semibold w-6rem">Fecha Término </label>
                        <i class="pi pi-info-circle" style="justify-content: center" v-tooltip.bottom="tooltipText" />
                    </div>
                    <Calendar v-if="inputYear" v-model="fecha_termino_1" dateFormat="dd/mm/yy" style="width: 58%;"
                        :disabled="!inputYear" :minDate="minDateFechaTermino" :maxDate="maxDateFechaTermino"
                        :invalid="fecha_termino_1_isInvalid" />
                    <Calendar v-else dateFormat="dd/mm/yy" style="width: 58%;" :disabled="!inputYear" />

                </div>
            </div>
            <div style="text-align: center;" v-if="errorMsg_FechaTermino" class="p-error block mb-3">{{
                errorMsg_FechaTermino }}
            </div>

            <!-- Buttons -->
            <div class="flex justify-content-end gap-2">
                <Button type="button" label="Cancelar" severity="secondary"
                    @click="dialogVisibleCrear = false"></Button>
                <Button type="button" label="Crear" @click="crearMuPregrado"></Button>
            </div>
        </div>

        <!-- Success message -->
        <div v-if="!dialogIsLoading && muPregradoisCorrect"
            style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
            <i class="pi pi-check" style="font-size: 5rem; color: blue;"></i>
            <span class="p-text-secondary block mb-5">¡Creación exitosa!</span>
        </div>
    </Dialog>
    <Dialog v-model:visible="dialogVisibleEliminar" modal header="Eliminar Registro" :style="{ width: '25rem' }"
        v-on:hide="resetForm()">
        <div v-if="dialogIsLoading && !muPregradoisCorrect">
            <Loading />
        </div>

        <div v-if="!dialogIsLoading && !muPregradoisCorrect">
            <div>
                <!-- Validation error message -->
                <p>Esto eliminará permanentemente este registro ¿Está seguro?</p>
            </div>
            <br />
            <div class="flex justify-content-center gap-2">
                <InputSwitch style="scale: 1.5;" v-model="deletePreConfirmation"></InputSwitch>
            </div>
            <br />
            <!-- Validation error message -->
            <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}<br /></div>


            <!-- Buttons -->
            <div class="flex justify-content-end gap-2">
                <Button type="button" label="Cancelar" severity="secondary"
                    @click="dialogVisibleEliminar = false"></Button>
                <Button v-if="deletePreConfirmation" type="button" label="Eliminar"
                    @click="eliminarMUPregrado"></Button>
            </div>
        </div>

        <!-- Success message -->
        <div v-if="!dialogIsLoading && muPregradoisCorrect"
            style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
            <i class="pi pi-trash" style="font-size: 5rem; color: blue;"></i>
            <br />
            <span class="p-text-secondary block mb-5">¡Eliminado correctamente!</span>
        </div>

    </Dialog>

</template>

<script>
import { useAuthStore } from '../../../store/auth';
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import { useToast } from "primevue/usetoast";
import axios from "axios";
import { useRouter, useRoute } from 'vue-router';

export default {

    name: "pp_Matricula_unificada_postgrado_postitulo",

    setup() {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const router = useRouter();
        const route = useRoute();
        const toast = useToast();
        const authStore = useAuthStore();
        const permissionsList = computed(() => decryptData(authStore.permissionsList));
        const globalLoading = computed(() => decryptData(authStore.isLoading));
        const userToken = computed(() => decryptData(authStore.idToken));
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const isLoading = ref(false);
        const mu_pregrado_data = ref([]);
        const dialogConfirmarMU_post = ref(false);
        const selectedYear = ref(null);
        const dialogVisibleCrear = ref(false);
        const dialogVisibleEliminar = ref(false);
        const inputYear = ref(null);
        const inputYearIsInvalid = ref(false);
        const errorMessage = ref('');
        const errorMessageYearIsInvalid = ref('');
        const errorMsg_FechaInicio = ref('');
        const errorMsg_FechaTermino = ref('');
        const muPregradoisCorrect = ref(false);
        const muPregradoAnioSelecionado = ref(false);
        const deletePreConfirmation = ref(false);
        const dialogIsLoading = ref(false);
        const validatePreConfirmation = ref(false);
        const dialogConfirmarMU = ref(false);
        const finalized_text = ref("Finalizado");
        const pending_text = ref("Pendiente");
        const fecha_inicio_1_isInvalid = ref(false);
        const fecha_inicio_1 = ref();
        const fecha_termino_1 = ref();
        const fecha_termino_1_isInvalid = ref(false);
        const minDateFechaInicio = computed(() => new Date(inputYear.value - 1, 0, 1));
        const maxDateFechaInicio = computed(() => new Date(inputYear.value + 1, 11, 31));
        const minDateFechaTermino = computed(() => new Date(inputYear.value - 1, 0, 1));
        const maxDateFechaTermino = computed(() => new Date(inputYear.value + 1, 11, 31));


        const hasAccess = (allowedRol, requiredRecurso) => {
            const access = permissionsList.value.some(user => user.rol === allowedRol && user.recurso === requiredRecurso);
            if (access) {
                return true;
            } else {
                return false;
            }

        };

        onMounted(async () => {
            isLoading.value = true;
            await getMUPregradoData();
            //await getAllMuPregradoFecha();
            console.log(mu_pregrado_data.value)
            isLoading.value = false;
        });

        // Function to reset the form for a new creation
        const resetForm = () => {
            inputYear.value = null; // Reset the year input
            errorMessage.value = ''; // Clear any error messages
            muPregradoisCorrect.value = false; // Reset success state
            dialogVisibleCrear.value = false; // Close the dialog
            deletePreConfirmation.value = false;
            dialogVisibleEliminar.value = false;
            validatePreConfirmation.value = false;
            dialogConfirmarMU.value = false;
        }
        const goToDetails = (anio, etapa) => {
            const encrypted_year = encryptData(anio);
            router.push({ name: 'PD-MU-pregrado/', params: { anio_proceso: encrypted_year } });
        };
        const goToDatesMU = (anio, etapa, isFinalized) => {
            const encrypted_year = encryptData(anio);
            const encrypted_etapa = encryptData(etapa);
            const encrypted_isFinalized = encryptData(isFinalized);
            router.push({ name: 'MU-pregrado-fechas/anio_proceso', params: { anio_proceso: encrypted_year, etapa_actual: encrypted_etapa, is_finalized: encrypted_isFinalized } });
        };
        const crearMuPregrado = async () => {
            // Clear previous error messages
            errorMessage.value = '';
            inputYearIsInvalid.value = false;
            errorMessageYearIsInvalid.value = '';
            errorMsg_FechaInicio.value = '';
            fecha_inicio_1_isInvalid.value = false;
            fecha_termino_1_isInvalid.value = false;
            errorMsg_FechaTermino.value = '';
            // Validate the year input
            if (inputYear.value === null) {
                errorMessageYearIsInvalid.value = 'El valor ingresado no es valido';
                inputYearIsInvalid.value = true;
                return;
            }
            let hasError = false;

            if (inputYear.value === null) {
                errorMessageYearIsInvalid.value = 'El valor ingresado no es valido';
                inputYearIsInvalid.value = true;
                hasError = true;
            }

            if (!fecha_inicio_1.value) {
                errorMsg_FechaInicio.value = 'La fecha de inicio es obligatoria';
                fecha_inicio_1_isInvalid.value = true;
                hasError = true;
            }

            if (!fecha_termino_1.value) {
                errorMsg_FechaTermino.value = 'La fecha de término es obligatoria';
                fecha_termino_1_isInvalid.value = true;
                hasError = true;
            }
            // Validate that fecha_inicio_1 is not greater than fecha_termino_1
            if (fecha_inicio_1.value && fecha_termino_1.value && fecha_inicio_1.value > fecha_termino_1.value) {
                errorMsg_FechaInicio.value = 'La fecha de inicio no puede ser mayor que la fecha de término';
                fecha_inicio_1_isInvalid.value = true;
                fecha_termino_1_isInvalid.value = true;
                hasError = true;
            }

            if (hasError) {
                return;
            }

            dialogIsLoading.value = true;
            await getMUPregradoData();

            // Check if `anio_proceso` matches `inputYear.value` in any item of `infraestructuraRecursoData.value`
            const isDuplicate = mu_pregrado_data.value.some(item => item.anio_proceso === inputYear.value);

            if (isDuplicate) {
                // If a match is found, throw the error
                errorMessageYearIsInvalid.value = 'El valor ingresado ya existe';
                inputYearIsInvalid.value = true;
                dialogIsLoading.value = false; // Stop loading since an error occurred
                return; // Prevent further execution
            } else {
                const response = await postMuPregrado();
                dialogIsLoading.value = false;
                if (response == "ok") {
                    muPregradoisCorrect.value = true
                    // If no match is found, proceed with saving the report
                    console.log('Saving report for year:', inputYear.value);
                    await getMUPregradoData();
                }
            }

        }

        const postMuPregrado = async () => {
            // Define the request body
            const requestBody = {
                anio_proceso: inputYear.value,
                fecha_inicio: fecha_inicio_1.value,
                fecha_termino: fecha_termino_1.value
            };
            try {
                dialogIsLoading.value = true;
                const response = await axios.post(API_BASE_URL + "mu_pregrado", requestBody, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                return "ok";
            } catch (error) {
                console.error("Error: ", error);
            } finally {
                dialogIsLoading.value = false;
            }
        };

        const getMUPregradoData = async () => {
            try {
                isLoading.value = true;

                const response = await axios.get(API_BASE_URL + "mu_pregrado", {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });

                if (response.status == 200) {
                    mu_pregrado_data.value = response.data;
                }
            } catch (error) {
                console.error("Error: ", error);
            } finally {
                isLoading.value = false;
            }
        };
        const getAllMuPregradoFecha = async () => {
            try {
                isLoading.value = true;

                const response = await axios.get(`${API_BASE_URL}mu_pregrado_fecha`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    },
                });
                console.log(response.data);
                /*if (response.status == 200) {
                    mu_pregrado_fecha_data.value = response.data;

                    if (mu_pregrado_fecha_data.value.length < 5) {
                        const currentLength = mu_pregrado_fecha_data.value.length;
                        for (let i = currentLength; i < 5; i++) {
                            mu_pregrado_fecha_data.value.push({
                                etapa_proceso: i + 1,
                                fecha_inicio: "Sin definir",
                                fecha_termino: "Sin definir"
                            });
                        }
                    }
                    mu_pregrado_fecha_data.value = mu_pregrado_fecha_data.value.map(item => {
                        return {
                            ...item,
                            estado_etapa: item.etapa_proceso < mu_pregrado_data_actual.value.etapa_actual ? true : false
                        };
                    });
                } else if (response.status == 204) {
                    mu_pregrado_fecha_data.value = [];
                } else {
                    toast.add({
                        severity: 'error',
                        summary: 'Error',
                        detail: 'Error al cargar los datos de la tabla, intentelo nuevamente.',
                        group: "bl",
                        life: 3000
                    });
                    console.error("Error: ", response);
                }*/
            } catch (error) {
                toast.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Error al cargar los datos de la tabla, intentelo nuevamente.',
                    group: "bl",
                    life: 3000
                });
                console.error("Error: ", error);
            } finally {
                isLoading.value = false;
            }
        }

        const eliminarMUPregrado = async () => {
            errorMessage.value = '';

            const eliminarResponse = await eliminarMU();

            if (eliminarResponse == "ok") {
                muPregradoisCorrect.value = true;
                console.log('Deleting report for year:', inputYear.value);
                await getMUPregradoData();

            } else if (eliminarResponse == "conflicto") {
                errorMessage.value = 'El elemento no debe tener registros asociados a él';
            } else if (eliminarResponse == "error") {
                errorMessage.value = 'Se detectó un error, inténtelo más tarde';
            }
        }

        const eliminarMU = async () => {
            errorMessage.value = '';

            try {
                dialogIsLoading.value = true;
                const response = await axios.delete(API_BASE_URL + `mu_pregrado/${selectedYear.value}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                return "ok";
            } catch (error) {
                console.error("Error: ", error);
                if (error.status == 409) {
                    return "conflicto";
                } else {
                    if (error.status === 403) {
                        toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El registro no puede ser modificado', life: 5000 });
                    }
                    return "error";
                }
            } finally {
                dialogIsLoading.value = false;
            }

        }

        async function obtenerMuPregradoSeleccionado(anioProceso,etapa_proceso) {
            try {
                const response = await axios.get(API_BASE_URL + `mu_pregrado/${anioProceso}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                muPregradoAnioSelecionado.value = await response.data;
                if (muPregradoAnioSelecionado.value.is_finalized == true) {
                    console.log(muPregradoAnioSelecionado.value);
                    exportarDatosCSV(anioProceso,etapa_proceso);
                } else {
                    toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El registro no puede ser modificado', life: 5000 });
                }

            } catch (error) {
                console.error("Error fetching data:", error);
            }
        }

        const exportarDatosCSV = async (anio_proceso, etapa) => {
            try {
                const response = await axios.get(
                    `${API_BASE_URL}mu_pregrado/exportCSV/${anio_proceso}/${etapa}`,
                    {
                        headers: {
                            Authorization: `Bearer ${userToken.value}`,
                        },
                        responseType: 'blob', // IMPORTANT: receive as blob
                    }
                );

                // Now response.data is the Blob
                const blob = response.data;

                // Trigger download
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `matricula_unificada_${anio_proceso}_etapa_${etapa}_PIU.csv`;
                a.style.display = 'none';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                toast.add({ severity: 'success', summary: 'Éxito', group: 'bl', detail: 'Datos exportados correctamente', life: 5000 });


            }
            catch (error) {
                if (error.status === 403) {
                    toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El registro no puede ser modificado', life: 5000 });
                }
                console.error("Error fetching data:", error);
            }
        }


        return {
            globalLoading,
            isLoading,
            hasAccess,
            mu_pregrado_data,
            dialogConfirmarMU_post,
            selectedYear,
            dialogVisibleCrear,
            dialogVisibleEliminar,
            resetForm,
            inputYear,
            errorMessage,
            errorMessageYearIsInvalid,
            errorMsg_FechaInicio,
            errorMsg_FechaTermino,
            muPregradoisCorrect,
            deletePreConfirmation,
            dialogIsLoading,
            validatePreConfirmation,
            dialogConfirmarMU,
            crearMuPregrado,
            finalized_text,
            pending_text,
            eliminarMUPregrado,
            obtenerMuPregradoSeleccionado,
            muPregradoAnioSelecionado,
            goToDetails,
            goToDatesMU,
            fecha_inicio_1,
            fecha_termino_1,
            minDateFechaInicio,
            maxDateFechaInicio,
            minDateFechaTermino,
            maxDateFechaTermino,
            inputYearIsInvalid,
            fecha_inicio_1_isInvalid,
            fecha_termino_1_isInvalid,
            getAllMuPregradoFecha,
            exportarDatosCSV


        };

    }
}
</script>

<style scoped>
:deep() .center-header {
    text-align: center !important;
    align-content: center !important;
}

:deep() .p-datatable .p-column-header-content {
    display: block !important;

}

.yellow-icon .pi {
    color: yellow;
}

.green-icon .pi {
    color: greenyellow;
}
</style>