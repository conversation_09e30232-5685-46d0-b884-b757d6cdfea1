import { watch } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import { storeToRefs } from 'pinia'
import { useAdmisionStore } from '../../store/admision'

import backEndApi from '@/api/backEndApi'
import type { Admision } from '../interfaces/admision'

const getAdmisiones = async (): Promise<Admision[]> => {
  const { data } = await backEndApi.get<Admision[]>('/api/v1/matricula/admision/')
  return data
}

const useAdmisiones = () => {
  const store = useAdmisionStore()
  const { admisiones, totalRecords } = storeToRefs(store)
  const { isLoading, data, error, isError, isSuccess, status } = useQuery({
    queryKey: ['admisiones'],
    queryFn: getAdmisiones
  })

  watch(
    data,
    (admisiones) => {
      if (admisiones) {
        store.setAdmisiones(admisiones)
      }
    },
    { immediate: true }
  )
  return {
    admisiones,
    isLoading,
    error,
    isError,
    isSuccess,
    status
  }
}

export default useAdmisiones
