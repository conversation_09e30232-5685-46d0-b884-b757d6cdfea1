<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import Panel from 'primevue/panel'
import TabMenu from 'primevue/tabmenu'
import Toolbar from 'primevue/toolbar';
import useOAcademica from '../composables/useOAcademica';
import { useRoute, useRouter } from 'vue-router';
import { useToast } from "primevue/usetoast";
import ProgressSpinner from 'primevue/progressspinner';
import OAcademicaForm from '../components/OAcademicaForm.vue';
import OAcademicaFormEtapa1Pregrado from '../components/OAcademicaDialogEtapa1Pregrado.vue';
import OAcademicaFormEtapa3Pregrado from '../components/OAcademicaDialogEtapa3Pregrado.vue';
import OAcademicaFormEtapa1PosgradoPostitulo from '../components/OAcademicaDialogEtapa1PosgradoPostitulo.vue';
import OAcademicaFormEtapa3PosgradoPostitulo from '../components/OAcademicaDialogEtapa3PosgradoPostitulo.vue';
import { useAuthStore } from '@/store/auth';
import { decryptData } from '@/utils/crypto';
import type { Permiso } from '@/utils/permiso';

interface Props {
  etapa: string,
  nivel_global: string,
}
const authStore = useAuthStore();
const userName = computed(() => decryptData(authStore.userName));
const userEmail = computed(() => decryptData(authStore.userEmail));
const permissions = computed<Permiso[]>(() => decryptData(authStore.permissionsList));
const route = useRoute();
const router = useRouter();
const toast = useToast();
const showDialogModificar = ref(false);
const etapa = route.params.etapa;
//const queryClient = useQueryClient();
const { oAcademica,
  isLoading,
  isError,
  oAcademicaMutation,
  updateOAcademica,
  isUpdating,
  isUpdatingSuccess,
  oAcademicaEtapa1PregradoMutation,
  updateOAcademicaEtapa1Pregrado,
  updateOAcademicaEtapa3Pregrado,
  updateOAcademicaEtapa1PosgradoPostitulo,
  updateOAcademicaEtapa3PosgradoPostitulo,

} = useOAcademica(Array.isArray(route.params.oa_sies_id) ? route.params.oa_sies_id[0] : route.params.oa_sies_id);

watch(isUpdatingSuccess, (value) => {
  if (value) {
    toast.add({severity: 'success', summary: 'Correcto', group: 'bl', detail: 'Oferta Académica actualizada', life: 5000});
    oAcademicaMutation.reset();
  }

});

watch(isError, () => {
  if (isError.value)
    router.replace('/oacademicaView')
});

const back = () => {
  // Implement your back navigation logic here
  router.back();
  console.log('Back button clicked');
};


const items = ref([
  {
    label: 'Carrera',
    icon: 'pi pi-check',
    command: () => scrollToSection('carrera'),
    id: 'carrera',
    index: 0
  },
  {
    label: 'Sede',
    icon: 'pi pi-user',
    command: () => scrollToSection('sede'),
    id: 'sede',
    index: 1
  },

  {
    label: 'SIES',
    icon: 'pi pi-book',
    command: () => scrollToSection('sies'),
    id: 'sies',
    index: 2
  },
  {
    label: 'Duración',
    icon: 'pi pi-book',
    command: () => scrollToSection('duracion'),
    id: 'duracion',
    index: 3
  },
  {
    label: 'Perfil Profesional',
    icon: 'pi pi-book',
    command: () => scrollToSection('perfilprof'),
    id: 'perfilprof',
    index: 4
  },
  {
    label: 'Area',
    icon: 'pi pi-book',
    command: () => scrollToSection('area'),
    id: 'area',
    index: 5
  },
  {
    label: 'Ponderación',
    icon: 'pi pi-book',
    command: () => scrollToSection('ponderacion'),
    id: 'ponderacion',
    index: 6
  },
  {
    label: 'Vacantes',
    icon: 'pi pi-book',
    command: () => scrollToSection('vacantes'),
    id: 'vacantes',
    index: 7
  },

])
const activeItem = ref(0)

const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}
const handleScroll = () => {

  const scrollPanelContent = document.querySelector('.scroll-panel .p-scrollpanel-content');
  const scrollPosition = scrollPanelContent ? scrollPanelContent.scrollTop : 0;

  for (const section of items.value) {
    const sectionElement = document.getElementById(section.id)
    if (sectionElement) {
      const sectionTop = sectionElement.offsetTop
      const sectionHeight = sectionElement.offsetHeight
      if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
        activeItem.value = section.index
        break
      }
    }
  }
}

const closeDialog = () => {
  showDialogModificar.value = false;
};

onMounted(() => {
  console.log('mounted')
  //scrollPanel = document.querySelector('.scroll-panel .p-scrollpanel-content');
  const scrollPanelContent = document.querySelector('.scroll-panel .p-scrollpanel-content');
  if (scrollPanelContent) {
    scrollPanelContent.addEventListener('scroll', handleScroll);
  }
})

// onUnmounted(() => {
//   //const scrollPanel = document.querySelector('.scroll-panel .p-scrollpanel-content');
//   document.querySelector('.scroll-panel .p-scrollpanel-content').removeEventListener('scroll', handleScroll)
// })
</script>

<template>
  <!--Aqui estan los dialogs que se muestran segun la etapa y tipo de carrera ( pregrado, posgrado, postitulo) -->
  <Dialog header="Modificar Datos" v-model:visible="showDialogModificar" :modal="true" :closable="true"
    :style="{ width: '70rem' }">
    <OAcademicaFormEtapa1Pregrado @submitForm="updateOAcademicaEtapa1Pregrado" @closeDialog="closeDialog"
      v-if="oAcademica && permissions.some(user => user.recurso === 'OAPregrado' && Number(etapa) === 1)"
      v-model:showDialog="showDialogModificar" :oAcademicaEtapa1Pregrado="{ ...oAcademica }" />

    <OAcademicaFormEtapa3Pregrado @submitForm="updateOAcademicaEtapa3Pregrado" @closeDialog="closeDialog"
      v-else-if="oAcademica && permissions.some(user => user.recurso === 'OAPregrado' && Number(etapa) === 3)"
      v-model:showDialog="showDialogModificar" :oAcademicaEtapa3Pregrado="{ ...oAcademica }" />

    <OAcademicaFormEtapa1PosgradoPostitulo @submitForm="updateOAcademicaEtapa1PosgradoPostitulo"
      @closeDialog="closeDialog"
      v-else-if="oAcademica && permissions.some(user => user.recurso === 'OAPosgradoPostitulo' && Number(etapa) === 1)"
      v-model:showDialog="showDialogModificar" :oAcademicaEtapa1PosgradoPostitulo="{ ...oAcademica }" />

    <OAcademicaFormEtapa3PosgradoPostitulo @submitForm="updateOAcademicaEtapa3PosgradoPostitulo"
      @closeDialog="closeDialog"
      v-else-if="oAcademica && permissions.some(user => user.recurso === 'OAPosgradoPostitulo' && Number(etapa) === 3)"
      v-model:showDialog="showDialogModificar" :oAcademicaEtapa3PosgradoPostitulo="{ ...oAcademica }" />
  </Dialog>
  <Toolbar class="mb-4">
    <template #center>

    </template>
    <template #start>
      <Button label="Volver" icon="pi pi-arrow-left" @click="back" />
    </template>
    <template #end>

      <Button v-if="oAcademica" label="Modificar" icon="pi pi-upload" severity="help"
        @click="showDialogModificar = true" />
    </template>
  </Toolbar>
  <h3 class="h2 ml-3">Oferta Académica </h3>

  <div class="flex center">

    <Panel>
      <TabMenu v-model:activeIndex="activeItem" :model="items" class="mb-3" />
      <!-- <TabMenu v-model:activeIndex="active" :model="items" class="mb-3" :activeItem="activeItem" /> -->
      <ScrollPanel class="scroll-panel" style="width: 100%; height: 600px" @scroll="handleScroll">
        <ProgressSpinner aria-label="Loading" v-if="isLoading" />
        <div class="content" v-if="oAcademica">
          <OAcademicaForm :oAcademica="oAcademica" @submitForm="updateOAcademica(oAcademica)"
            :is-loading="isUpdating" />
        </div>
      </ScrollPanel>
    </Panel>
  </div>
</template>

<style lang="css" scoped>
.p-field {
  margin-bottom: 20px;
}

.fixed-tabmenu {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  /* Asegúrate de que esté por encima de otros elementos */
}
</style>
