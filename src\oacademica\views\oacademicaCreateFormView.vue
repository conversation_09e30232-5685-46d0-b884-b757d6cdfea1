<script setup>
import {ref, onMounted, onUnmounted, computed, watch} from 'vue'
import Panel from 'primevue/panel'
import TabMenu from 'primevue/tabmenu'
import useOAcademicaCreateMutation from '../composables/useOAcademicaCreateMutation';
import { useRoute, useRouter } from 'vue-router';
import {useToast} from "primevue/usetoast";
import ProgressSpinner from 'primevue/progressspinner';
import OAcademicaForm from '../components/OAcademicaForm.vue';

const route = useRoute();
const router = useRouter();
const toast = useToast();
//const queryClient = useQueryClient();
const { 
  oAcademica,
  oAcademicaCreateMutation,
  isCreateSuccess,
  createOAcademica,
  isLoading
} = useOAcademicaCreateMutation();

watch( isCreateSuccess, (value) => {
  if (value) {
    toast.add({severity: 'success', summary: 'Correcto', group: 'bl', detail: 'Oferta Académica actualizada', life: 5000});
    oAcademicaCreateMutation.reset();
  }

});


const items = ref([
  {
    label: 'Carrera',
    icon: 'pi pi-check',
    command: () => scrollToSection('carrera'),
    id: 'carrera',
    index: 0
  },
  {
    label: 'Sede',
    icon: 'pi pi-user',
    command: () => scrollToSection('sede'),
    id: 'sede',
    index: 1
  },

  {
    label: 'SIES',
    icon: 'pi pi-book',
    command: () => scrollToSection('sies'),
    id: 'sies',
    index: 2
  },
  {
    label: 'Duración',
    icon: 'pi pi-book',
    command: () => scrollToSection('duracion'),
    id: 'duracion',
    index: 3
  },
  {
    label: 'Perfil Profesional',
    icon: 'pi pi-book',
    command: () => scrollToSection('perfilprof'),
    id: 'perfilprof',
    index: 4
  },
  {
    label: 'Area',
    icon: 'pi pi-book',
    command: () => scrollToSection('area'),
    id: 'area',
    index: 5
  },
  {
    label: 'Ponderación',
    icon: 'pi pi-book',
    command: () => scrollToSection('ponderacion'),
    id: 'ponderacion',
    index: 6
  },
  {
    label: 'Vacantes',
    icon: 'pi pi-book',
    command: () => scrollToSection('vacantes'),
    id: 'vacantes',
    index: 7
  },

])
const activeItem = ref(0)

const scrollToSection = (sectionId) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}
const handleScroll = () => {

  const scrollPosition = document.querySelector('.scroll-panel .p-scrollpanel-content').scrollTop

  for (const section of items.value) {
    const sectionElement = document.getElementById(section.id)
    if (sectionElement) {
      const sectionTop = sectionElement.offsetTop
      const sectionHeight = sectionElement.offsetHeight
      if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
        activeItem.value = section.index
        break
      }
    }
  }
}

onMounted(() => {
  console.log('mounted')
  //scrollPanel = document.querySelector('.scroll-panel .p-scrollpanel-content');
  document.querySelector('.scroll-panel .p-scrollpanel-content').addEventListener('scroll', handleScroll)
})

// onUnmounted(() => {
//   //const scrollPanel = document.querySelector('.scroll-panel .p-scrollpanel-content');
//   document.querySelector('.scroll-panel .p-scrollpanel-content').removeEventListener('scroll', handleScroll)
// })
</script>

<template>
  <h3 class="h2 ml-3">Oferta Académica </h3>
  <div class="flex center">
    <Panel>
      <TabMenu v-model:activeIndex="activeItem" :model="items" class="mb-3" />
      <!-- <TabMenu v-model:activeIndex="active" :model="items" class="mb-3" :activeItem="activeItem" /> -->
      <ScrollPanel class="scroll-panel" style="width: 100%; height: 600px" @scroll="handleScroll">
        <ProgressSpinner aria-label="Loading" v-if="isLoading"/>
        <div class="content">
          <OAcademicaForm :oAcademica="oAcademica" @submitForm="createOAcademica(oAcademica)"/>
        
        </div>
      </ScrollPanel>
    </Panel>
  </div>
</template>

<style lang="css" scoped>
.p-field {
  margin-bottom: 20px;
}

.fixed-tabmenu {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  /* Asegúrate de que esté por encima de otros elementos */
}
</style>
