import { encryptData, decryptData } from "@/utils/crypto";
import axios from "axios";
import { useAuthStore } from "@/store/auth";

const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;

export function validateAndDecryptParam(
  to,
  next,
  paramName,
  allowedRange = { min: 1980, max: 2100 }
) {
  let paramValue;
  try {
    // First check if the param exists
    if (!to.params[paramName]) {
      throw new Error(`Missing ${paramName} parameter`);
    }

    // Attempt to decrypt the parameter
    paramValue = decryptData(to.params[paramName]);

    // Check if decryption returned a valid number
    if (isNaN(paramValue)) {
      throw new Error(`Decryption of ${paramName} resulted in invalid number`);
    }
  } catch (error) {
    console.error("Decryption error or invalid param:", error.message);

    // Redirect to error page or home if the decryption/validation fails
    next({ name: "<PERSON>rro<PERSON>" });
    return;
  }

  // Further validation to check if the number is within a valid range
  if (paramValue < allowedRange.min || paramValue > allowedRange.max) {
    next({ name: "Error" });
  } else {
    // If valid, proceed to the route
    next();
  }
}

export async function validateAndDecryptParamID(
  to,
  next,
  paramName,
  tipoInmueble
) {
  let paramValue;

  try {
    // First check if the param exists
    if (!to.params[paramName]) {
      throw new Error(`Missing ${paramName} parameter`);
    }
  } catch (error) {
    console.error("Decryption error or invalid param:", error.message);

    // Redirect to error page or home if the decryption/validation fails
    next({ name: "Error" });
    return;
  }

  try {
    paramValue = to.params[paramName];
    // Query the backend to check if the object exists
    const response = await axios.get(
      API_BASE_URL + `${tipoInmueble}/${paramValue}`
    );

    if (response.status === 200) {
      // If the object exists, proceed to the route
      next();
    } else {
      // If not, redirect to error page
      next({ name: "Error" });
    }
  } catch (error) {
    console.error("API request error:", error.message);
    // Redirect to error page if API call fails
    next({ name: "Error" });
  }
}
/**
 *
 * @param {*} to The route to navigate
 * @param {*} next The next route to navigate
 * @param {*} paramName The id of the model to be validated
 * @param {*} tipoInmueble Name of the route to call the API
 * @returns
 */
export async function validateAndDecryptParamID2(
  to,
  next,
  paramID,
  tipoInmueble
) {
  let paramValue;
  const authStore = useAuthStore();
  const userToken = decryptData(authStore.idToken);

  try {
    // First check if the param exists
    if (!to.params[paramID]) {
      throw new Error(`Missing ${paramID} parameter`);
    }
  } catch (error) {
    console.error("Decryption error or invalid param:", error.message);

    // Redirect to error page or home if the decryption/validation fails
    next({ name: "Error" });
    return;
  }

  try {
    console.log("inside the validateAndDecrypt2");
    console.log(to.params[paramID]);
    paramValue = to.params[paramID];
    // Query the backend to check if the object exists
    const response = await axios.get(
      API_BASE_URL + `${tipoInmueble}/${paramValue}`,
      {
        headers: {
          Authorization: `Bearer ${userToken}`,
        },
      }
    );

    if (response.status === 200) {
      // If the object exists, proceed to the route
      next();
    } else {
      // If not, redirect to error page
      next({ name: "Error" });
    }
  } catch (error) {
    console.error("API request error:", error.message);
    // Redirect to error page if API call fails
    next({ name: "Error" });
  }
}

/**
 *
 * @param {*} to The route to navigate
 * @param {*} next The next route to navigate
 * @param {*} paramAnio The "anio proceso" of the model to be validated
 * @param {*} tipo Name of the route to call the API
 * @returns
 */
export async function validateAndDecryptAnioProceso(
  to,
  next,
  paramAnio,
  tipo
) {
  let paramValue;
  const authStore = useAuthStore();
  const userToken = decryptData(authStore.idToken);

  try {
    // First check if the param exists
    if (!to.params[paramAnio]) {
      throw new Error(`Missing ${paramAnio} parameter`);
    }
  } catch (error) {
    console.error("Decryption error or invalid param:", error.message);

    // Redirect to error page or home if the decryption/validation fails
    next({ name: "Error" });
    return;
  }

  try {
    console.log("inside the validateAndDecrypt2");
    console.log(to.params[paramAnio]);
    paramValue = decryptData(to.params[paramAnio]);
    // Query the backend to check if the object exists
    const response = await axios.get(
      API_BASE_URL + `${tipo}/${paramValue}`,
      {
        headers: {
          Authorization: `Bearer ${userToken}`,
        },
      }
    );

    if (response.status === 200) {
      // If the object exists, proceed to the route
      next();
    } else {
      // If not, redirect to error page
      next({ name: "Error" });
    }
  } catch (error) {
    console.error("API request error:", error.message);
    // Redirect to error page if API call fails
    next({ name: "Error" });
  }
}
