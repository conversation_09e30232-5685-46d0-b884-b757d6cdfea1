<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import Panel from 'primevue/panel'
import TabMenu from 'primevue/tabmenu'
import useAdmision from '../composables/useAdmision';
import { useRoute, useRouter } from 'vue-router';


const route = useRoute();
const { admision,
    isLoading,
    isError,
    clientMutation,
    updateClient,
    isUpdating,
    isUpdatingSuccess
} = useAdmision(route.params.id);


const items = ref([
    {
        label: 'Estudiante',
        icon: 'pi pi-user',
        command: () => scrollToSection('estudiante'),
        id: 'estudiante',
        index: 0
    },
    {
        label: 'IES',
        icon: 'pi pi-user',
        command: () => scrollToSection('ies'),
        id: 'ies',
        index: 1
    },
    {
        label: 'SEDE',
        icon: 'pi pi-user',
        command: () => scrollToSection('sede'),
        id: 'sede',
        index: 2
    },
    {
        label: 'SIES',
        icon: 'pi pi-user',
        command: () => scrollToSection('sies'),
        id: 'sies',
        index: 3
    },
    {
        label: 'Carrera',
        icon: 'pi pi-book',
        command: () => scrollToSection('carrera'),
        id: 'carrera',
        index: 4
    },
    {
        label: 'Matrícula',
        icon: 'pi pi-check',
        command: () => scrollToSection('matricula'),
        id: 'matricula',
        index: 5
    }, {
        label: 'Colegio',
        icon: 'pi pi-book',
        command: () => scrollToSection('colegio'),
        id: 'colegio',
        index: 6
    }, {
        label: 'Inf. Curricular',
        icon: 'pi pi-book',
        command: () => scrollToSection('inf_curricular'),
        id: 'inf_curricular',
        index: 7
    }
    ,
])
const activeItem = ref(0)

const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId)
    if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
    }
}
const handleScroll = () => {

    const scrollPosition = document.querySelector('.scroll-panel .p-scrollpanel-content').scrollTop

    for (const section of items.value) {
        const sectionElement = document.getElementById(section.id) //sectionRefs['seccion3'].value;
        if (sectionElement) {
            const sectionTop = sectionElement.offsetTop
            const sectionHeight = sectionElement.offsetHeight
            if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                activeItem.value = section.index
                break
            }
        }
    }
}

onMounted(() => {
    console.log('mounted')
    //scrollPanel = document.querySelector('.scroll-panel .p-scrollpanel-content');
    document.querySelector('.scroll-panel .p-scrollpanel-content').addEventListener('scroll', handleScroll)
})

</script>

<template>
    <h3 class="h2 ml-3">Admisión </h3>
    <div class="flex center">
        <Panel>
            <TabMenu v-model:activeIndex="activeItem" :model="items" class="mb-3" />
            <!-- <TabMenu v-model:activeIndex="active" :model="items" class="mb-3" :activeItem="activeItem" /> -->
            <ScrollPanel class="scroll-panel" style="width: 100%; height: 600px" @scroll="handleScroll">
                <div class="content" v-if="admision">

                    <Panel header="ESTUDIANTE" id="estudiante">
                        <div class="formgrid grid">
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="tipo_doc">Tipo de Documento</label>
                                    <InputText id="tipo_doc" type="text" v-model="admision.tipo_doc" class="w-full" />
                                </FloatLabel>
                            </div>

                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="n_doc">Número de Documento</label>
                                    <InputText id="n_doc" type="text" v-model="admision.n_doc" class="w-full" />
                                </FloatLabel>
                            </div>

                            <!-- <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="rut_annio">RUT Año</label>
                                    <InputText id="rut_annio" type="text" v-model="admision.rut_annio" class="w-full" />
                                </FloatLabel>
                            </div> -->

                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="dv">Dígito Verificador</label>
                                    <InputText id="dv" type="text" v-model="admision.dv" class="w-full" />
                                </FloatLabel>
                            </div>

                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="ap_pat">Apellido Paterno</label>
                                    <InputText id="ap_pat" type="text" v-model="admision.ap_paterno" class="w-full" />
                                </FloatLabel>
                            </div>

                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="ap_mat">Apellido Materno</label>
                                    <InputText id="ap_mat" type="text" v-model="admision.ap_materno" class="w-full" />
                                </FloatLabel>
                            </div>

                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="nombre">Nombre</label>
                                    <InputText id="nombre" type="text" v-model="admision.nombres" class="w-full" />
                                </FloatLabel>
                            </div>

                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="sexo">Sexo</label>
                                    <InputText id="sexo" type="text" v-model="admision.sexo" class="w-full" />
                                </FloatLabel>
                            </div>

                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="fnac">Fecha de Nacimiento</label>
                                    <InputText id="fnac" type="text" v-model="admision.fnac" class="w-full" />
                                </FloatLabel>
                            </div>

                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="nacionalidad">Nacionalidad</label>
                                    <InputText id="nacionalidad" type="text" v-model="admision.nacionalidad"
                                        class="w-full" />
                                </FloatLabel>
                            </div>

                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="pais_est_sec">País de Estudios Secundarios</label>
                                    <InputText id="pais_est_sec" type="text" v-model="admision.pais_est_sec"
                                        class="w-full" />
                                </FloatLabel>
                            </div>
                        </div>
                    </Panel>

                    <Panel header="IES" id="ies" class="mt-8">
                        <div class="formgrid grid">
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="cod_ies">Código</label>
                                    <InputText id="cod_ies" type="text" v-model="admision.cod_ies" class="w-full" />
                                </FloatLabel>
                            </div>
                        </div>
                    </Panel>

                    <Panel header="SEDE" id="sede" class="mt-8">
                        <div class="formgrid grid">
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="cod_sed">Código</label>
                                    <InputText id="cod_sed" type="text" v-model="admision.cod_sed" class="w-full" />
                                </FloatLabel>
                            </div>
                        </div>
                    </Panel>

                    <Panel header="SIES" id="sies" class="mt-8">
                        <div class="formgrid grid">
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="jornada">Jornada</label>
                                    <InputText id="jornada" type="text" v-model="admision.jornada" class="w-full" />
                                </FloatLabel>
                            </div>
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="version">Versión</label>
                                    <InputText id="version" type="text" v-model="admision.version" class="w-full" />
                                </FloatLabel>
                            </div>
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="modalidad">Modalidad</label>
                                    <InputText id="modalidad" type="text" v-model="admision.modalidad" class="w-full" />
                                </FloatLabel>
                            </div>
                        </div>
                    </Panel>

                    <Panel header="CARRERA" id="carrera" class="mt-8">
                        <div class="formgrid grid">
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="cod_ua">Código UA</label>
                                    <InputText id="cod_ua" type="text" v-model="admision.cod_ua" class="w-full" />
                                </FloatLabel>
                            </div>
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="cod_car">Código Carrera</label>
                                    <InputText id="cod_car" type="text" v-model="admision.cod_car" class="w-full" />
                                </FloatLabel>
                            </div>
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="nombre_carrera">Nombre Carrera</label>
                                    <InputText id="nombre_carrera" type="text" v-model="admision.nombre_carrera"
                                        class="w-full" />
                                </FloatLabel>
                            </div>
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="cod_demre">Código DEMRE</label>
                                    <InputText id="cod_demre" type="text" v-model="admision.cod_demre" class="w-full" />
                                </FloatLabel>
                            </div>
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="nivel">Nivel</label>
                                    <InputText id="nivel" type="text" v-model="admision.nivel" class="w-full" />
                                </FloatLabel>
                            </div>
                        </div>
                    </Panel>

                    <Panel header="MATRICULA" id="matricula" class="mt-8">
                        <div class="formgrid grid">
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="for_ing_act">Forma Ingreso Actual</label>
                                    <InputText id="for_ing_act" type="text" v-model="admision.for_ing_act"
                                        class="w-full" />
                                </FloatLabel>
                            </div>
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="annio_ing_act">Año Ingreso Actual</label>
                                    <InputText id="annio_ing_act" type="text" v-model="admision.annio_ing_act"
                                        class="w-full" />
                                </FloatLabel>
                            </div>
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="sem_ing_act">Semestre Ingreso Actual</label>
                                    <InputText id="sem_ing_act" type="text" v-model="admision.sem_ing_act"
                                        class="w-full" />
                                </FloatLabel>
                            </div>
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="annio_ing_ori">Año Ingreso Origen</label>
                                    <InputText id="annio_ing_ori" type="text" v-model="admision.annio_ing_ori"
                                        class="w-full" />
                                </FloatLabel>
                            </div>
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="sem_ing_ori">Semestre Ingreso Origen</label>
                                    <InputText id="sem_ing_ori" type="text" v-model="admision.sem_ing_ori"
                                        class="w-full" />
                                </FloatLabel>
                            </div>
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="sit_fond_solid">Situación Fondo Solidario</label>
                                    <InputText id="sit_fond_solid" type="text" v-model="admision.sit_fond_solid"
                                        class="w-full" />
                                </FloatLabel>
                            </div>
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="vigencia">Vigencia</label>
                                    <InputText id="vigencia" type="text" v-model="admision.vigencia" class="w-full" />
                                </FloatLabel>
                            </div>
                        </div>
                    </Panel>

                    <Panel header="COLEGIO" id="colegio" class="mt-8">
                        <div class="formgrid grid">
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="nombre_establecimiento">Nombre</label>
                                    <InputText id="nombre_establecimiento" type="text"
                                        v-model="admision.nombre_establecimiento" class="w-full" />
                                </FloatLabel>
                            </div>
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="nombre_region">Región</label>
                                    <InputText id="nombre_region" type="text" v-model="admision.nombre_region"
                                        class="w-full" />
                                </FloatLabel>
                            </div>


                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="nombre_provincia">Provincia</label>
                                    <InputText id="nombre_provincia" type="text" v-model="admision.nombre_provincia"
                                        class="w-full" />
                                </FloatLabel>
                            </div>
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="nombre_comuna">Comuna</label>
                                    <InputText id="nombre_comuna" type="text" v-model="admision.nombre_comuna"
                                        class="w-full" />
                                </FloatLabel>
                            </div>
                        </div>
                    </Panel>

                    <Panel header="INFORMACIÓN CURRICULAR" id="inf_curricular" class="mt-8">
                        <div class="formgrid grid">
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="promedio_notas_em">Promedio Notas</label>
                                    <InputText id="promedio_notas_em" type="text" v-model="admision.promedio_notas_em"
                                        class="w-full" />
                                </FloatLabel>
                            </div>
                            <div class="field col-12 md:col-6 lg:col-3 mt-3">
                                <FloatLabel>
                                    <label for="puntaje_nem">Puntaje NEM</label>
                                    <InputText id="puntaje_nem" type="text" v-model="admision.puntaje_nem"
                                        class="w-full" />
                                </FloatLabel>
                            </div>

                        </div>
                    </Panel>
                </div>
            </ScrollPanel>
        </Panel>
    </div>
</template>

<style lang="css" scoped>
.p-field {
    margin-bottom: 20px;
}

.fixed-tabmenu {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    /* Asegúrate de que esté por encima de otros elementos */
}
</style>
