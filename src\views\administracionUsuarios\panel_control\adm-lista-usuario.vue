<template>
    <div class="surface-ground py-7 md:px-5 lg:px-6">
        <div class="card">
            <div style="margin-left: 1rem;margin-right: 1rem;">
                <DataTable v-model:expandedRowGroups="expandedRowGroups" paginator :rows="20" dataKey="id"
                    :value="filteredUsersData" expandableRowGroups rowGroupMode="subheader" groupRowsBy="email"
                    sortMode="single" sortField='email' :sortOrder="1" scrollable lazy :loading="isLoading"
                    style="max-height: 500px; overflow-y: auto;">
                    <template #empty>
                        <div
                            style="display: flex; justify-content: center; align-items: center; height: 100%; text-align: center;">
                            No se encontraron usuarios a su cargo.
                        </div>
                    </template>
                    <template #header>
                        <div class="flex justify-content-end">
                            <IconField iconPosition="left">
                                <InputIcon>
                                    <i class="pi pi-search" />
                                </InputIcon>
                                <InputText v-model="filters['global'].value" placeholder="Buscar por correo" />
                            </IconField>
                        </div>
                    </template>
                    <template #groupheader="slotProps">
                        <span class="vertical-align-middle ml-2 font-bold line-height-3">{{ slotProps.data.email
                            }}</span>
                    </template>
                    <Column field="email" header="Email"></Column>
                    <Column field="recurso" header="Area" style="text-align: center">
                        <template #body="slotProps">
                            {{ slotProps.data.recurso || 'N/A' }}
                        </template>
                    </Column>
                    <Column field="rol" header="Rol" style="text-align: center">
                        <template #body="slotProps">
                            {{ slotProps.data.rol || 'N/A' }}
                        </template>
                    </Column>
                    <Column field="eliminar" header="Eliminar" style="text-align: center;">
                        <template #body="slotProps">
                            <Button icon="pi pi-trash" class="p-button-text" @click="dialogEliminar(slotProps.data)" />
                        </template>
                    </Column>
                    <!-- Footer for Create Button -->
                    <template #groupfooter="slotProps">
                        <div style="text-align:  -webkit-center">
                            <tr>
                                <td style="text-align: center; vertical-align: middle; height: 4rem;">
                                    <Button label="Agregar acceso" class="p-button-outlined"
                                        @click="openDialog(slotProps.data)" />
                                </td>
                            </tr>
                        </div>
                    </template>
                </DataTable>
            </div>
        </div>
    </div>
    <Toast position="bottom-left" group="bl" />
    <Dialog v-model:visible="dialogIsVisible" modal header="Añadir nuevo acceso" :style="{ width: '50rem' }"
        v-on:hide="onDialogClose()">
        <div v-if="dialogIsLoading && !createIsCorrect">
            <Loading />
        </div>

        <div v-if="!dialogIsLoading && !createIsCorrect">
            <form @submit.prevent="submitForm">
                <!--Creacion de un nuevo usuario-->
                <div style="margin-left: 1rem;margin-right: 1rem;">
                    <div v-if="true" class="formgrid grid">
                        <div class="field col-6" v-for="(field, index) in formFormat1" :key="field.id">
                            <label v-if="!field.dontNeedtoolTip" style="padding-right: 1rem ;">{{ field.displayName
                                }}</label>
                            <i class="pi pi-info-circle" v-if="!field.dontNeedtoolTip" style="justify-content: center "
                                v-tooltip.bottom="field.tooltip" />
                            <Dropdown :class="{
                                'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                            }" v-if="field.contenedor === 'dropdown' && field.name == 'recurso'"
                                v-model="formData[field.name]" :placeholder="field.placeholder"
                                style="height:2.6rem;margin-bottom: 1rem" :options="getOptions(field.options)"
                                :optionLabel="field.optionLabel" :optionValue="field.optionValue" class="w-full">
                            </Dropdown>
                            <Dropdown :class="{
                                'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                            }" v-if="field.contenedor === 'dropdown' && field.name == 'rol' && formData.recurso"
                                v-model="formData[field.name]" :placeholder="field.placeholder"
                                style="height:2.6rem;margin-bottom: 1rem" :options="getOptions(field.options)"
                                :optionLabel="field.optionLabel" :optionValue="field.optionValue" class="w-full">
                            </Dropdown>
                        </div>
                    </div>
                </div>
                <div
                    style="display: flex; justify-content: center; align-items: center;padding-top: 0.5rem;padding-bottom: 0.5rem;">
                    <Button label="Crear" @click="submitForm()" />
                </div>
            </form>
        </div>

        <!-- Success message -->
        <div v-if="!dialogIsLoading && createIsCorrect"
            style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
            <i class="pi pi-check" style="font-size: 5rem; color: blue;"></i>
            <br />
            <span class="p-text-secondary block mb-5">Elemento creado correctamente!</span>
        </div>
    </Dialog>

    <Dialog v-model:visible="dialogIsVisibleEliminar" modal header="Confirmar eliminación" :style="{ width: '25rem' }"
        v-on:hide="onDialogEliminarClose()">
        <div v-if="dialogIsLoading && !deleteIsCorrect">
            <Loading />
        </div>

        <div v-if="!dialogIsLoading && !deleteIsCorrect">
            <div>
                <!-- Validation error message -->
                <p>Esto eliminará permanentemente este registro ¿Está seguro?</p>
            </div>
            <br />
            <div class="flex justify-content-center gap-2">
                <InputSwitch style="scale: 1.5;" v-model="deletePreConfirmation"></InputSwitch>
            </div>
            <br />
            <!-- Validation error message -->
            <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}<br /></div>
            <!-- Buttons -->
            <div class="flex justify-content-end gap-2">
                <Button type="button" label="Cancelar" severity="secondary"
                    @click="dialogVisibleEliminar = false"></Button>
                <Button v-if="deletePreConfirmation" type="button" label="Eliminar"
                    @click="deleteUserAccess()"></Button>
            </div>
        </div>

        <!-- Success message -->
        <div v-if="!dialogIsLoading && deleteIsCorrect"
            style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
            <i class="pi pi-trash" style="font-size: 5rem; color: blue;"></i>
            <br />
            <span class="p-text-secondary block mb-5">¡Eliminado correctamente!</span>
        </div>
    </Dialog>
</template>

<script>
import { useAuthStore } from '../../../store/auth';
import { ref, computed, onMounted, watch } from 'vue';
import axios from 'axios';
import { useToast } from 'primevue/usetoast';
import { FilterMatchMode } from 'primevue/api';
import { encryptData, decryptData } from '@/utils/crypto';

export default {
    name: "adm-creacion-usuario",
    //TODO: Remove the roles and areas that the user dont have access asign to other users in the fo
    setup() {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const authStore = useAuthStore();
        const toast = useToast();
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const userToken = computed(() => authStore.idToken);
        const permissionsList = computed(() => decryptData(authStore.permissionsList));
        const isLoading = ref(false);
        const usersData = ref([]);
        const originalUsersData = ref([]);
        const expandedRowGroups = ref();
        const dialogIsVisible = ref(false);
        const dialogIsLoading = ref(false);
        const selectedFuncionario = ref(null);
        const rolesData = ref(null);
        const recursosData = ref(null);
        const filteredRolesData = ref([]);
        const filteredRecursosData = ref([]);
        const submitted = ref(false);
        const dialogIsVisibleEliminar = ref(false);
        const deletePreConfirmation = ref(false);
        const deleteIsCorrect = ref(false);
        const createIsCorrect = ref(false);
        const errorMessage = ref(null);
        const filters = ref({
            global: { value: null, matchMode: FilterMatchMode.CONTAINS },
            email: { value: null, matchMode: FilterMatchMode.CONTAINS },
        });

        // Computed property to display the data in the dataTable and also filter usersData in the search bar at the top of the table
        const filteredUsersData = computed(() => {
            const filterValue = filters.value.global?.value?.toLowerCase() || '';
            return usersData.value
                .filter(user => user.email.toLowerCase().includes(filterValue))
                .sort((a, b) => a.email.localeCompare(b.email)); // Sort alphabetically by email
        });

        // Define initial form data
        const initialFormData = {
            email: null,
            recurso: null,
            rol: null,
            accion: "all"
        };

        const formData = ref({ ...initialFormData });

        const formFormat1 = ref([
            { id: 1, placeholder: 'Area laboral', name: 'recurso', displayName: 'Area de trabajo:', contenedor: 'dropdown', options: 'recursosData', optionLabel: 'nombre_recurso', optionValue: "nombre_recurso", tooltip: '' },
            { id: 2, placeholder: 'Permisos', name: 'rol', displayName: 'Permisos a asignar:', contenedor: 'dropdown', options: 'rolesData', optionLabel: 'nombre_rol', optionValue: "nombre_rol", tooltip: '' },
        ]);

        const getOptions = (optionKey) => {
            if (optionKey === 'rolesData') {
                return filteredRolesData.value;
            }
            if (optionKey === 'recursosData') {
                return filteredRecursosData.value;
            }
            return [];
        }

        // On component mount, fetch data
        onMounted(async () => {
            isLoading.value = true;
            await fetchUsuariosAcargo();
            await fetchAllRol();
            await fetchAllRecurso();

            isLoading.value = false;
        });

        watch(
            () => formData.value.recurso,
            (newArea) => {
                if (newArea === "Administracion PIU") {
                    // Shrink the filteredRolesData list to exclude "Administrador Sistema"
                    filteredRolesData.value = rolesData.value.filter(
                        (role) => role.nombre_rol === "Administrador Sistema"
                    );
                } else {
                    // Restore the full roles list if "Administracion PIU" is not selected
                    filteredRolesData.value = rolesData.value.filter(
                        (role) => role.nombre_rol !== "Administrador Sistema"
                    );
                }
            }
        );

        const openDialog = (funcionarioData) => {
            selectedFuncionario.value = funcionarioData;
            formData.value.email = funcionarioData.email

            const resultado = originalUsersData.value.find(user => user.email.toLowerCase() === funcionarioData.email.toLowerCase());

            // Check if "Administracion PIU" is present in the user's "accesos"
            const hasAdministracionPIU = resultado.accesos.some(access => access.recurso === "Administracion PIU");


            if (!hasAdministracionPIU) {
                // Filter roles, removing "Administrador Sistema" if "Administracion PIU" is a resource
                filteredRolesData.value = rolesData.value.filter(role => "Administrador Sistema");
            }

            // Filter resources based on user's current resources
            const currentResources = new Set(resultado.accesos.map(access => access.recurso));
            filteredRecursosData.value = recursosData.value.filter(resource => !currentResources.has(resource.nombre_recurso));

            console.log(filteredRecursosData.value);
            console.log(permissionsList.value);
            // -------

            dialogIsVisible.value = true;
        };

        const dialogEliminar = (funcionarioData) => {
            dialogIsVisibleEliminar.value = true;
            selectedFuncionario.value = funcionarioData;
            console.log(funcionarioData)
        };

        const onDialogClose = () => {
            formData.value = { ...initialFormData };
            submitted.value = false;
            dialogIsVisible.value = false;
            if (createIsCorrect.value) {
                fetchUsuariosAcargo();
            }
            createIsCorrect.value = false;
        };

        const onDialogEliminarClose = () => {
            formData.value = { ...initialFormData };
            deletePreConfirmation.value = false;
            submitted.value = false;
            dialogIsVisibleEliminar.value = false;
            if (deleteIsCorrect.value) {
                fetchUsuariosAcargo();
            }
            deleteIsCorrect.value = false;
        };

        const fetchUsuariosAcargo = async () => {
            dialogIsLoading.value = true;
            isLoading.value = true;
            try {
                const response = await axios.get(API_BASE_URL + "usuarioAreasEncargado/" + userEmail.value);
                originalUsersData.value = response.data;
                console.log("PermisosUsuario");
                console.log(response.data);
                // Flatten data and add unique IDs
                let idCounter = 0; // Counter for unique IDs
                const flattenedData = response.data.flatMap(user =>
                    (user.accesos || []).map(acceso => ({
                        id: ++idCounter, // Increment counter for unique ID
                        email: user.email.toLowerCase(),
                        nombre: user.nombre,
                        recurso: acceso.recurso,
                        rol: acceso.rol
                    }))
                );

                usersData.value = flattenedData;
                console.log(usersData.value);
            } catch (error) {
                console.error("Error fetching roles data:", error);
            } finally {
                isLoading.value = false;
                dialogIsLoading.value = false;
            }
        };

        const fetchAllRol = async () => {
            isLoading.value = true; // Start loading
            try {
                const response = await axios.get(API_BASE_URL + "roles/", {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${decryptData(userToken.value)}`,
                    },
                });
                rolesData.value = response.data;
            } catch (error) {
                console.error("Error fetching roles data:", error);
            } finally {
                isLoading.value = false; // End loading
            }
        };

        const fetchAllRecurso = async () => {
            isLoading.value = true; // Start loading
            try {
                const response = await axios.get(API_BASE_URL + "recursos/" + userEmail.value);
                recursosData.value = response.data;
            } catch (error) {
                console.error("Error fetching recursos data:", error);
            } finally {
                isLoading.value = false; // End loading
            }
        };




        const submitForm = async () => {
            submitted.value = true;
            if (!validateForm()) {
                // Notify the user about the empty fields
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Existen campos vacios', life: 5000 });
                return;
            }

            try {
                isLoading.value = true;
                const success = await postNewUserAccess();

                if (success) {
                    // Notify the user of successful form submission
                    toast.add({ severity: 'success', summary: 'Correcto', group: 'bl', detail: 'Formulario ingresado correctamente', life: 5000 });

                    // Reset form data after successful submission
                    formData.value = { ...initialFormData };
                    submitted.value = false; // Reset submitted state
                }
            } catch (error) {
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Hubo un problema al enviar el formulario', life: 5000 });
            } finally {
                isLoading.value = false;
                submitted.value = false;
            }
        };

        const validateForm = () => {
            let hasErrors = false;

            // Iterate through formData keys and validate non-conditional fields
            Object.keys(formData.value).forEach((key) => {
                const value = formData.value[key];

                // Check if the value is null or empty
                if (value === null || value === "") {
                    hasErrors = true;
                }
            });

            if (hasErrors) {
                // Return false if there are any errors
                return false;
            }
            return true;
        };

        const postNewUserAccess = async () => {
            dialogIsLoading.value = true;
            isLoading.value = true; // Start loading
            try {
                const response = await axios.post(API_BASE_URL + "acceso", formData.value, {
                    headers: {
                        'Content-Type': 'application/json',
                        Authorization: `Bearer ${decryptData(userToken.value)}`,
                    },
                });
                // Check if the response is successful (status code 201)
                if (response.status === 201) {
                    createIsCorrect.value = true;
                }
            } catch (error) {
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Ocurrio un error al enviar la informacion al servidor', life: 5000 });
                console.error("Error posting usuario data:", error.response || error);
                return false; // Return false on error
            } finally {
                isLoading.value = false; // End loading
                dialogIsLoading.value = false;
            }
        }

        const deleteUserAccess = async () => {
            dialogIsLoading.value = true;
            isLoading.value = true; // Start loading
            console.log(selectedFuncionario.value);
            const { email, rol, recurso } = selectedFuncionario.value;
            try {
                const response = await axios.delete(API_BASE_URL + "acceso/", {
                    data: { email, rol, recurso },
                    headers: {
                        'Content-Type': 'application/json',
                        Authorization: `Bearer ${decryptData(userToken.value)}`,
                    },
                });
                // Check if the response is successful (status code 200)
                if (response.status === 200) {
                    deleteIsCorrect.value = true;
                }
            } catch (error) {
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Ocurrio un error al enviar la informacion al servidor', life: 5000 });

                console.error("Error deleting usuario data:", error.response || error);
                return false; // Return false on error
            } finally {
                isLoading.value = false; // End loading
                dialogIsLoading.value = false;
            }
        }


        return {
            isLoading,
            filters,
            dialogIsVisible,
            openDialog,
            selectedFuncionario,
            onDialogClose,
            expandedRowGroups,
            filteredUsersData,
            formData,
            formFormat1,
            getOptions,
            rolesData,
            recursosData,
            submitForm,
            submitted,
            validateForm,
            originalUsersData,
            onDialogEliminarClose,
            dialogIsLoading,
            deletePreConfirmation,
            deleteIsCorrect,
            createIsCorrect,
            dialogIsVisibleEliminar,
            dialogEliminar,
            errorMessage,
            deleteUserAccess,
            usersData,
            permissionsList
        };
    },
};
</script>

<style>
.p-datatable .p-column-header-content {
    text-align: center;
    display: block;
}
</style>
