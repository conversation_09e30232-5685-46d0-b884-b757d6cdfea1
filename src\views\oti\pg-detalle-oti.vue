<template>
    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading && infraestructuraRecursoData">
            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <div class='surface-card shadow-3 p-3 border-round'>
                    <!--Titulo-->
                    <div style="display: flex;align-items: center;padding-left: 1rem;">

                        <Button @click.prevent="goBack()"> <- </Button>
                                <h2 style="padding-left: 1rem;">Detalle de infraestructura y recursos año {{
                                    anio_proceso }}
                                </h2>
                    </div>
                    <br />

                    <!--Inmueble permanente-->
                    <div style="margin-left: 1rem;margin-right: 1rem;">
                        <div style="display: flex;justify-content: space-between;align-items: center;">
                            <h3>Inmueble permanente</h3>
                            <Button
                                v-if="infraestructuraRecursoData.is_finalized != true && (hasAccess('Encargado', 'OTI') || hasAccess('Usuario General', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU'))"
                                @click="goToCreate(anio_proceso, 'inmueble permanente')">Nuevo</Button>
                        </div>
                        <DataTable sortField="createdAt" :sortOrder="-1" showGridlines scrollable scrollHeight="250px"
                            :value="infraestructuraRecursoData.InmueblePermanentes" style="width: 100%;">
                            <template #empty>
                                <div style="text-align: center;"> No se encontraron datos </div>
                            </template>
                            <Column header="Id" style="width: 5%;">
                                <template #body="slotProps">
                                    {{ slotProps.index + 1 }}
                                </template>
                            </Column>
                            <Column field="nombre_identificacion" header="Nombre de identificacion" style="width: 30%;">
                                <template #body="slotProps">
                                    <span v-if="slotProps.data.nombre_identificacion.length > 15"
                                        v-tooltip.bottom="slotProps.data.nombre_identificacion">{{
                                            truncateText(slotProps.data.nombre_identificacion, textTruncateLength) }}</span>
                                    <span v-else>{{
                                        truncateText(slotProps.data.nombre_identificacion, textTruncateLength) }}</span>
                                </template>
                            </Column>
                            <Column field="comuna" header="Comuna" style="width: 15%;"></Column>
                            <Column field="direccion_inmueble" header="Direccion del inmueble" style="width: 25%;">
                                <template #body="slotProps">
                                    <span v-if="slotProps.data.direccion_inmueble.length > 15"
                                        v-tooltip.bottom="slotProps.data.direccion_inmueble">{{
                                            truncateText(slotProps.data.direccion_inmueble, textTruncateLength) }}</span>
                                    <span v-else>{{
                                        truncateText(slotProps.data.direccion_inmueble, textTruncateLength) }}</span>
                                </template>
                            </Column>
                            <Column field="vigencia" header="Estado" style="width: 15%">
                                <template #body="slotProps">
                                    <span>{{ slotProps.data.vigencia }}</span>
                                </template>
                            </Column>
                            <Column field="ver" header="Ver" style="width: 5%;text-align: center">
                                <template #body="slotProps">
                                    <Button icon="pi pi-eye" style="" class="p-button-text"
                                        @click="goToDetails('inmueble permanente', slotProps.data.inmueblePermanente_id)" />
                                </template>
                            </Column>
                            <Column
                                v-if="(hasAccess('Encargado', 'OTI') || hasAccess('Usuario General', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU'))"
                                field="elimnar" header="Eliminar" style="width: 5%;text-align: center">
                                <template #body="slotProps">
                                    <Button icon="pi pi-trash" style="" class="p-button-text"
                                        @click="dialogVisibleEliminar = true, idToDelete = slotProps.data.inmueblePermanente_id, typeToDelete = 'InmueblePermanente', checkVigenciaInmuebleToDelete(idToDelete, typeToDelete)" />
                                </template>
                            </Column>
                        </DataTable>
                    </div>

                    <br />
                    <br />
                    <!--Inmueble restringido-->
                    <div style="margin-left: 1rem;margin-right: 1rem;">
                        <div style="display: flex;justify-content: space-between;align-items: center;">
                            <h3>Inmueble restringido</h3>
                            <Button
                                v-if="infraestructuraRecursoData.is_finalized != true && (hasAccess('Encargado', 'OTI') || hasAccess('Usuario General', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU'))"
                                @click="goToCreate(anio_proceso, 'inmueble restringido')">Nuevo</Button>
                        </div>
                        <DataTable sortField="createdAt" :sortOrder="-1" showGridlines scrollable scrollHeight="250px"
                            :value="infraestructuraRecursoData.InmuebleRestringidos" style="width: 100%;">
                            <template #empty>
                                <div style="text-align: center;"> No se encontraron datos </div>
                            </template>
                            <Column header="Id" style="width: 5%;">
                                <template #body="slotProps">
                                    {{ slotProps.index + 1 }}
                                </template>
                            </Column>
                            <Column field="nombre_identificacion" header="Nombre de identificacion" style="width: 30%;">
                                <template #body="slotProps">
                                    <span v-if="slotProps.data.nombre_identificacion.length > 15"
                                        v-tooltip.bottom="slotProps.data.nombre_identificacion">{{
                                            truncateText(slotProps.data.nombre_identificacion, textTruncateLength) }}</span>
                                    <span v-else>{{
                                        truncateText(slotProps.data.nombre_identificacion, textTruncateLength) }}</span>
                                </template>
                            </Column>
                            <Column field="comuna" header="Comuna" style="width: 15%;"></Column>
                            <Column field="direccion_inmueble" header="Direccion del inmueble" style="width: 25%;">
                                <template #body="slotProps">
                                    <span v-if="slotProps.data.direccion_inmueble.length > 15"
                                        v-tooltip.bottom="slotProps.data.direccion_inmueble">{{
                                            truncateText(slotProps.data.direccion_inmueble, textTruncateLength) }}</span>
                                    <span v-else>{{
                                        truncateText(slotProps.data.direccion_inmueble, textTruncateLength) }}</span>
                                </template>
                            </Column>
                            <Column field="vigencia" header="Estado" style="width: 15%">
                                <template #body="slotProps">
                                    <span>{{ slotProps.data.vigencia }}</span>
                                </template>
                            </Column>
                            <Column field="ver" header="Ver" style="width: 5%;text-align: center">
                                <template #body="slotProps">
                                    <Button icon="pi pi-eye" style="" class="p-button-text"
                                        @click="goToDetails('inmueble restringido', slotProps.data.inmuebleRestringido_id)" />
                                </template>
                            </Column>
                            <Column
                                v-if="(hasAccess('Encargado', 'OTI') || hasAccess('Usuario General', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU'))"
                                field="elimnar" header="Eliminar" style="width: 5%;text-align: center">
                                <template #body="slotProps">
                                    <Button icon="pi pi-trash" style="" class="p-button-text"
                                        @click="dialogVisibleEliminar = true, idToDelete = slotProps.data.inmuebleRestringido_id, typeToDelete = 'InmuebleRestringido', checkVigenciaInmuebleToDelete(idToDelete, typeToDelete)" />
                                </template>
                            </Column>
                        </DataTable>
                    </div>

                    <br />
                    <br />
                    <!--Bibliotecas-->
                    <div style="margin-left: 1rem;margin-right: 1rem;">
                        <div style="display: flex;justify-content: space-between;align-items: center;">
                            <h3>Bibliotecas</h3>
                            <Button
                                v-if="infraestructuraRecursoData.is_finalized != true && (hasAccess('Encargado', 'OTI') || hasAccess('Usuario General', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU'))"
                                @click="goToCreate(anio_proceso, 'biblioteca')">Nuevo</Button>
                        </div>
                        <DataTable sortField="createdAt" :sortOrder="-1" showGridlines scrollable scrollHeight="250px"
                            :value="infraestructuraRecursoData.Bibliotecas" style="width: 100%;">
                            <template #empty>
                                <div style="text-align: center;"> No se encontraron datos </div>
                            </template>
                            <Column header="Id" style="width: 5%;">
                                <template #body="slotProps">
                                    {{ slotProps.index + 1 }}
                                </template>
                            </Column>
                            <Column field="nombre_identificacion" header="Nombre de identificacion" style="width: 30%;">
                                <template #body="slotProps">
                                    <span v-if="slotProps.data.nombre_identificacion.length > 15"
                                        v-tooltip.bottom="slotProps.data.nombre_identificacion">{{
                                            truncateText(slotProps.data.nombre_identificacion, textTruncateLength) }}</span>
                                    <span v-else>{{
                                        truncateText(slotProps.data.nombre_identificacion, textTruncateLength) }}</span>
                                </template>
                            </Column>
                            <Column field="comuna" header="Comuna" style="width: 15%;"></Column>
                            <Column field="direccion_inmueble" header="Direccion del inmueble" style="width: 25%;">
                                <template #body="slotProps">
                                    <span v-if="slotProps.data.direccion_inmueble.length > 15"
                                        v-tooltip.bottom="slotProps.data.direccion_inmueble">{{
                                            truncateText(slotProps.data.direccion_inmueble, textTruncateLength) }}</span>
                                    <span v-else>{{
                                        truncateText(slotProps.data.direccion_inmueble, textTruncateLength) }}</span>
                                </template>
                            </Column>
                            <Column field="vigencia" header="Estado" style="width: 15%">
                                <template #body="slotProps">
                                    <span>{{ slotProps.data.vigencia }}</span>
                                </template>
                            </Column>
                            <Column field="ver" header="Ver" style="width: 5%;text-align: center">
                                <template #body="slotProps">
                                    <Button icon="pi pi-eye" style="" class="p-button-text"
                                        @click="goToDetails('biblioteca', slotProps.data.biblioteca_id)" />
                                </template>
                            </Column>
                            <Column
                                v-if="(hasAccess('Encargado', 'OTI') || hasAccess('Usuario General', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU'))"
                                field="elimnar" header="Eliminar" style="width: 5%;text-align: center">
                                <template #body="slotProps">
                                    <Button icon="pi pi-trash" style="" class="p-button-text"
                                        @click="dialogVisibleEliminar = true, idToDelete = slotProps.data.biblioteca_id, typeToDelete = 'Biblioteca', checkVigenciaInmuebleToDelete(idToDelete, typeToDelete)" />
                                </template>
                            </Column>
                        </DataTable>
                    </div>

                    <br />
                    <br />
                    <!--Libros y bases de datos digitales-->
                    <div style="margin-left: 1rem;margin-right: 1rem;">
                        <div style="display: flex;justify-content: space-between;align-items: center;">
                            <h3>Libros y bases de datos digitales</h3>
                            <Button
                                v-if="infraestructuraRecursoData.is_finalized != true && (hasAccess('Encargado', 'OTI') || hasAccess('Usuario General', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU'))"
                                @click="goToCreate(anio_proceso, 'libro y base digital')">Nuevo</Button>
                        </div>
                        <DataTable sortField="createdAt" :sortOrder="-1" showGridlines scrollable scrollHeight="250px"
                            :value="infraestructuraRecursoData.LibrosBasesDigitales" style="width: 100%;">
                            <template #empty>
                                <div style="text-align: center;"> No se encontraron datos </div>
                            </template>
                            <Column header="Id" style="width: 5%;">
                                <template #body="slotProps">
                                    {{ slotProps.index + 1 }}
                                </template>
                            </Column>
                            <Column field="nombre_identificacion" header="Nombre de identificacion" style="width: 30%;">
                                <template #body="slotProps">
                                    <span v-if="slotProps.data.nombre_identificacion.length > 15"
                                        v-tooltip.bottom="slotProps.data.nombre_identificacion">{{
                                            truncateText(slotProps.data.nombre_identificacion, textTruncateLength) }}</span>
                                    <span v-else>{{
                                        truncateText(slotProps.data.nombre_identificacion, textTruncateLength) }}</span>
                                </template>
                            </Column>
                            <Column field="comuna" header="Comuna" style="width: 15%;"></Column>
                            <Column field="direccion_inmueble" header="Direccion del inmueble" style="width: 25%;">
                                <template #body="slotProps">
                                    <span v-if="slotProps.data.direccion_inmueble.length > 15"
                                        v-tooltip.bottom="slotProps.data.direccion_inmueble">{{
                                            truncateText(slotProps.data.direccion_inmueble, textTruncateLength) }}</span>
                                    <span v-else>{{
                                        truncateText(slotProps.data.direccion_inmueble, textTruncateLength) }}</span>
                                </template>
                            </Column>
                            <Column field="vigencia" header="Vigencia" style="width: 15%">
                                <template #body="slotProps">
                                    <span>{{ slotProps.data.vigencia }}</span>
                                </template>
                            </Column>
                            <Column field="ver" header="Ver" style="width: 5%;text-align: center">
                                <template #body="slotProps">
                                    <Button icon="pi pi-eye" style="" class="p-button-text"
                                        @click="goToDetails('libro y base digital', slotProps.data.librosBasesDigitales_id)" />
                                </template>
                            </Column>
                            <Column
                                v-if="(hasAccess('Encargado', 'OTI') || hasAccess('Usuario General', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU'))"
                                field="elimnar" header="Eliminar" style="width: 5%;text-align: center">
                                <template #body="slotProps">
                                    <Button icon="pi pi-trash" style="" class="p-button-text"
                                        @click="dialogVisibleEliminar = true, idToDelete = slotProps.data.librosBasesDigitales_id, typeToDelete = 'LibrosBasesDigitales', checkVigenciaInmuebleToDelete(idToDelete, typeToDelete)" />
                                </template>
                            </Column>
                        </DataTable>
                    </div>

                    <br />
                    <br />
                    <!--Predios-->
                    <div style="margin-left: 1rem;margin-right: 1rem;">
                        <div style="display: flex;justify-content: space-between;align-items: center;">
                            <h3>Predios</h3>
                            <Button
                                v-if="infraestructuraRecursoData.is_finalized != true && (hasAccess('Encargado', 'OTI') || hasAccess('Usuario General', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU'))"
                                @click="goToCreate(anio_proceso, 'predio')">Nuevo</Button>
                        </div>
                        <DataTable sortField="createdAt" :sortOrder="-1" showGridlines scrollable scrollHeight="250px"
                            :value="infraestructuraRecursoData.Predios" style="width: 100%;">
                            <template #empty>
                                <div style="text-align: center;"> No se encontraron datos </div>
                            </template>
                            <Column header="Id" style="width: 5%;">
                                <template #body="slotProps">
                                    {{ slotProps.index + 1 }}
                                </template>
                            </Column>
                            <Column field="nombre_identificacion" header="Nombre de identificacion" style="width: 30%;">
                                <template #body="slotProps">
                                    <span v-if="slotProps.data.nombre_identificacion.length > 15"
                                        v-tooltip.bottom="slotProps.data.nombre_identificacion">{{
                                            truncateText(slotProps.data.nombre_identificacion, textTruncateLength) }}</span>
                                    <span v-else>{{
                                        truncateText(slotProps.data.nombre_identificacion, textTruncateLength) }}</span>
                                </template>
                            </Column>
                            <Column field="comuna" header="Comuna" style="width: 15%;"></Column>
                            <Column field="direccion_inmueble" header="Direccion del inmueble" style="width: 25%;">
                                <template #body="slotProps">
                                    <span v-if="slotProps.data.direccion_inmueble.length > 15"
                                        v-tooltip.bottom="slotProps.data.direccion_inmueble">{{
                                            truncateText(slotProps.data.direccion_inmueble, textTruncateLength) }}</span>
                                    <span v-else>{{
                                        truncateText(slotProps.data.direccion_inmueble, textTruncateLength) }}</span>
                                </template>
                            </Column>
                            <Column field="vigencia" header="Vigencia" style="width: 15%">
                                <template #body="slotProps">
                                    <span>{{ slotProps.data.vigencia }}</span>
                                </template>
                            </Column>
                            <Column field="ver" header="Ver" style="width: 5%;text-align: center">
                                <template #body="slotProps">
                                    <Button icon="pi pi-eye" style="" class="p-button-text"
                                        @click="goToDetails('predio', slotProps.data.predio_id)" />
                                </template>
                            </Column>
                            <Column
                                v-if="(hasAccess('Encargado', 'OTI') || hasAccess('Usuario General', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU'))"
                                field="elimnar" header="Eliminar" style="width: 5%;text-align: center">
                                <template #body="slotProps">
                                    <Button icon="pi pi-trash" style="" class="p-button-text"
                                        @click="dialogVisibleEliminar = true, idToDelete = slotProps.data.predio_id, typeToDelete = 'Predio', checkVigenciaInmuebleToDelete(idToDelete, typeToDelete)" />
                                </template>
                            </Column>
                        </DataTable>
                    </div>

                    <br />
                    <br />
                    <!--Plataformas virtuales-->
                    <div style="margin-left: 1rem;margin-right: 1rem;">
                        <div style="display: flex;justify-content: space-between;align-items: center;">
                            <h3>Plataformas virtuales</h3>
                            <Button
                                v-if="infraestructuraRecursoData.is_finalized != true && (hasAccess('Encargado', 'OTI') || hasAccess('Usuario General', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU'))"
                                @click="goToCreate(anio_proceso, 'plataforma-virtual')">Nuevo</Button>
                        </div>
                        <DataTable sortField="createdAt" :sortOrder="-1" showGridlines scrollable scrollHeight="250px"
                            :value="infraestructuraRecursoData.PlataformaVirtuals" style="width: 100%;">
                            <template #empty>
                                <div style="text-align: center;"> No se encontraron datos </div>
                            </template>
                            <Column header="Id" style="width: 5%;">
                                <template #body="slotProps">
                                    {{ slotProps.index + 1 }}
                                </template>
                            </Column>
                            <Column field="nombre_identificacion" header="Nombre de identificacion" style="width: 30%;">
                                <template #body="slotProps">
                                    <span v-if="slotProps.data.nombre_identificacion.length > 15"
                                        v-tooltip.bottom="slotProps.data.nombre_identificacion">{{
                                            truncateText(slotProps.data.nombre_identificacion, textTruncateLength) }}</span>
                                    <span v-else>{{
                                        truncateText(slotProps.data.nombre_identificacion, textTruncateLength) }}</span>
                                </template>
                            </Column>
                            <Column field="comuna" header="Comuna" style="width: 15%;"></Column>
                            <Column field="direccion_inmueble" header="Direccion del inmueble" style="width: 25%;">
                                <template #body="slotProps">
                                    <span v-if="slotProps.data.direccion_inmueble.length > 15"
                                        v-tooltip.bottom="slotProps.data.direccion_inmueble">{{
                                            truncateText(slotProps.data.direccion_inmueble, textTruncateLength) }}</span>
                                    <span v-else>{{
                                        truncateText(slotProps.data.direccion_inmueble, textTruncateLength) }}</span>
                                </template>
                            </Column>
                            <Column field="vigencia" header="Vigencia" style="width: 15%">
                                <template #body="slotProps">
                                    <span>{{ slotProps.data.vigencia }}</span>
                                </template>
                            </Column>
                            <Column field="ver" header="Ver" style="width: 5%;text-align: center">
                                <template #body="slotProps">
                                    <Button icon="pi pi-eye" style="" class="p-button-text"
                                        @click="goToDetails('plataforma virtual', slotProps.data.plataformaVirtual_id)" />
                                </template>
                            </Column>
                            <Column
                                v-if="(hasAccess('Encargado', 'OTI') || hasAccess('Usuario General', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU'))"
                                field="elimnar" header="Eliminar" style="width: 5%;text-align: center">
                                <template #body="slotProps">
                                    <Button icon="pi pi-trash" style="" class="p-button-text"
                                        @click="dialogVisibleEliminar = true, idToDelete = slotProps.data.plataformaVirtual_id, typeToDelete = 'PlataformaVirtual', checkVigenciaInmuebleToDelete(idToDelete, typeToDelete)" />
                                </template>
                            </Column>
                        </DataTable>
                    </div>

                    <br />
                </div>
            </div>

            <Dialog v-model:visible="dialogVisibleEliminar" modal header="Eliminar inmueble" :style="{ width: '25rem' }"
                v-on:hide="resetForm()">
                <div v-if="dialogIsLoading && !infraestructuraRecursoIsCorrect">
                    <Loading />
                </div>

                <div v-if="!dialogIsLoading && !infraestructuraRecursoIsCorrect">
                    <div>
                        <!-- Validation error message -->
                        <p>Esto eliminará permanentemente este registro ¿Está seguro?</p>
                    </div>
                    <br />
                    <div class="flex justify-content-center gap-2">
                        <InputSwitch :disabled="dialogHasError" style="scale: 1.5;" v-model="deletePreConfirmation">
                        </InputSwitch>
                    </div>
                    <br />
                    <!-- Validation error message -->
                    <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}<br /></div>


                    <!-- Buttons -->
                    <div class="flex justify-content-end gap-2">
                        <Button type="button" label="Cancelar" severity="secondary"
                            @click="dialogVisibleEliminar = false"></Button>
                        <Button :disabled="dialogHasError" v-if="deletePreConfirmation" type="button" label="Eliminar"
                            @click="eliminarInmueble()"></Button>
                    </div>
                </div>

                <!-- Success message -->
                <div v-if="!dialogIsLoading && infraestructuraRecursoIsCorrect"
                    style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
                    <i class="pi pi-trash" style="font-size: 5rem; color: blue;"></i>
                    <br />
                    <span class="p-text-secondary block mb-5">¡Eliminado correctamente!</span>
                </div>

            </Dialog>
        </div>
        <div v-else class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
                <Loading />
            </div>
        </div>
        <Toast position="bottom-left" group="bl" />
    </div>



</template>

<script>
import { useAuthStore } from '../../store/auth';
import { ref, computed, onMounted } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import axios from 'axios';
import Papa from 'papaparse';
import { useToast } from 'primevue/usetoast';
import { useRoute, useRouter } from 'vue-router';

export default {

    setup() {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const route = useRoute();
        const router = useRouter();
        const authStore = useAuthStore();
        const toast = useToast();
        const textTruncateLength = ref(15);

        // Obtain the stored user data from pinia and the global loading state
        const userName = computed(() => decryptData(authStore.userName));
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const permissionsList = computed(() => decryptData(authStore.permissionsList));
        const isUserAuthenticated = computed(() => decryptData(authStore.userIsAuthenticated));
        const globalLoading = computed(() => decryptData(authStore.isLoading));
        const userToken = computed(() => decryptData(authStore.idToken));

        // Variable to store InfraestructuraRecurso data
        const infraestructuraRecursoData = ref(null);

        // Theme for changing the actual theme from dark to light or viceversa
        const theme = ref("dark");

        // Dialog variables
        const dialogVisibleEliminar = ref(false);
        const dialogIsLoading = ref(false);
        const infraestructuraRecursoIsCorrect = ref(false);
        const deletePreConfirmation = ref(false);
        const errorMessage = ref("");
        const dialogHasError = ref(false);
        const idToDelete = ref("");
        const typeToDelete = ref("");

        const truncateText = (text, maxLength) => {
            return text.length > maxLength ? text.slice(0, maxLength) + '...' : text;
        };

        // Local "Loading" for the current view
        const isLoading = ref(false);

        const anio_proceso = decryptData(route.params.anio_proceso); // Access the param

        // On component mount, fetch data
        onMounted(async () => {
            if (anio_proceso) {
                await fetchInfraestructuraRecursoData(anio_proceso);
            }
        });

        const hasAccess = (allowedRol, requiredRecurso) => {
            const access = permissionsList.value.some(user => user.rol === allowedRol && user.recurso === requiredRecurso);
            return access;
        };

        const goToCreate = (data, tipo) => {

            const encrypted_year = encryptData(data);
            switch (tipo) {
                case 'inmueble permanente':
                    router.push({ name: 'Crear-inmueble-permanente', params: { anio_proceso: encrypted_year } });
                    break;
                case 'inmueble restringido':
                    router.push({ name: 'Crear-inmueble-restringido', params: { anio_proceso: encrypted_year } });
                    break;
                case 'biblioteca':
                    router.push({ name: 'Crear-biblioteca', params: { anio_proceso: encrypted_year } });
                    break;
                case 'libro y base digital':
                    router.push({ name: 'Crear-libro-base-digital', params: { anio_proceso: encrypted_year } });
                    break;
                case 'predio':
                    router.push({ name: 'Crear-predio', params: { anio_proceso: encrypted_year } });
                    break;
                case 'plataforma-virtual':
                    router.push({ name: 'Crear-plataforma-virtual', params: { anio_proceso: encrypted_year } });
                    break;
            }
        }
        const goToDetails = (tipo, detalle_id) => {
            switch (tipo) {
                case 'inmueble permanente':
                    router.push({ name: 'Ver-inmueble-permanente', params: { inmueblePermanente_id: detalle_id } });
                    break;
                case 'inmueble restringido':
                    router.push({ name: 'Ver-inmueble-restringido', params: { inmuebleRestringido_id: detalle_id } });
                    break;
                case 'biblioteca':
                    router.push({ name: 'Ver-detalle-biblioteca', params: { biblioteca_id: detalle_id } });
                    break;
                case 'libro y base digital':
                    router.push({ name: 'Ver-libro-base-digital', params: { librosBasesDigitales_id: detalle_id } });
                    break;
                case 'predio':
                    router.push({ name: 'Ver-predio', params: { predio_id: detalle_id } });
                    break;
                case 'plataforma virtual':
                    router.push({ name: 'Ver-plataforma-virtual', params: { plataformaVirtual_id: detalle_id } });
                    break;
            }
        };
        // Function to fetch InfraestructuraRecurso data based on the year
        const fetchInfraestructuraRecursoData = async (year) => {
            isLoading.value = true; // Start loading
            try {
                const response = await axios.get(API_BASE_URL + "infraestructuraRecurso/" + year, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                infraestructuraRecursoData.value = response.data;
                await updateVigenciaStatus(infraestructuraRecursoData.value);
                console.log(infraestructuraRecursoData.value);
            } catch (error) {
                console.error("Error fetching InfraestructuraRecurso data:", error);
            } finally {
                isLoading.value = false; // End loading
            }
        };

        const updateVigenciaStatus = (data) => {
            const keys = [
                "Bibliotecas",
                "PlataformaVirtuals",
                "InmueblePermanentes",
                "InmuebleRestringidos",
                "LibrosBasesDigitales",
                "Predios"
            ];
            keys.forEach((key) => {
                if (Array.isArray(data[key])) {
                    data[key] = data[key].map(item => ({
                        ...item,
                        vigencia: item.vigencia === 1 ? "Vigente" : "No vigente"
                    }));
                }
            });
        };

        const goBack = () => {
            router.push({ path: "/Pagina-principal-oti" });
        };

        const checkVigenciaInmuebleToDelete = (idInmueble, tipoInmueble) => {

            switch (tipoInmueble) {
                case "InmueblePermanente":
                    for (let index = 0; index < infraestructuraRecursoData.value.InmueblePermanentes.length; index++) {
                        const item = infraestructuraRecursoData.value.InmueblePermanentes[index];
                        if (item.inmueblePermanente_id === idInmueble) {
                            console.log("inmueble permanente FOUND");
                            if (item.vigencia === "No vigente") {
                                dialogHasError.value = false;
                            } else {
                                errorMessage.value = 'El inmueble seleccionado aún continúa vigente';
                                dialogHasError.value = true;
                            }
                            // Exit the loop once the item is found
                            break;
                        } else {
                            console.log("NOT FOUND");
                        }
                    }
                    return;
                case "InmuebleRestringido":
                    for (let index = 0; index < infraestructuraRecursoData.value.InmuebleRestringidos.length; index++) {
                        const item = infraestructuraRecursoData.value.InmuebleRestringidos[index];
                        if (item.inmuebleRestringido_id === idInmueble) {
                            console.log("inmueble restringido FOUND");
                            if (item.vigencia === "No vigente") {
                                dialogHasError.value = false;
                            } else {
                                errorMessage.value = 'El inmueble seleccionado aún continúa vigente';
                                dialogHasError.value = true;
                            }
                            // Exit the loop once the item is found
                            break;
                        } else {
                            console.log("NOT FOUND");
                        }
                    }
                    return;
                case "Biblioteca":
                    for (let index = 0; index < infraestructuraRecursoData.value.Bibliotecas.length; index++) {
                        const item = infraestructuraRecursoData.value.Bibliotecas[index];
                        if (item.biblioteca_id === idInmueble) {
                            console.log("Biblioteca FOUND");
                            if (item.vigencia === "No vigente") {
                                dialogHasError.value = false;
                            } else {
                                errorMessage.value = 'El inmueble seleccionado aún continúa vigente';
                                dialogHasError.value = true;
                            }
                            // Exit the loop once the item is found
                            break;
                        } else {
                            console.log("NOT FOUND");
                        }
                    }
                    return;
                case "LibrosBasesDigitales":
                    for (let index = 0; index < infraestructuraRecursoData.value.LibrosBasesDigitales.length; index++) {
                        const item = infraestructuraRecursoData.value.LibrosBasesDigitales[index];
                        if (item.librosBasesDigitales_id === idInmueble) {
                            console.log("LibrosBasesDigitales FOUND");
                            if (item.vigencia === "No vigente") {
                                dialogHasError.value = false;
                            } else {
                                errorMessage.value = 'El inmueble seleccionado aún continúa vigente';
                                dialogHasError.value = true;
                            }
                            // Exit the loop once the item is found
                            break;
                        } else {
                            console.log("NOT FOUND");
                        }
                    }
                    return;
                case "PlataformaVirtual":
                    for (let index = 0; index < infraestructuraRecursoData.value.PlataformaVirtuals.length; index++) {
                        const item = infraestructuraRecursoData.value.PlataformaVirtuals[index];
                        if (item.plataformaVirtual_id === idInmueble) {
                            console.log("Plataforma virtual FOUND");
                            if (item.vigencia === "No vigente") {
                                dialogHasError.value = false;
                            } else {
                                errorMessage.value = 'El inmueble seleccionado aún continúa vigente';
                                dialogHasError.value = true;
                            }
                            // Exit the loop once the item is found
                            break;
                        } else {
                            console.log("NOT FOUND");
                        }
                    }
                    return;
                case "Predio":
                    for (let index = 0; index < infraestructuraRecursoData.value.Predios.length; index++) {
                        const item = infraestructuraRecursoData.value.Predios[index];
                        if (item.predio_id === idInmueble) {
                            console.log("Predio FOUND");
                            if (item.vigencia === "No vigente") {
                                dialogHasError.value = false;
                            } else {
                                errorMessage.value = 'El inmueble seleccionado aún continúa vigente';
                                dialogHasError.value = true;
                            }
                            // Exit the loop once the item is found
                            break;
                        } else {
                            console.log("NOT FOUND");
                        }
                    }
                    return;
                default:
                    return;

            }

        };

        const eliminarInmueble = async () => {
            errorMessage.value = '';

            const eliminarResponse = await eliminarInfraestructura();

            if (eliminarResponse == "ok") {
                infraestructuraRecursoIsCorrect.value = true;
                await fetchInfraestructuraRecursoData(anio_proceso);

            } else if (eliminarResponse == "conflicto") {
                errorMessage.value = 'El elemento no debe tener infraestructuras o recursos asociados a él';
            } else if (eliminarResponse == "error") {
                errorMessage.value = 'Se detectó un error, inténtelo más tarde';
            }

        }


        const eliminarInfraestructura = async () => {
            errorMessage.value = '';

            try {
                dialogIsLoading.value = true;
                const body = ref({});
                body.value.userEmail = userEmail.value;
                body.value.userToken = "Bearer " + userToken.value;
                const response = await axios.delete(API_BASE_URL + `${typeToDelete.value}/${idToDelete.value}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    },
                    data: body.value
                });
                if (response.status == 200) {
                    return "ok";
                }
            } catch (error) {
                console.error("Error: ", error);
                if (error.status == 409) {
                    return "conflicto";
                } else {
                    if (error.status === 403) {
                        toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El registro no puede ser modificado', life: 5000 });
                    }
                    return "error";
                }
            } finally {
                dialogIsLoading.value = false;
            }

        }

        // Function to reset the form for a new creation
        const resetForm = () => {
            idToDelete.value = null;
            typeToDelete.value = null;
            dialogHasError.value = false;
            errorMessage.value = ''; // Clear any error messages
            infraestructuraRecursoIsCorrect.value = false; // Reset success state
            deletePreConfirmation.value = false;
            dialogVisibleEliminar.value = false;
        }


        return {
            isLoading,
            toast,
            globalLoading,
            anio_proceso,
            infraestructuraRecursoData,
            fetchInfraestructuraRecursoData,
            goToDetails,
            goToCreate,
            truncateText,
            textTruncateLength,
            goBack,
            updateVigenciaStatus,
            dialogVisibleEliminar,
            dialogIsLoading,
            dialogHasError,
            infraestructuraRecursoIsCorrect,
            deletePreConfirmation,
            errorMessage,
            idToDelete,
            typeToDelete,
            eliminarInmueble,
            resetForm,
            checkVigenciaInmuebleToDelete,
            hasAccess,
            permissionsList,
            userToken

        }
    }
}

</script>