import { computed, ref, watch } from "vue";
import { useMutation, useQuery } from "@tanstack/vue-query";

import backEndApi from "@/api/backEndApi";
import type {
  OAcademica,
  OAcademicaEtapa1PosgradoPostitulo,
  OAcademicaEtapa1Pregrado,
  OAcademicaEtapa3Pregrado,
  OAcademicaEtapa3PosgradoPostitulo,
} from "../interfaces/OAcademica";

const getOAcademica = async (oa_sies_id: string): Promise<OAcademica> => {
  const { data } = await backEndApi.get(`/api/v1/oacademica/${oa_sies_id}`);
  console.log("getOAcademica data: ", data);
  return data;
};

const updateOAcademica = async (
  oAcademica: OAcademica
): Promise<OAcademica> => {
  console.log("updateOAcademica updateando ando " + oAcademica.cod_carrera);
  const { data } = await backEndApi.patch<OAcademica>(
    `/api/v1/oacademica/${oAcademica.oa_sies_id}`,
    oAcademica
  );
  return data;
};

const updateOAcademicaEtapa1Pregrado = async (
  oAcademicaEtapa1Pregrado: OAcademicaEtapa1Pregrado
): Promise<OAcademicaEtapa1Pregrado> => {
  console.log("updateOAcademica updateando ando " + oAcademicaEtapa1Pregrado.oa_sies_id);
  const { data } = await backEndApi.patch<OAcademica>(
    `/api/v1/updateoacademicaetapa1pregrado`,
    oAcademicaEtapa1Pregrado
  );
  return data;
};
const updateOAcademicaEtapa3Pregrado = async (
  oAcademicaEtapa3Pregrado: OAcademicaEtapa3Pregrado
): Promise<OAcademicaEtapa3Pregrado> => {
  console.log("updateOAcademica updateando ando " + oAcademicaEtapa3Pregrado.oa_sies_id);
  const { data } = await backEndApi.patch<OAcademica>(
    `/api/v1/updateoacademicaetapa3pregrado`,
    oAcademicaEtapa3Pregrado
  );
  return data;
};
const updateOAcademicaEtapa1PosgradoPostitulo = async (
  oAcademicaEtapa1PosgradoPostitulo: OAcademicaEtapa1PosgradoPostitulo
): Promise<OAcademicaEtapa1PosgradoPostitulo> => {
  console.log("updateOAcademica updateando ando " + oAcademicaEtapa1PosgradoPostitulo.oa_sies_id);
  const { data } = await backEndApi.patch<OAcademica>(
    `/api/v1/updateoacademicaetapa1posgradopostitulo`,
    oAcademicaEtapa1PosgradoPostitulo
  );
  return data;
};

const updateOAcademicaEtapa3PosgradoPostitulo = async (
  oAcademicaEtapa3PosgradoPostitulo: OAcademicaEtapa3PosgradoPostitulo
): Promise<OAcademicaEtapa3PosgradoPostitulo> => {
  console.log("updateOAcademica updateando ando " + oAcademicaEtapa3PosgradoPostitulo.oa_sies_id);
  const { data } = await backEndApi.patch<OAcademica>(
    `/api/v1/updateoacademicaetapa3posgradopostitulo`,
    oAcademicaEtapa3PosgradoPostitulo
  );
  return data;
};

const useOAcademica = (oa_sies_id?: string) => {
  console.log("useOAcademica id de esto: ", oa_sies_id);
  const oAcademica = ref<OAcademica>();

  const { isLoading, data, isError } = useQuery({
    queryKey: ["oAcademica", oa_sies_id ?? ""],
    queryFn: () => getOAcademica(oa_sies_id ?? ""),
  });

  const proando = (proa: string) => {
    return useQuery({
      queryKey: ["oAcademica", proa],
      queryFn: () => getOAcademica(proa),
    });
  };

  watch(
    data,
    () => {
      if (data.value) oAcademica.value = { ...data.value };
    },
    { immediate: true }
  );

  const oAcademicaMutation = useMutation({ mutationFn: updateOAcademica });
  const oAcademicaEtapa1PregradoMutation = useMutation({
    mutationFn: updateOAcademicaEtapa1Pregrado,
  });
  const oAcademicaEtapa3PregradoMutation = useMutation({
    mutationFn: updateOAcademicaEtapa3Pregrado,
  });
  const oAcademicaEtapa1PosgradoPostituloMutation = useMutation({
    mutationFn: updateOAcademicaEtapa1PosgradoPostitulo,
  });
  const oAcademicaEtapa3PosgradoPostituloMutation = useMutation({
    mutationFn: updateOAcademicaEtapa3PosgradoPostitulo,
  });

  return {
    oAcademica,
    oAcademicaMutation,
    oAcademicaEtapa1PregradoMutation,
    oAcademicaEtapa3PregradoMutation,
    oAcademicaEtapa1PosgradoPostituloMutation,
    oAcademicaEtapa3PosgradoPostituloMutation,
    isError,
    isLoading,
    proando,

    // Method
    updateOAcademica: oAcademicaMutation.mutate,
    isUpdating: computed(() => oAcademicaMutation.isPending.value),
    isUpdatingSuccess: computed(() => oAcademicaMutation.isSuccess.value),
    isErrorUpdating: computed(() => oAcademicaMutation.isError.value),
    updateOAcademicaEtapa1Pregrado: oAcademicaEtapa1PregradoMutation.mutate,
    updateOAcademicaEtapa3Pregrado: oAcademicaEtapa3PregradoMutation.mutate,
    updateOAcademicaEtapa1PosgradoPostitulo: oAcademicaEtapa1PosgradoPostituloMutation.mutate,
    updateOAcademicaEtapa3PosgradoPostitulo: oAcademicaEtapa3PosgradoPostituloMutation.mutate,
    
  };
};

export default useOAcademica;
