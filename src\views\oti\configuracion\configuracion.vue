<template>
    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading">
            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <div class='surface-card shadow-3 p-3 border-round'>
                    <div style="display: flex;justify-content: space-between;align-items: center;padding-left: 0.1rem;">
                        <h2>Listado de comunas</h2>
                        <Button @click="dialogVisibleCrear = true">Nuevo</Button>
                    </div>
                    <div class="card">
                        <DataTable :value="lista_comuna" stripedRows showGridlines paginator :rows="10">
                            <Column field="nombre_comuna" header="Comuna"></Column>
                            <Column field="creador_email" header="Creado por"></Column>
                            <Column field="eliminar" header="Eliminar" :style="{ textAlign: 'center' }">
                                <template #body="slotProps">
                                    <Button icon="pi pi-trash" class="p-button-text p-button-danger"
                                        @click="dialogVisibleEliminar = true, comuna_id = slotProps.data.comuna_id" />
                                </template>
                            </Column>
                            <template #empty>
                                <div style="text-align: center;">No se encontraron registros.</div>
                            </template>
                        </DataTable>

                    </div>
                </div>
            </div>
        </div>
        <Dialog v-model:visible="dialogVisibleCrear" modal header="Crear Nueva Comuna" :style="{ width: '30rem' }"
            v-on:hide="resetForm()">
            <div v-if="dialogIsLoading && !comunaIsCorrect">
                <Loading />
            </div>

            <!-- Form to create a new report -->
            <div v-if="!dialogIsLoading && !comunaIsCorrect">
                <span class="p-text-secondary block mb-5">Esta comuna aparecerá en el formulario de "Infraestructura y
                    Recursos Institucionales".</span>
                <span class="p-text-secondary block mb-5"></span>

                <!-- Year input with validation -->
                <div class="flex align-items-center gap-3 mb-3">
                    <div>
                        <label for="anio" class="font-semibold">Nombre Comuna: </label>


                        <InputText v-model="nombreComuna" inputId="comuna" :maxlength="50" />
                    </div>
                </div>

                <!-- Validation error message -->
                <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}</div>

                <!-- Buttons -->
                <div class="flex justify-content-end gap-2">
                    <Button type="button" label="Cancelar" severity="secondary"
                        @click="dialogVisibleCrear = false"></Button>
                    <Button type="button" label="Crear" @click="crearNuevoComuna()"></Button>
                </div>
            </div>


            <!-- Success message -->
            <div v-if="!dialogIsLoading && comunaIsCorrect"
                style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
                <i class="pi pi-check" style="font-size: 5rem; color: blue;"></i>
                <span class="p-text-secondary block mb-5">¡Creación exitosa!</span>
            </div>
        </Dialog>

        <Dialog v-model:visible="dialogVisibleEliminar" modal header="Eliminar Comuna" :style="{ width: '25rem' }"
            v-on:hide="resetForm()">
            <div v-if="dialogIsLoading && !comunaIsCorrect">
                <Loading />
            </div>

            <div v-if="!dialogIsLoading && !comunaIsCorrect">
                <div>
                    <!-- Validation error message -->
                    <p>Esto eliminará permanentemente este registro ¿Está seguro?</p>
                </div>
                <br />
                <div class="flex justify-content-center gap-2">
                    <InputSwitch style="scale: 1.5;" v-model="deletePreConfirmation"></InputSwitch>
                </div>
                <br />
                <!-- Validation error message -->
                <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}<br /></div>


                <!-- Buttons -->
                <div class="flex justify-content-end gap-2">
                    <Button type="button" label="Cancelar" severity="secondary"
                        @click="dialogVisibleEliminar = false"></Button>
                    <Button v-if="deletePreConfirmation" type="button" label="Eliminar"
                        @click="eliminarComuna()"></Button>
                </div>
            </div>

            <!-- Success message -->
            <div v-if="!dialogIsLoading && comunaIsCorrect"
                style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
                <i class="pi pi-trash" style="font-size: 5rem; color: blue;"></i>
                <br />
                <span class="p-text-secondary block mb-5">¡Eliminado correctamente!</span>
            </div>

        </Dialog>
    </div>

</template>
<script>
import { useAuthStore } from '../../../store/auth';
import { ref, computed, onMounted, watch } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import axios from 'axios';
import { useToast } from 'primevue/usetoast';

export default {
    setup() {
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const globalLoading = computed(() => decryptData(authStore.isLoading));
        const userToken = computed(() => decryptData(authStore.idToken));
        const lista_comuna = ref([]);
        const isLoading = ref(false);
        const authStore = useAuthStore();
        const toast = useToast();
        const dialogVisibleCrear = ref(false);
        const dialogIsLoading = ref(false);
        const comunaIsCorrect = ref(false);
        const errorMessage = ref('');
        const nombreComuna = ref('');
        const comuna_id = ref(null);
        const apiUrl = import.meta.env.VITE_BACKEND_BASE_URL;
        const deletePreConfirmation = ref(false);
        const dialogVisibleEliminar = ref(false);

        const getAllComunas = async () => {
            isLoading.value = true;
            try {
                const response = await axios.get(apiUrl + "comunas-OTI", {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`,
                    }
                });
                lista_comuna.value = response.data;
            } catch (error) {
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Failed to fetch comunas', life: 5000 });
            } finally {
                isLoading.value = false;
            }
        };

        const resetForm = () => {
            nombreComuna.value = '';
            errorMessage.value = '';
            comunaIsCorrect.value = false;
            dialogIsLoading.value = false;
            dialogVisibleCrear.value = false;
            dialogVisibleEliminar.value = false;
            deletePreConfirmation.value = false;
        }

        const crearNuevoComuna = () => {
            isLoading.value = true;
            nombreComuna.value = nombreComuna.value.trim();
            console.log(nombreComuna.value);
            axios.post(apiUrl + "comunas-OTI", {
                nombre_comuna: nombreComuna.value,
            }, {
                headers: {
                    Authorization: `Bearer ${userToken.value}`,
                }
            }).then(response => {
                comunaIsCorrect.value = true;
                toast.add({ severity: 'success', summary: 'Success', group: 'bl', detail: 'Comuna creada correctamente', life: 5000 });
                getAllComunas(); // Refresh the list of comunas
            }).catch(error => {
                console.log(error)
                errorMessage.value = 'Error al craer comuna, intente nuevamente';
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: errorMessage.value, life: 5000 });
            }).finally(() => {
                dialogIsLoading.value = false;
            });
        }
        const eliminarComuna = () => {
            isLoading.value = true;
            axios.delete(apiUrl + "comunas-OTI/" + comuna_id.value, {
                headers: {
                    Authorization: `Bearer ${userToken.value}`,
                }
            }).then(response => {
                comunaIsCorrect.value = true;
                toast.add({ severity: 'success', summary: 'Success', group: 'bl', detail: 'Comuna eliminada correctamente', life: 5000 });
                getAllComunas(); // Refresh the list of comunas
            }).catch(error => {
                console.log(error)
                errorMessage.value = 'Error al eliminar comuna, intente nuevamente';
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: errorMessage.value, life: 5000 });
            }).finally(() => {
                dialogIsLoading.value = false;
            });
        }

        onMounted(async () => {
            isLoading.value = true;
            await getAllComunas();
            console.log(lista_comuna.value);
            isLoading.value = false;
        });

        // Watch para aplicar handleInput instantáneamente cuando nombreComuna cambia
        watch(nombreComuna, (newVal) => {
            nombreComuna.value = newVal.replace(/[^a-zA-Z ]/g, '') // Solo letras y espacios
                .replace(/\s+/g, ' ') // Un solo espacio entre palabras
                .trim(); // Quitar espacios al inicio y final
        });

        return {
            lista_comuna,
            globalLoading,
            isLoading,
            userEmail,
            userToken,
            dialogVisibleCrear,
            dialogIsLoading,
            resetForm,
            crearNuevoComuna,
            comunaIsCorrect,
            errorMessage,
            nombreComuna,
            deletePreConfirmation,
            dialogVisibleEliminar,
            eliminarComuna,
            comuna_id
        };

    },
}

</script>