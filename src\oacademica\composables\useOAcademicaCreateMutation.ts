import { computed, ref, watch } from "vue";
import { useMutation, useQuery } from "@tanstack/vue-query";
import backEndApi from "@/api/backEndApi";
import type { OAcademica } from "../interfaces/OAcademica";

const createOAcademica = async (
  oAcademica: OAcademica
): Promise<OAcademica> => {
  const { data } = await backEndApi.post<OAcademica>(
    `/api/v1/oacademica`,
    oAcademica
  );
  return data;
};

const useOAcademicaCreateMutation = () => {
    const oAcademica = ref<OAcademica>({
        oa_sies_id: '',
        cod_sede: '',
        nombre_sede: '',
        cod_carrera: '',
        nombre_carrera: '',
        modalidad: '',
        cod_jornada: '',
        version: '',
        cod_tipo_plan_carrera: '',
        caracteristicas_tipo_plan: '',
        duracion_estudios: '',
        duracion_titulacion: '',
        duracion_total: '',
        regimen: '',
        duracion_regimen: '',
        nombre_titulo: '',
        nombre_grado: '',
        cod_nivel_global: '',
        cod_nivel_carrera: '',
        cod_demre: '',
        anio_inicio: '',
        acreditacion: '',
        elegible_beca_pedagogia: '',
        ped_med_odont_otro: '',
        requisito_ingreso: '',
        semestres_reconocidos: '',
        area_actual: '',
        area_destino_agricultura: '',
        area_destino_ciencias: '',
        area_destino_cs_sociales: '',
        area_destino_educacion: '',
        area_destino_humanidades: '',
        area_destino_ingenieria: '',
        area_destino_salud: '',
        area_destino_servicios: '',
        ponderacion_nem: '',
        ponderacion_ranking: '',
        ponderacion_c_lectora: '',
        ponderacion_matematica_1: '',
        ponderacion_matematica_2: '',
        ponderacion_historia: '',
        ponderacion_ciencias: '',
        ponderacion_otros: '',
        vacantes_primer_semestre: '',
        vacantes_segundo_semestre: '',
        vacantes_pace: '',
        malla_curricular: '',
        perfil_egreso: '',
        texto_requerido_ingreso: '',
        otros_requisitos: '',
        mail_difusion_carrera: '',
        formato_valor: '',
        valor_matricula_anual: '',
        costo_titulacion: '',
        valor_certificado_diploma: '',
        arancel_anual: '',
        vigencia_carrera: ''
      });
  const oAcademicaCreateMutation = useMutation({
    mutationFn: createOAcademica,
  });

  return {
    oAcademica,
    oAcademicaCreateMutation,

    // Method
    createOAcademica: oAcademicaCreateMutation.mutate,
    isLoading: computed(() => oAcademicaCreateMutation.isPending.value),
    isCreateSuccess: computed(() => oAcademicaCreateMutation.isSuccess.value),
    isErrorCreating: computed(() => oAcademicaCreateMutation.isError.value)
  };
};

export default useOAcademicaCreateMutation;