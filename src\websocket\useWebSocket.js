// src/composables/useWebSocket.js
import { ref, onMounted, onUnmounted, inject, computed } from "vue";
import { encryptData, decryptData } from "@/utils/crypto";
import { useAuthStore } from "@/store/auth";
import axios from "axios";

let wsInstance = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;
const reconnectInterval = 3000;
let wasClosedManually = false;
const userEmail = ref(null);

const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
const API_WS_BASE_URL = import.meta.env.VITE_WS_BASE_URL;

export function useWebSocket(toast) {
  const authStore = useAuthStore();
  const msalInstance = inject("msalInstance");
  const isWebSocketOpen = ref(false);
  const isAuthenticated = ref(false);

  const connectWebSocket = async () => {
    if (wsInstance && wsInstance.readyState === WebSocket.OPEN) {
      console.log("WebSocket is already open.");
      isWebSocketOpen.value = true;
      return;
    }

    wsInstance = new WebSocket(API_WS_BASE_URL);

    wsInstance.onopen = () => {
      console.log("WebSocket connection opened");
      isWebSocketOpen.value = true;
      wasClosedManually = false;
      reconnectAttempts = 0;
      if (decryptData(authStore.userEmail) != null) {
        userEmail.value = decryptData(authStore.userEmail);
      }
      if (userEmail.value) {
        wsInstance.send(JSON.stringify({ email: userEmail.value }));
        fetchNotificationCount();
      } else {
        console.error("User email is not set.");
      }
    };

    wsInstance.onmessage = (event) => {
      const { message } = JSON.parse(event.data);
      console.log("Received message:", event);
      console.log("Received message:", message);
      if (message === "NEW_NOTIFICATION") {
        if (toast) {
          toast.add({
            severity: "info",
            summary: "Nueva Notificación",
            group: "bl",
            detail: "Tiene una nueva notificación en su bandeja",
            life: 3000,
          });
        }
        fetchNotificationCount();
      }
    };

    wsInstance.onclose = () => {
      console.log("WebSocket connection closed");
      isWebSocketOpen.value = false;
      if (!wasClosedManually) attemptReconnect();
    };

    wsInstance.onerror = (error) => {
      console.error("WebSocket error:", error);
      isWebSocketOpen.value = false;
      if (!wasClosedManually) attemptReconnect();
    };
  };

  const closeWebSocket = async () => {
    if (wsInstance) {
      console.log("Closing WebSocket connection");
      wasClosedManually = true;
      wsInstance.close();
      wsInstance = null;
      isWebSocketOpen.value = false;
    }
  };

  const attemptReconnect = () => {
    if (reconnectAttempts < maxReconnectAttempts) {
      reconnectAttempts++;
      setTimeout(connectWebSocket, reconnectInterval);
    } else {
      console.error("Max reconnection attempts reached.");
    }
  };

  const checkUserAuthentication = async () => {
    try {
      const isAuthenticated = computed(() =>
        decryptData(authStore.isAuthenticated)
      );
      if (isAuthenticated.value) {
        console.log("User is authenticated");
        isAuthenticated.value = true;
        connectWebSocket();
      } else {
        console.log("User is not authenticated");
      }
    } catch (error) {
      console.error("Error checking user authentication:", error);
    }
  };

  const handleLoginSuccess = () => {
    isAuthenticated.value = true;
    connectWebSocket();
  };

  const setupEventCallback = () => {
    if (msalInstance) {
      msalInstance.addEventCallback((event) => {
        if (event.eventType === "msal:loginSuccess") {
          userEmail.value = event.payload.account.username;
          handleLoginSuccess();
        } else if (event.eventType === "msal:loginFailure") {
          console.error("Login failed!", event);
        }
      });
    }
  };

  const fetchNotificationCount = async () => {
    try {
      const authStore = useAuthStore();
      if (!userEmail.value) return;
      const response = await axios.get(
        `${API_BASE_URL}notificacion/count/${userEmail.value}`
      );
      authStore.notificationNumber = encryptData(response.data.count);
      console.log(response.data.count);
    } catch (error) {
      console.error("Error fetching notification count:", error);
    }
  };

  onMounted(() => {
    setupEventCallback();
    checkUserAuthentication();
  });

  onUnmounted(() => {
    closeWebSocket();
  });

  return {
    isWebSocketOpen,
    isAuthenticated,
    closeWebSocket,
    connectWebSocket,
  };
}
