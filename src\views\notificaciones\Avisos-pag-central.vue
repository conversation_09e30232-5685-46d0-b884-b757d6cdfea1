<template>

    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading">
            <div class="surface-ground py-7 md:px-5 lg:px-6">
                <div class="card">
                    <TabView>
                        <TabPanel header="Mis Avisos">
                            <h2>Notificaciones</h2>
                            <AvisosList></AvisosList>

                        </TabPanel>
                        <TabPanel header="" :disabled="true">
                            <p class="m-0">

                            </p>
                        </TabPanel>
                        <TabPanel header="" :disabled="true">
                            <p class="m-0">

                            </p>
                        </TabPanel>
                        <TabPanel header="" :disabled="true">
                            <p class="m-0">

                            </p>
                        </TabPanel>
                        <TabPanel header="" :disabled="true">
                            <p class="m-0">

                            </p>
                        </TabPanel>
                        <TabPanel header="" :disabled="true">
                            <p class="m-0">

                            </p>
                        </TabPanel>
                        <TabPanel header="" :disabled="true">
                            <p class="m-0">

                            </p>
                        </TabPanel>
                        <TabPanel header="Crear Aviso">
                            <avisos></avisos>
                        </TabPanel>
                    </TabView>
                </div>

            </div>

        </div>
        <div v-else class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
            </div>
        </div>
        <Toast position="bottom-left" group="bl" />
    </div>


</template>

<script>
import { useAuthStore } from '../../store/auth';
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useToast } from "primevue/usetoast";
import { encryptData, decryptData } from '@/utils/crypto';
import avisos from './Avisos.vue';
import AvisosList from './Avisos-list.vue';

export default {
    components: {
        avisos,
        AvisosList
    },

    name: "Avisos-Central",

    setup() {
        const authStore = useAuthStore();
        const toast = useToast();

        // Obtain the stored user data from pinia and the global loading state
        const userName = computed(() => decryptData(authStore.userName));
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const permissions = computed(() => decryptData(authStore.permissions));
        const isUserAuthenticated = computed(() => decryptData(authStore.userIsAuthenticated));
        const globalLoading = computed(() => decryptData(authStore.isLoading));

        // Theme for changing the actual theme from dark to light or viceversa
        const theme = ref("dark");

        // Local "Loading" for the current view
        const isLoading = ref(false);
        const ws = ref(null);

        /**
         * Toast notification used to display success or error of the user inputs
         * @param severity type of toast notification
         * @param summary Title of the notification (success, info, warn, error)
         * @param detail description of the notification
         * @param life duration of the notification
         */
        const showToast = (severity, summary, detail, life) => {
            toast.add({ severity: severity, summary: summary, group: 'bl', detail: detail, life: life });
        };

        // Return data object
        return {
            theme,
            authStore,
            userName,
            userEmail,
            permissions,
            isUserAuthenticated,
            isLoading,
            globalLoading,
            showToast
        };
    }
}
</script>