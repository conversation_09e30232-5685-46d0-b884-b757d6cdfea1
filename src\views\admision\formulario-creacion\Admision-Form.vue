<template>
    <Toast position="bottom-left" group="bl" />
    <form @submit.prevent="submitForm">
        <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
                <h2>Ingresar nueva Matrícula:</h2>
                <div v-if="!isLoading">

                    <div class="formgrid grid" style="align-items:center; gap:0.5rem;">
                        <div class="field col-6 col-5 p-md-6 p-sm-12" style="display: grid;">
                            <label>Número de Rut:</label>
                            <InputText v-model="formData.estudianteRut" required :disabled="checkRutStatus"
                                type="number" maxlength="9"></InputText>
                        </div>
                        <div class="field p-md-6 p-sm-12" style="display: grid;">
                            <label>Digito Verificador:</label>
                            <InputText v-model="formData.estudianteDv" required :disabled="checkRutStatus" maxlength="1"
                                @input="validateCharacterDv"></InputText>
                        </div>
                        <div class="field p-md-12 p-sm-12" style="display: grid; align-self: self-end">
                            <div class="" style="display: flex;">
                                <div v-if="!loadingCheckRut && !rutExist">
                                    <Button label="Check Rut" type="submit" :disabled="checkRutStatus"
                                        style="gap:2rem"></Button>
                                </div>
                                <div v-if="formIsLoading && !estudianteData">
                                    <ProgressSpinner style="stroke:#2667d6; width: 50px; height: 50px" strokeWidth="2"
                                        animationDuration=".8s" aria-label="Custom ProgressSpinner" />
                                </div>
                                <i v-if="checkRutStatus" class="pi pi-check"
                                    style="font-size: 2rem;gap:2rem; color: green"></i>
                                <i v-if="checkRutStatus == false" class="pi pi-times"
                                    style="font-size: 2rem; color: darkred"></i>
                            </div>
                        </div>
                    </div>

                    <div v-if="!loadingCheckRut && rutExist" class="formgrid grid">
                        <div class="field col-6" v-for="(field, index) in formFormat1" :key="field.id">
                            <label :for="field.name">{{ field.displayName }}</label>
                            <InputText :class="{
                                'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                            }" v-if="field.contenedor === 'InputText'" :id=field.name :placeholder=field.placeholder
                                :type=field.type style="height:2.6rem" v-model="formData[field.name]"
                                :modelValue="field.modelValue">
                            </InputText>
                            <Dropdown :class="{
                                'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                            }" v-if="field.contenedor === 'dropdown'" v-model="formData[field.name]" filter
                                :placeholder="field.placeholder" style="height:2.6rem;margin-bottom: 1rem"
                                :options="getOptions(field.options)" :optionLabel="field.optionLabel" class="w-full">
                            </Dropdown>
                            <Calendar :class="{
                                'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                            }" v-if="field.contenedor === 'timeonly'" id="calendar-timeonly" timeOnly
                                class="w-full custom-dropdown" style="margin-top: 0.05rem;"
                                v-model="formData.hora_matricula" :placeholder="field.placeholder">
                            </Calendar>
                        </div>

                    </div>
                    <div v-if="!loadingCheckRut && rutExist" class="center">
                        <Button label="Subir Matricula" @click="submitForm()" />

                        <Dialog class="center" v-model:visible="visible" :closable=false modal
                            header="Subiendo Matricula" :style="{ width: '25rem' }">
                            <ProgressSpinner v-if="isResponseOk == null" class="center"
                                style="stroke:#2667d6;width: 100px; height: 100px" strokeWidth="3"
                                animationDuration=".8s" aria-label="Custom ProgressSpinner" />
                            <span v-if="isResponseOk == null" class="center">Espere</span>
                            <i v-if="isResponseOk == true" class="pi pi-check" style="font-size: 2rem;color: green"></i>
                            <span v-if="isResponseOk == true" class="center">Matricula cargada correctamente</span>
                            <i v-if="isResponseOk == false" class="pi pi-times"
                                style="font-size: 2rem;color: darkred"></i>
                            <span v-if="isResponseOk == false" class="center">Error al subir la matricula</span>

                            <br></br>

                            <div class="center">
                                <Button type="button" label="Confirmar" @click="visible = false"></Button>
                            </div>
                        </Dialog>
                    </div>
                </div>
                <div v-else>
                    <div class="center">
                        <i class="pi pi-spin pi-cog" style="font-size: 6rem"></i>
                        <br></br><br></br>
                    </div>
                    <div class="center"><label class="labelLoading">{{ loadingText }}</label></div>
                </div>
            </div>
        </div>
    </form>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useToast } from "primevue/usetoast";
import axios from 'axios';
import InputText from 'primevue/inputtext';
import Dropdown from 'primevue/dropdown';

export default {
    name: 'AdmisionForm',

    setup() {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const responseData = ref(null);
        const isResponseOk = ref(null);
        const visible = ref(false);
        const submitted = ref(false);
        const isLoading = ref(false);
        const loadingTexts = ['cargando', 'cargando.', 'cargando..', 'cargando...'];
        const loadingIndex = ref(0);
        const loadingText = ref(loadingTexts[0]);
        const toast = useToast();
        const formData = ref(
            {
                estudianteRut: null,
                estudianteDv: null,
                carrera: null,
                periodo_academico: null,
                situacion_matricula: "MATRICULADO",
                situacion_simbad_1: null,
                tipo_admision: null,
                fecha_matricula: null,
                hora_matricula: null,
                anio_ing_act: null,
                sem_ing_act: null,
                anio_ing_ori: null,
                sem_ing_ori: null,
                sit_fon_sol: null,
                reincorporacion: null,
                vigencia: null
            });
        const formFormat1 = ref([
            { id: 1, placeholder: 'Seleccionar Carrera', name: 'carrera', displayName: 'Nombre Carrera', type: 'text', contenedor: 'dropdown', options: 'carreraList', optionLabel: 'nombre_carrera' },
            { id: 2, placeholder: 'Ingresar periodo academico', name: 'periodo_academico', displayName: 'Periodo académico', type: 'text', contenedor: 'dropdown', options: 'periodo_academico_list', optionLabel: 'options' },
            { id: 3, placeholder: 'Ingresar situacion matricula', name: 'situacion_matricula', displayName: 'Situación matricula', type: 'text', contenedor: 'InputText' },
            { id: 4, placeholder: 'Seleccionar situacion simbad 1', name: 'situacion_simbad_1', displayName: 'Situación simbad 1', type: 'text', contenedor: 'dropdown', options: 'situacion_simbad_1_options', optionLabel: 'options' },
            { id: 5, placeholder: 'Ingresar tipo admision', name: 'tipo_admision', displayName: 'Tipo admisión', type: 'text', contenedor: 'dropdown', options: 'tipo_admision_list', optionLabel: 'options' },
            { id: 6, placeholder: 'Ingresar fecha', name: 'fecha_matricula', displayName: 'Fecha matricula', type: 'date', contenedor: 'InputText' },
            { id: 7, placeholder: 'ej: "18:25"', name: 'hora_matricula', displayName: 'Hora matricula', type: 'text', contenedor: 'timeonly' },
            { id: 8, placeholder: 'Ingresar año ingreso actual', name: 'anio_ing_act', displayName: 'Año ingreso actual', type: 'text', contenedor: 'dropdown', options: 'anioIngresoList', optionLabel: 'options' },
            { id: 9, placeholder: 'Seleccionar semestre ingreso actual', name: 'sem_ing_act', displayName: 'Semestre ingreso actual', type: 'text', contenedor: 'dropdown', options: 'semestre_ingreso', optionLabel: 'options' },
            { id: 10, placeholder: 'Ingresar año ingreso original', name: 'anio_ing_ori', displayName: 'Año ingreso original', type: 'text', contenedor: 'dropdown', options: 'anioIngresoList', optionLabel: 'options' },
            { id: 11, placeholder: 'Ingresar semestre ingreso original', name: 'sem_ing_ori', displayName: 'Semestre ingreso original', type: 'number', contenedor: 'dropdown', options: 'semestre_ingreso', optionLabel: 'options' },
            { id: 12, placeholder: 'Ingresar Situación fondo solidario', name: 'sit_fon_sol', displayName: 'Situación fondo solidario', type: 'text', contenedor: 'dropdown', options: 'situacion_fondo_solidario_list', optionLabel: 'options' },
            { id: 13, placeholder: 'Ingresar reincorporacion', name: 'reincorporacion', displayName: 'Reincorporación', type: 'text', contenedor: 'dropdown', options: 'reincorporacion_list', optionLabel: 'options' },
            { id: 14, placeholder: 'Ingresar vigencia', name: 'vigencia', displayName: 'Vigencia', type: 'text', contenedor: 'dropdown', options: 'vigenciaList', optionLabel: 'options' },
        ]);
        const rutExist = ref(false);
        const loadingCheckRut = ref(false);
        const checkRutStatus = ref(null);
        const checkDvStatus = ref(null);
        const formIsLoading = ref(false);
        const estudianteData = ref(null);
        const carreraList = ref(null);
        const situacion_simbad_1_options = ref([
            { options: 'REGUL/REGULAR' },
            { options: 'RENUNC/RENUNC' },
        ]);
        const semestre_ingreso = ref([
            { options: 'Semestre 1', value: 1 },
            { options: 'Semestre 2', value: 2 },
        ]);
        const vigenciaList = ref([
            { options: 'Estudiante sin matrícula', value: 0 },
            { options: 'Estudiante con matrícula vigente', value: 1 },
            { options: 'Estudiante egresado con matrícula vigente', value: 2 },
        ]);
        const tipo_admision_list = ref([
            { options: "Ingreso Directo (regular)", value: 1 },
            { options: "Continuidad de Plan Común o Bachillerato", value: 2 },
            { options: "Cambio Interno", value: 3 },
            { options: "Cambio Externo", value: 4 },
            { options: "Ingreso por Reconocimiento de Aprendizajes Previos", value: 5 },
            { options: "Ingreso especial para estudiantes extranjeros", value: 6 },
            { options: "Ingreso a través del Programa PACE", value: 7 },
            { options: "Ingreso a través de Programas de Inclusión", value: 8 },
            { options: "Acceso por características especiales (Deportista, Artista, Hijo de profesional de la IES, Pueblos Originarios o Diplomáticos)", value: 9 },
            { options: "Otras formas de ingreso", value: 10 },
            { options: "Articulación de TNS a carrera profesional", value: 11 },
        ]);
        const anioIngresoList = ref([
            { options: 2000 },
            { options: 2001 },
            { options: 2002 },
            { options: 2003 },
            { options: 2004 },
            { options: 2005 },
            { options: 2006 },
            { options: 2007 },
            { options: 2008 },
            { options: 2009 },
            { options: 2010 },
            { options: 2011 },
            { options: 2012 },
            { options: 2013 },
            { options: 2014 },
            { options: 2015 },
            { options: 2016 },
            { options: 2017 },
            { options: 2018 },
            { options: 2019 },
            { options: 2020 },
            { options: 2021 },
            { options: 2022 },
            { options: 2023 },
            { options: 2024 },
            { options: 2025 },
            { options: 2026 },
            { options: 2027 },
            { options: 2028 },
        ]);
        const situacion_fondo_solidario_list = ref([
            { options: "No cumple / No aplica", value: 0 },
            { options: "Si cumple", value: 1 },
            { options: "No presenta documentación", value: 2 },
        ]);
        const reincorporacion_list = ref([
            { options: "No se reincorpora / No aplica", value: 0 },
            { options: "Si se reincorpora", value: 1 },
        ]);
        const periodo_academico_list = ref([
            { options: "2024 - ANUAL" },
        ]);
        const dataMatriculaFinal = ref({});

        /**
         * Toast notification used to display success or error of the user inputs
         * @param severity type of toast notification
         * @param summary Title of the notification (success, info, warn, error)
         * @param detail description of the notification
         * @param life duration of the notification
         */
        const showToast = (severity, summary, detail, life) => {
            toast.add({ severity: severity, summary: summary, group: 'bl', detail: detail, life: life });
        };

        /**
         * Update the string of the loading text, when the page is loading
         */
        const updateLoadingText = () => {
            setInterval(() => {
                loadingIndex.value = (loadingIndex.value + 1) % loadingTexts.length;
                loadingText.value = loadingTexts[loadingIndex.value];
            }, 500);
        };
        onMounted(() => {
            updateLoadingText();
        });

        /** 
        * Check if the given rut exists in the database
        */
        const checkEstudianteExist = async (estudianteRut) => {
            try {
                loadingCheckRut.value = true;
                const response = await axios.get(API_BASE_URL + "estudiante/" + estudianteRut);

                if (response.status === 200) {
                    console.log('Estudiante Encontrado');
                    rutExist.value = true;
                    estudianteData.value = response.data;
                }
            } catch (error) {
                if (error.response && error.response.status === 404) {
                    console.log('Estudiante NO Encontrado');

                    // Handle UI or other logic for student not found
                } else {
                    console.error('Error fetching data:', error);
                    error.value = 'Error fetching data';
                }
                rutExist.value = false;

            } finally {
                console.log("Finally");
                loadingCheckRut.value = false;
            }
        }

        const submitForm = async () => {
            formIsLoading.value = true;
            console.log('Form submitted with data:');

            // Check if required fields are filled
            if (!checkRutStatus.value && !loadingCheckRut.value && formData.value.estudianteRut && !submitted.value) {

                await checkEstudianteExist(formData.value.estudianteRut + formData.value.estudianteDv);
                await getCarreraData();
                if (estudianteData.value != null && carreraList.value != null) {
                    checkRutStatus.value = true;
                    formIsLoading.value = false;
                } else {
                    checkRutStatus.value = false;
                    formIsLoading.value = null;
                }

            } else {

                if (Object.values(formData.value).some(value => value === null || value === '')) {
                    // The form has empty values
                    console.log("empty values");
                    console.log(formData.value);
                    showToast("error", "Campos Vacios", "Se detectaron campos vacios", "4000");
                } else {
                    // The form is complete
                    console.log("NO EMPTY VALUES detected");
                    visible.value = true;

                    // Set the "Hour : minutes" format
                    const date = new Date(formData.value.hora_matricula);
                    const hours = date.getHours().toString().padStart(2, '0');
                    const minutes = date.getMinutes().toString().padStart(2, '0');
                    const formattedTime = `${hours}:${minutes}`;

                    // Set the variables year month day
                    const date2 = new Date(formData.value.fecha_matricula);
                    const year = date2.getFullYear();
                    const month = (date2.getMonth() + 1).toString().padStart(2, '0');
                    const day = date2.getDate().toString().padStart(2, '0');

                    console.log(formattedTime); // Output: "14:50"

                    dataMatriculaFinal.value.estudiante_id = estudianteData.value.estudiante_id
                    dataMatriculaFinal.value.carrera_id = formData.value.carrera.carrera_id
                    dataMatriculaFinal.value.periodo_academico = formData.value.periodo_academico.options
                    dataMatriculaFinal.value.situacion_matricula = formData.value.situacion_matricula
                    dataMatriculaFinal.value.situacion_simbad_1 = formData.value.situacion_simbad_1.options
                    dataMatriculaFinal.value.tipo_admision = formData.value.tipo_admision.value
                    dataMatriculaFinal.value.fecha_matricula = formData.value.fecha_matricula
                    dataMatriculaFinal.value.hora_matricula = formattedTime
                    dataMatriculaFinal.value.anio_ing_act = formData.value.anio_ing_act.options
                    dataMatriculaFinal.value.sem_ing_act = formData.value.sem_ing_act.value
                    dataMatriculaFinal.value.anio_ing_ori = formData.value.anio_ing_ori.options
                    dataMatriculaFinal.value.sem_ing_ori = formData.value.sem_ing_ori.value
                    dataMatriculaFinal.value.sit_fon_sol = formData.value.sit_fon_sol.value
                    dataMatriculaFinal.value.reincorporacion = formData.value.reincorporacion.value
                    dataMatriculaFinal.value.vigencia = formData.value.vigencia.value
                    dataMatriculaFinal.value.anio_matricula = year;
                    dataMatriculaFinal.value.mes_matricula = month;
                    dataMatriculaFinal.value.dia_matricula = day;

                    console.log(dataMatriculaFinal.value);

                    await postMatricula();

                    if (responseData.value == 201) {
                        showToast("success", "Matricula Creada", "Se ingreso correctamente la matricula", "4000");
                        isResponseOk.value = true;

                    } else if (responseData.value == 400 || 404 || 500) {
                        isResponseOk.value = false;
                    } else {
                        isResponseOk.value = false;
                    }


                }
                submitted.value = true;
            }

        }
        /**
        * Get the Matriculas data from the back-end to poblate the DataTable
        */
        const getCarreraData = async () => {
            try {
                isLoading.value = true;
                const response = await axios.get(API_BASE_URL + "carrera/");
                const filteredData = response.data.filter(item => {
                    return item.vigencia === "Vigente con Alumnos Nuevos";
                });
                carreraList.value = filteredData;

            } catch (error) {
                console.log("Error: " + error);
                error.value = 'Error fetching data';
            } finally {
                isLoading.value = false;
            }
        }

        /**
        * Post the data of matricula to the back-end 
        */
        const postMatricula = async () => {
            try {
                isLoading.value = true;
                const response = await axios.post(API_BASE_URL + "matricula", dataMatriculaFinal.value)
                console.log("respuesta");
                console.log(response.data);
                if (response.data.status == 201) {
                    responseData.value = response.data.status
                }
            } catch (error) {
                console.log(error);
                responseData.value = error.response.status
                console.log("Error: " + error);
                error.value = 'Error posting data';
            } finally {
                console.log("Finally");
                isLoading.value = false;
            }
        }

        /**
         * Get options for the dropdown field based on the field options string
        */
        const getOptions = (optionKey) => {
            if (optionKey === 'carreraList') {
                return carreraList.value;
            } else if (optionKey === 'situacion_simbad_1_options') {
                return situacion_simbad_1_options.value;
            } else if (optionKey === 'semestre_ingreso') {
                return semestre_ingreso.value;
            } else if (optionKey === 'vigenciaList') {
                return vigenciaList.value;
            } else if (optionKey === 'tipo_admision_list') {
                return tipo_admision_list.value;
            } else if (optionKey === 'anioIngresoList') {
                return anioIngresoList.value;
            } else if (optionKey === 'situacion_fondo_solidario_list') {
                return situacion_fondo_solidario_list.value;
            } else if (optionKey === 'reincorporacion_list') {
                return reincorporacion_list.value;
            } else if (optionKey === 'periodo_academico_list') {
                return periodo_academico_list.value;
            }

            return [];
        }

        return {
            submitted,
            showToast,
            isLoading,
            loadingTexts,
            loadingIndex,
            loadingText,
            formData,
            formFormat1,
            checkEstudianteExist,
            rutExist,
            loadingCheckRut,
            checkRutStatus,
            checkDvStatus,
            submitForm,
            formIsLoading,
            estudianteData,
            getCarreraData,
            carreraList,
            situacion_simbad_1_options,
            semestre_ingreso,
            getOptions,
            vigenciaList,
            situacion_fondo_solidario_list,
            reincorporacion_list,
            periodo_academico_list,
            postMatricula,
            dataMatriculaFinal,
            visible,
            isResponseOk
        }

    },
    methods: {
        validateCharacterDv(event) {
            const input = event.target.value;
            const isNumber = /^[0-9]$/;
            const isLetter = /^[kK]$/;

            if (isNumber.test(input)) {
                // Handle number input
                this.formData.estudianteDv = input;
            } else if (isLetter.test(input)) {
                // Handle letter input
                event.target.value = input.toUpperCase();
                this.formData.estudianteDv = input.toUpperCase();
            } else {
                // Handle invalid input
                event.target.value = null;
                this.formData.estudianteDv = null;
            }
        }

    }

};


</script>

<style>
.p-dropdown-panel {
    /* Adjust width as needed */
    white-space: initial;
    overflow: hidden;
    text-overflow: ellipsis;

}

.p-dropdown-label {
    line-height: 1px;
    /* Adjust as necessary */
    padding-top: 1.2rem;
    /* Adjust as necessary */

}

.labelLoading {
    padding-left: 0.6rem;
}


.pi-spin {
    color: #0071e3;
}
</style>