import { computed, ref, watch } from 'vue'
import { useMutation, useQuery } from '@tanstack/vue-query'

import backEndApi from '@/api/backEndApi'
import type { Admision } from '../interfaces/admision'

const getAdmision = async (id: string): Promise<Admision> => {
  const { data } = await backEndApi.get(`/api/v1/matricula/admbyid/${id}`)
  return data
}

const updateAdmision = async (admision: Admision): Promise<Admision> => {
  const { data } = await backEndApi.patch<Admision>(`/api/v1/matricula/${admision.matId}`, admision)
  return data
}

const useAdmision = (id: string) => {
  const admision = ref<Admision>()

  const { isLoading, data, isError } = useQuery({
    queryKey: ['admision', id],
    queryFn: () => getAdmision(id),
    retry: false
  })

  const admisionMutation = useMutation({ mutationFn: updateAdmision })

  watch(
    data,
    () => {
      if (data.value) admision.value = { ...data.value }
    },
    { immediate: true }
  )

  return {
    admision: admision,
    admisionMutation: admisionMutation,
    isError,
    isLoading,

    // Method
    updateAdmision: admisionMutation.mutate,
    isUpdating: computed(() => admisionMutation.isPending.value),
    isUpdatingSuccess: computed(() => admisionMutation.isSuccess.value),
    isErrorUpdating: computed(() => admisionMutation.isError.value)
  }
}

export default useAdmision
