import { ref } from 'vue'
import { defineStore } from 'pinia'
import type { Admision } from '@/admision2/interfaces/admision'

export const useAdmisionStore = defineStore('admision', () => {
  const admisiones = ref<Admision[]>([])
  const totalRecords = ref(0)

  return {
    //state
    admisiones,
    totalRecords,

    //actions
    setAdmisiones(newAdmisiones: Admision[]) {
      admisiones.value = newAdmisiones
    }
  }
})
