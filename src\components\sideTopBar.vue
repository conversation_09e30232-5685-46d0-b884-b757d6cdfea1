<template>
    <!--Navigation Bar-->
    <Menubar :model="items" breakpoint="320px">

        <template #start>

            <router-link to="/">
                <img src="/src/assets/logo-uantofagasta.svg" alt="logo" style="margin-right: 2rem;" @url="base" />
            </router-link>
        </template>
        <template class="option-container" #item="{ item }">
            <div v-if="isLoggedIn" style="align-content: center; display: flex; align-items: center;">
                <span
                    v-if="item.label !== 'Notificaciones' || userRole.some(role => role.rol === 'Administrador Sistema')"
                    :class="item.icon" style="font-size: 2rem; margin-right: 0.5rem;"></span>
                <span class="font-bold" style="margin: 1rem; display: inline-flex; align-items: center;"
                    v-if="item.label !== 'Notificaciones' || userRole.some(role => role.rol === 'Administrador Sistema')">
                    {{ item.label }}
                </span>
                <Badge
                    v-if="(item.label !== 'Notificaciones' || userRole.some(role => role.rol === 'Administrador Sistema')) && shouldShowBadge(item.badge)"
                    :class="['badge-custom', { 'ml-auto': !root, 'ml-2': root, 'badge-large': item.badge === '+9' }]"
                    :value="formatBadgeValue(item.badge)" />
            </div>
        </template>
        <template #end>
            <div style="display: flex; justify-content: flex-end; align-items: center;">
                <!-- Conditionally render the logout button if the user is logged in -->
                <div v-if="isLoggedIn" @click="logout" style="cursor: pointer;">
                    <span class="pi pi-sign-out" style="font-size: 1.5rem; margin-right: 1rem; color: white;"></span>
                    <span class="font-bold" style="margin-right: 1rem; color: white;">Cerrar Sesión</span>
                </div>
            </div>

        </template>

    </Menubar>


    <!--Side Bar-->
    <Sidebar v-model:visible="visible" style="background-color: ">
        <template #container="{ closeCallback }">
            <div class="flex flex-column h-full">
                <div class="flex align-items-center justify-content-between px-4 pt-3 flex-shrink-0">
                    <!--LOGO-->
                    <span class="inline-flex align-items-center gap-2">
                        <svg width="35" height="40" viewBox="0 0 35 40" fill="none" xmlns="http://www.w3.org/2000/svg"
                            url="/">
                            <path
                                d="M25.87 18.05L23.16 17.45L25.27 20.46V29.78L32.49 23.76V13.53L29.18 14.73L25.87 18.04V18.05ZM25.27 35.49L29.18 31.58V27.67L25.27 30.98V35.49ZM20.16 17.14H20.03H20.17H20.16ZM30.1 5.19L34.89 4.81L33.08 12.33L24.1 15.67L30.08 5.2L30.1 5.19ZM5.72 14.74L2.41 13.54V23.77L9.63 29.79V20.47L11.74 17.46L9.03 18.06L5.72 14.75V14.74ZM9.63 30.98L5.72 27.67V31.58L9.63 35.49V30.98ZM4.8 5.2L10.78 15.67L1.81 12.33L0 4.81L4.79 5.19L4.8 5.2ZM24.37 21.05V34.59L22.56 37.29L20.46 39.4H14.44L12.34 37.29L10.53 34.59V21.05L12.42 18.23L17.45 26.8L22.48 18.23L24.37 21.05ZM22.85 0L22.57 0.69L17.45 13.08L12.33 0.69L12.05 0H22.85Z"
                                fill="var(--primary-color)" />
                            <path
                                d="M30.69 4.21L24.37 4.81L22.57 0.69L22.86 0H26.48L30.69 4.21ZM23.75 5.67L22.66 3.08L18.05 14.24V17.14H19.7H20.03H20.16H20.2L24.1 15.7L30.11 5.19L23.75 5.67ZM4.21002 4.21L10.53 4.81L12.33 0.69L12.05 0H8.43002L4.22002 4.21H4.21002ZM21.9 17.4L20.6 18.2H14.3L13 17.4L12.4 18.2L12.42 18.23L17.45 26.8L22.48 18.23L22.5 18.2L21.9 17.4ZM4.79002 5.19L10.8 15.7L14.7 17.14H14.74H15.2H16.85V14.24L12.24 3.09L11.15 5.68L4.79002 5.2V5.19Z"
                                fill="var(--text-color)" />
                        </svg>
                        <span class="font-semibold text-2xl text-primary">Logo Depto</span>
                    </span>
                    <!--Boton cierre modal-->
                    <span>
                        <Button type="button" @click="closeCallback" icon="pi pi-times" rounded outlined
                            class="h-2rem w-2rem"></Button>
                    </span>
                </div>
                <div class="overflow-y-auto mt-5">
                    <ul class="list-none p-0 m-2 pl-0 overflow-hidden">
                        <li>
                            <a v-ripple @click="redirectToAnotherPage('Inicio')"
                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                <i class="mr-2"></i>
                                <span class="font-medium">Inicio</span>
                            </a>
                        </li>
                        <!-- Admisión with Submenu Admision and Bienestar-->
                        <!--<li>
                            <a v-ripple v-styleclass="{
                                selector: '@next',
                                enterClass: 'hidden',
                                enterActiveClass: 'slidedown',
                                leaveToClass: 'hidden',
                                leaveActiveClass: 'slideup'
                            }"
                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                <i class="mr-2"></i>
                                <span class="font-medium">Admisión</span>
                                <i class="pi pi-chevron-down ml-auto"></i>
                            </a>
                            <ul
                                class="list-none py-0 pl-3 pr-0 m-0 hidden overflow-y-hidden transition-all transition-duration-400 transition-ease-in-out">
                                <li>
                                    <a v-ripple v-styleclass="{
                                        selector: '@next',
                                        enterClass: 'hidden',
                                        enterActiveClass: 'slidedown',
                                        leaveToClass: 'hidden',
                                        leaveActiveClass: 'slideup'
                                    }"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="mr-2"></i>
                                        <span class="font-medium">Admisión</span>
                                        <i class="pi pi-chevron-down ml-auto"></i>
                                    </a>
                                    <ul
                                        class="list-none py-0 pl-3 pr-0 m-0 hidden overflow-y-hidden transition-all transition-duration-400 transition-ease-in-out">
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('Matrículas-Form')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-plus-circle mr-2"></i>
                                                <span class="font-medium">Ingresar matrícula</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('tabs')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-plus-circle mr-2"></i>
                                                <span class="font-medium">Cargar .csv</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('Admision-View')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-chart-bar mr-2"></i>
                                                <span class="font-medium">Filtro 1</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-chart-bar mr-2"></i>
                                                <span class="font-medium">Filtro 2</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-chart-bar mr-2"></i>
                                                <span class="font-medium">Filtro 3</span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                                <li>
                                    <a v-ripple v-styleclass="{
                                        selector: '@next',
                                        enterClass: 'hidden',
                                        enterActiveClass: 'slidedown',
                                        leaveToClass: 'hidden',
                                        leaveActiveClass: 'slideup'
                                    }"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="mr-2"></i>
                                        <span class="font-medium">Bienestar</span>
                                        <i class="pi pi-chevron-down ml-auto"></i>
                                    </a>
                                    <ul
                                        class="list-none py-0 pl-3 pr-0 m-0 hidden overflow-y-hidden transition-all transition-duration-400 transition-ease-in-out">
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('Matrículas-Form')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-plus-circle mr-2"></i>
                                                <span class="font-medium">Ingresar matrícula</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('tabs')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-plus-circle mr-2"></i>
                                                <span class="font-medium">Cargar .csv</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('Admision-View')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-chart-bar mr-2"></i>
                                                <span class="font-medium">Filtro 1</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-chart-bar mr-2"></i>
                                                <span class="font-medium">Filtro 2</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-chart-bar mr-2"></i>
                                                <span class="font-medium">Filtro 3</span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </li>-->

                        <!-- Prueba para verificar los accesos del usuario-->
                        <!-- <li v-if="userRole.some(role => role.rol === 'Administrador Sistema')">
                            <a v-ripple v-styleclass="{
                                selector: '@next',
                                enterClass: 'hidden',
                                enterActiveClass: 'slidedown',
                                leaveToClass: 'hidden',
                                leaveActiveClass: 'slideup'
                            }" @click="redirectToAnotherPage('Admin')"
                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                <i class="mr-2"></i>
                                <span class="font-medium">Administración
                                    Sitio</span>
                            </a>

                        </li>-->
                        <li
                            v-if="userRole.some(role => role.recurso === 'Administracion PIU') && userRole.some(role => role.rol === 'Administrador Sistema')">
                            <a v-ripple v-styleclass="{
                                selector: '@next',
                                enterClass: 'hidden',
                                enterActiveClass: 'slidedown',
                                leaveToClass: 'hidden',
                                leaveActiveClass: 'slideup'
                            }"
                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                <i class="mr-2"></i>
                                <span class="font-medium">Servicio de Bienestar Estudiantil</span>
                                <i class="pi pi-chevron-down ml-auto"></i>
                            </a>
                            <ul
                                class="list-none py-0 pl-3 pr-0 m-0 hidden overflow-y-hidden transition-all transition-duration-400 transition-ease-in-out">
                                <li>
                                    <a v-ripple v-styleclass="{
                                        selector: '@next',
                                        enterClass: 'hidden',
                                        enterActiveClass: 'slidedown',
                                        leaveToClass: 'hidden',
                                        leaveActiveClass: 'slideup'
                                    }"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="mr-2"></i>
                                        <span class="font-medium">Matrícula SIES</span>
                                        <i class="pi pi-chevron-down ml-auto mr-5"></i>
                                    </a>
                                    <ul
                                        class="list-none py-0 pl-3 pr-0 m-0 hidden overflow-y-hidden transition-all transition-duration-400 transition-ease-in-out">
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('Admision-View')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-list mr-2"></i>
                                                <span class="font-medium">Etapa 1</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-list mr-2"></i>
                                                <span class="font-medium">Etapa 2</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-list mr-2"></i>
                                                <span class="font-medium">Etapa 3</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-list mr-2"></i>
                                                <span class="font-medium">Etapa 4</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-list mr-2"></i>
                                                <span class="font-medium">Etapa 5</span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                                <li>
                                    <a v-ripple v-styleclass="{
                                        selector: '@next',
                                        enterClass: 'hidden',
                                        enterActiveClass: 'slidedown',
                                        leaveToClass: 'hidden',
                                        leaveActiveClass: 'slideup'
                                    }"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="mr-2"></i>
                                        <span class="font-medium">Estudiantes</span>
                                        <i class="pi pi-chevron-down ml-auto mr-5"></i>
                                    </a>
                                    <ul
                                        class="list-none py-0 pl-3 pr-0 m-0 hidden overflow-y-hidden transition-all transition-duration-400 transition-ease-in-out">
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('Matrículas-Form')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-list mr-2"></i>
                                                <span class="font-medium">Facultad 1</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('Matrículas-Form')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-list mr-2"></i>
                                                <span class="font-medium">Facultad 2</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('Matrículas-Form')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-list mr-2"></i>
                                                <span class="font-medium">Facultad 3</span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                                <li>
                                    <a v-ripple v-styleclass="{
                                        selector: '@next',
                                        enterClass: 'hidden',
                                        enterActiveClass: 'slidedown',
                                        leaveToClass: 'hidden',
                                        leaveActiveClass: 'slideup'
                                    }"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="mr-2"></i>
                                        <span class="font-medium">Cargar matrículas</span>
                                        <i class="pi pi-chevron-down ml-auto mr-5"></i>
                                    </a>
                                    <ul
                                        class="list-none py-0 pl-3 pr-0 m-0 hidden overflow-y-hidden transition-all transition-duration-400 transition-ease-in-out">
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('Matrículas-Form')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-plus-circle mr-2"></i>
                                                <span class="font-medium">Ingresar matrícula</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a v-ripple @click="redirectToAnotherPage('tabs')"
                                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                                <i class="pi pi-file mr-2"></i>
                                                <span class="font-medium">Cargar .csv</span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>


                            </ul>
                        </li>
                        <!-- Rutas Matricula Unificada -->
                        <li
                            v-if="userRole.some(role => role.rol === 'Encargado' || role.rol === 'Administrador Sistema')">
                            <a v-ripple v-styleclass="{
                                selector: '@next',
                                enterClass: 'hidden',
                                enterActiveClass: 'slidedown',
                                leaveToClass: 'hidden',
                                leaveActiveClass: 'slideup'
                            }"
                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                <i class="mr-2"></i>
                                <span class="font-medium">Matrícula Unificada</span>
                                <i class="pi pi-chevron-down ml-auto"></i>
                            </a>
                            <ul
                                class="list-none py-0 pl-3 pr-0 m-0 hidden overflow-y-hidden transition-all transition-duration-400 transition-ease-in-out">
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('PP-MU-pregrado')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-list mr-2"></i>
                                        <span class="font-medium">Pregrado</span>
                                    </a>
                                </li>
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('PP-MU-postgrado-postitulo')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-list mr-2"></i>
                                        <span class="font-medium">Postgrado - Postítulo</span>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li
                            v-if="userRole.some(role => role.rol === 'Encargado' || role.rol === 'Administrador Sistema')">
                            <a v-ripple v-styleclass="{
                                selector: '@next',
                                enterClass: 'hidden',
                                enterActiveClass: 'slidedown',
                                leaveToClass: 'hidden',
                                leaveActiveClass: 'slideup'
                            }"
                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                <i class="mr-2"></i>
                                <span class="font-medium">Oferta Académica</span>
                                <i class="pi pi-chevron-down ml-auto"></i>
                            </a>
                            <ul
                                class="list-none py-0 pl-3 pr-0 m-0 hidden overflow-y-hidden transition-all transition-duration-400 transition-ease-in-out">
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('OfertaAcademicaView')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-list mr-2"></i>
                                        <span class="font-medium">Etapa Actual</span>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <!--Rutas Ficha CNA-->
                        <li
                            v-if="userRole.some(role => role.recurso === 'Administracion PIU') && userRole.some(role => role.rol === 'Administrador Sistema')">
                            <a v-ripple v-styleclass="{
                                selector: '@next',
                                enterClass: 'hidden',
                                enterActiveClass: 'slidedown',
                                leaveToClass: 'hidden',
                                leaveActiveClass: 'slideup'
                            }" @click="redirectToAnotherPage('ficha-cna')"
                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                <i class="mr-2"></i>
                                <span class="font-medium">Ficha CNA</span>                                
                            </a>
                        </li>
                        <!--Rutas OTI-->
                        <li
                            v-if="userRole.some(role => role.recurso === 'OTI' || role.recurso === 'Administracion PIU') && userRole.some(role => role.rol === 'Encargado' || role.rol === 'Administrador Sistema' || role.rol === 'Usuario General')">
                            <a v-ripple v-styleclass="{
                                selector: '@next',
                                enterClass: 'hidden',
                                enterActiveClass: 'slidedown',
                                leaveToClass: 'hidden',
                                leaveActiveClass: 'slideup'
                            }"
                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                <i class="mr-2"></i>
                                <span class="font-medium">Infraestructura y recursos Institucionales</span>
                                <i class="pi pi-chevron-down ml-auto"></i>
                            </a>
                            <ul
                                class="list-none py-0 pl-3 pr-0 m-0 hidden overflow-y-hidden transition-all transition-duration-400 transition-ease-in-out">
                                <li
                                    v-if="userRole.some(role => role.recurso === 'OTI' || role.recurso === 'Administracion PIU') && userRole.some(role => role.rol === 'Encargado' || role.rol === 'Administrador Sistema' || role.rol === 'Usuario General')">
                                    <a v-ripple @click="redirectToAnotherPage('Pagina principal oti')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-list mr-2"></i>
                                        <span class="font-medium">Consultar registros</span>
                                    </a>
                                </li>
                                <li
                                    v-if="userRole.some(role => role.recurso === 'OTI' || role.recurso === 'Administracion PIU') && userRole.some(role => role.rol === 'Encargado' || role.rol === 'Administrador Sistema')">
                                    <a v-ripple @click="redirectToAnotherPage('Configuracion oti')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-list mr-2"></i>
                                        <span class="font-medium">Configuración</span>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <!-- Rutas Oferta Academica Sies David Meza -->
                        <li
                            v-if="userRole.some(role => role.rol === 'Encargado' || role.rol === 'Administrador Sistema')">
                            <a v-ripple v-styleclass="{
                                selector: '@next',
                                enterClass: 'hidden',
                                enterActiveClass: 'slidedown',
                                leaveToClass: 'hidden',
                                leaveActiveClass: 'slideup'
                            }"
                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                <i class="mr-2"></i>
                                <span class="font-medium">Oferta Academica Sies</span>
                                <i class="pi pi-chevron-down ml-auto"></i>
                            </a>
                            <ul
                                class="list-none py-0 pl-3 pr-0 m-0 hidden overflow-y-hidden transition-all transition-duration-400 transition-ease-in-out">
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('oferta-academica-sies')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-list mr-2"></i>
                                        <span class="font-medium">Carga Inicial</span>
                                    </a>
                                </li>

                            </ul>
                        </li>
                        <!-- Rutas Administracion Usuarios -->
                        <li
                            v-if="userRole.some(role => role.rol === 'Encargado' || role.rol === 'Administrador Sistema')">
                            <a v-ripple v-styleclass="{
                                selector: '@next',
                                enterClass: 'hidden',
                                enterActiveClass: 'slidedown',
                                leaveToClass: 'hidden',
                                leaveActiveClass: 'slideup'
                            }"
                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                <i class="mr-2"></i>
                                <span class="font-medium">Administración de Usuarios</span>
                                <i class="pi pi-chevron-down ml-auto"></i>
                            </a>
                            <ul
                                class="list-none py-0 pl-3 pr-0 m-0 hidden overflow-y-hidden transition-all transition-duration-400 transition-ease-in-out">
                                <li>
                                    <a v-ripple
                                        @click="redirectToAnotherPage('Pagina principal administracion usuarios')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-list mr-2"></i>
                                        <span class="font-medium">Panel de control</span>
                                    </a>
                                </li>
                            </ul>
                        </li>

                        <!--Titulados-->
                        <!--<li>
                            <a v-ripple v-styleclass="{
                                selector: '@next',
                                enterClass: 'hidden',
                                enterActiveClass: 'slidedown',
                                leaveToClass: 'hidden',
                                leaveActiveClass: 'slideup'
                            }"
                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                <i class="mr-2"></i>
                                <span class="font-medium">Titulados</span>
                                <i class="pi pi-chevron-down ml-auto"></i>
                            </a>
                            <ul
                                class="list-none py-0 pl-3 pr-0 m-0 hidden overflow-y-hidden transition-all transition-duration-400 transition-ease-in-out">
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-plus-circle mr-2"></i>
                                        <span class="font-medium">Form - Ingresar datos</span>
                                    </a>
                                </li>
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-chart-bar mr-2"></i>
                                        <span class="font-medium">Filter 1</span>
                                    </a>
                                </li>
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-chart-bar mr-2"></i>
                                        <span class="font-medium">Filter 2</span>
                                    </a>
                                </li>
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-chart-bar mr-2"></i>
                                        <span class="font-medium">Filter 3</span>
                                    </a>
                                </li>
                            </ul>
                        </li>-->
                        <!--Oferta Académica-->
                        <!--<li>
                            <a v-ripple v-styleclass="{
                                selector: '@next',
                                enterClass: 'hidden',
                                enterActiveClass: 'slidedown',
                                leaveToClass: 'hidden',
                                leaveActiveClass: 'slideup'
                            }"
                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                <i class="mr-2"></i>
                                <span class="font-medium">Oferta Académica</span>
                                <i class="pi pi-chevron-down ml-auto"></i>
                            </a>
                            <ul
                                class="list-none py-0 pl-3 pr-0 m-0 hidden overflow-y-hidden transition-all transition-duration-400 transition-ease-in-out">
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-plus-circle mr-2"></i>
                                        <span class="font-medium">Form - Ingresar datos</span>
                                    </a>
                                </li>
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('OfertaAcadémica-Form')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-chart-bar mr-2"></i>
                                        <span class="font-medium">Filter 1</span>
                                    </a>
                                </li>
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('OfertaAcadémica-Form-newTable')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-chart-bar mr-2"></i>
                                        <span class="font-medium">Filter 2</span>
                                    </a>
                                </li>
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-chart-bar mr-2"></i>
                                        <span class="font-medium">Filter 3</span>
                                    </a>
                                </li>
                            </ul>
                        </li>-->
                        <!--Postgrado-->
                        <!--<li>
                            <a v-ripple v-styleclass="{
                                selector: '@next',
                                enterClass: 'hidden',
                                enterActiveClass: 'slidedown',
                                leaveToClass: 'hidden',
                                leaveActiveClass: 'slideup'
                            }"
                                class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                <i class="mr-2"></i>
                                <span class="font-medium">Postgrado</span>
                                <i class="pi pi-chevron-down ml-auto"></i>
                            </a>
                            <ul
                                class="list-none py-0 pl-3 pr-0 m-0 hidden overflow-y-hidden transition-all transition-duration-400 transition-ease-in-out">
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-plus-circle mr-2"></i>
                                        <span class="font-medium">Form - Ingresar
                                            datos</span>
                                    </a>
                                </li>
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-chart-bar mr-2"></i>
                                        <span class="font-medium">Filter 1</span>
                                    </a>
                                </li>
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-chart-bar mr-2"></i>
                                        <span class="font-medium">Filter 2</span>
                                    </a>
                                </li>
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-chart-bar mr-2"></i>
                                        <span class="font-medium">Filter 3</span>
                                    </a>
                                </li>
                            </ul>
                        </li>-->
                    </ul>

                    <ul class="list-none p-3 m-0 pt-8">
                        <li>
                            <div v-ripple v-styleclass="{
                                selector: '@next',
                                enterClass: 'hidden',
                                enterActiveClass: 'slidedown',
                                leaveToClass: 'hidden',
                                leaveActiveClass: 'slideup'
                            }"
                                class="p-3 flex align-items-center justify-content-between text-600 cursor-pointer p-ripple">
                                <span class="font-medium">APLICACIÓN</span>
                                <i class="pi pi-chevron-down"></i>
                            </div>
                            <ul class="list-none p-0 m-0 overflow-hidden">
                                <li>
                                    <a
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <span class="font-medium mr-6">Modo Oscuro</span>
                                        <button @click="toggleDarkMode()">Switch Theme</button>
                                    </a>
                                </li>
                            </ul>
                            <ul class="list-none p-0 m-0 overflow-hidden">
                                <li>
                                    <a v-ripple @click="redirectToAnotherPage('')"
                                        class="flex align-items-center cursor-pointer p-3 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                                        <i class="pi pi-cog mr-2"></i>
                                        <span class="font-medium">Opciones</span>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
                <div class="mt-auto">
                    <hr class="mb-3 mx-3 border-top-1 border-none surface-border" />
                    <a v-ripple @click="redirectToAnotherPage('')"
                        class="m-3 flex align-items-center cursor-pointer p-3 gap-2 border-round text-700 hover:surface-100 transition-duration-150 transition-colors p-ripple">
                        <span class="font-bold">{{ userName }}</span>

                    </a>
                </div>
            </div>
        </template>

    </Sidebar>
    <Toast position="bottom-left" group="bl" />
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue';
import { useAuthStore } from '../store/auth';
import { useRouter } from 'vue-router';
import { usePrimeVue } from 'primevue/config';
import { useThemeStore } from '../store/theme';
import Button from 'primevue/button';
import { encryptData, decryptData } from '@/utils/crypto';
import axios from 'axios';
import { useToast } from 'primevue/usetoast';

export default {
    name: 'SideTopBar',
    setup() {
        const toast = useToast();
        const authStore = useAuthStore();
        const root = ref(false);
        // Reactive property to hold the notification count

        // Computed properties for userName , userEmail and GlobalLoading state
        const userName = computed(() => decryptData(authStore.userName));
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const notificationNumber = computed(() => decryptData(authStore.notificationNumber));
        const router = useRouter();
        const PrimeVue = usePrimeVue();
        const themeStore = useThemeStore();
        const currentTheme = ref(null);
        const inverseTheme = ref(null);

        // Computed property to check if user is logged in
        const isLoggedIn = computed(() => decryptData(authStore.userIsAuthenticated));
        const userRole = computed(() => decryptData(authStore.permissionsList));

        // Base url for redirection
        const base = "/";
        // Visibility of the side bar
        const visible = ref(false);
        /** Function for opening and closing the sideBar */
        const updateVisibility = () => {
            visible.value = !visible.value;
        };
        onMounted(() => {
            currentTheme.value = themeStore.getCurrentTheme;
            inverseTheme.value = themeStore.getInverseTheme;
            PrimeVue.changeTheme(currentTheme.value, currentTheme.value, 'theme-link', () => { });
        });

        /**
         * On the top Bar set the icon to display and add the option to open the sidebar when clicked
         */
        const items = reactive([
            {
                label: 'Opciones',
                icon: 'pi pi-bars',
                badge: "undefined",
                command: () => {
                    onclick = updateVisibility();
                }
            },
            {
                label: 'Notificaciones',
                icon: 'pi pi-envelope',
                badge: notificationNumber,
                command: () => {
                    router.push('avisos-central'); // Navigate to the avisos view
                }
            }
            // Add other items when needed
        ]);

        // Function to check if badge should be displayed
        const shouldShowBadge = (badge) => {
            const numericValue = parseInt(badge, 10);
            return numericValue > 0;
        };

        // Function to format the badge value
        const formatBadgeValue = (badge) => {
            const numericValue = parseInt(badge, 10);
            if (numericValue > 9) {
                return "+9"; // Show +9 for any value greater than 9
            }
            return badge; // Return the original badge value for 1-9
        };

        /**
         * Modify the darkMode throught vuex mutations
         */
        const toggleDarkMode = async () => {
            PrimeVue.changeTheme(currentTheme.value, inverseTheme.value, 'theme-link', () => { });
            await themeStore.setTheme(inverseTheme.value, currentTheme.value);
            currentTheme.value = themeStore.getCurrentTheme;
            inverseTheme.value = themeStore.getInverseTheme;

        };



        // Logout method
        const logout = () => {
            authStore.updateLoadingState(true);
            authStore.logout(router, toast);
        };

        return {
            root,
            visible,
            items,
            base,
            updateVisibility,
            toggleDarkMode,
            PrimeVue,
            authStore,
            router,
            logout,
            isLoggedIn,
            userName,
            userEmail,
            userRole,
            shouldShowBadge,
            formatBadgeValue,
            notificationNumber,
            toast

        };
    },
    methods: {
        /**
         * Method to redirect to the differents pages of the SideBar
         * 
         * "this.visible" closes the side bar when clicking a option
         * 
         * @param value The clicked button of the sideBar 
         */
        redirectToAnotherPage(value) {
            switch (value) {
                case "Inicio":
                    if (this.checkcurrentRoute('/')) {
                        this.$router.push('/');
                        this.visible = false;
                    }
                    break;
                case "Admin":
                    if (this.checkcurrentRoute('/Admin')) {
                        this.$router.push('/Admin/')
                        this.visible = false;
                    }
                    break;
                case "Matrículas-Form":
                    if (this.checkcurrentRoute('/Admision-Form/')) {
                        this.$router.push('/Admision-Form/')
                        this.visible = false;
                    }
                    break;
                case "tabs":
                    if (this.checkcurrentRoute('/tabs')) {
                        this.$router.push('/tabs');
                        this.visible = false;
                    }
                    break;
                case "Matrículas-View1":
                    if (this.checkcurrentRoute('/MatriculaView1/')) {
                        this.$router.push('/MatriculaView1/')
                        this.visible = false;
                    }
                    break;
                case "Titulados-Form":
                    break;
                case "OfertaAcadémica-Form":
                    if (this.checkcurrentRoute('/oferta-academica/')) {
                        this.$router.push('/oferta-academica/')
                        this.visible = false;
                    }
                    break;
                case "OfertaAcadémica-Form-newTable":
                    if (this.checkcurrentRoute('/oferta-academica-newtable/')) {
                        this.$router.push('/oferta-academica-newtable/')
                        this.visible = false;
                    }
                    break;
                case "Admision-View":
                    if (this.checkcurrentRoute('/Admision-View')) {
                        this.$router.push('/Admision-View')
                        this.visible = false;
                    }
                    break;
                case "Pagina principal oti":
                    if (this.checkcurrentRoute('/Pagina-principal-oti')) {
                        this.$router.push('/Pagina-principal-oti')
                        this.visible = false;
                    }
                    break;
                case "Configuracion oti":
                    if (this.checkcurrentRoute('/configuracion-oti')) {
                        this.$router.push('/configuracion-oti')
                        this.visible = false;
                    }
                    break;
                case "Pagina principal administracion usuarios":
                    if (this.checkcurrentRoute('/Pg-principal-adm-usuarios')) {
                        this.$router.push('/Pg-principal-adm-usuarios')
                        this.visible = false;
                    }
                    break;
                case "PP-MU-pregrado":
                    if (this.checkcurrentRoute('/PP-MU-pregrado')) {
                        this.$router.push('/PP-MU-pregrado')
                        this.visible = false;
                    }
                    break;
                case "PP-MU-postgrado-postitulo":
                    if (this.checkcurrentRoute('/PP-MU-postgrado-postitulo')) {
                        this.$router.push('/PP-MU-postgrado-postitulo')
                        this.visible = false;
                    }
                    break;
                    case "OfertaAcademicaView":
                    if (this.checkcurrentRoute('/oacademicaView')) {
                        this.$router.push('/oacademicaView')
                        this.visible = false;
                    }
                    break;
                case "oferta-academica-sies":
                    if (this.checkcurrentRoute('/oferta-academica-sies')) {
                        this.$router.push('/oferta-academica-sies')
                        this.visible = false;
                    }
                    break;
                case "ficha-cna":
                    if (this.checkcurrentRoute('/ficha-cna')) {
                        this.$router.push('/ficha-cna')
                        this.visible = false;
                    }
                    break;

                case "Postgrado-Form":
                    break;
            }

        },
        /**
         * Checks the current routes to not redirect to the same page that the user is
         * @param currentroute the route that the user is
         */
        checkcurrentRoute(currentroute) {
            if (this.$route.fullPath == currentroute) {
                return false;
            }
            return true;
        }
    }
}
</script>

<style>
.centered-text {
    flex: 1;
    text-align: center;
    font-weight: bold;
}

.badge-custom {
    background-color: red;
    /* Example background color */
    color: white;
    border-radius: 50%;
    /* Makes it a circle */
    border: 2px solid white;
    /* White outline */
    min-width: 2rem;
    /* Ensures badge stays circular for small numbers */
    min-height: 2rem;
    /* Sets equal height for circle */
    display: inline-flex;
    justify-content: center;
    align-items: center;
    font-size: 1rem;
    line-height: 1;
    /* Ensures the text is centered vertically */
    padding: 0.3rem;
    /* Default padding */
    animation: pulse 1.5s infinite ease-in-out, swing 2s infinite ease-in-out;
    /* Applies swing animation */
    transform-origin: center;
    /* Ensures swing rotates around the center */
}

.badge-large {
    min-width: 2.5rem;
    /* Increased size for larger numbers */
    min-height: 2.5rem;
    padding: 0.5rem;
    /* Increased padding for larger badge values */
}

.badge-hidden {
    min-width: 2.5rem;
    /* Increased size for larger numbers */
    min-height: 2.5rem;
    padding: 0.5rem;
    /* Increased padding for larger badge values */
}

.badge-custom::before {
    content: attr(data-value);
    /* Uses badge value for display */
}

.p-menubar {
    align-content: center;
}

@keyframes swing {
    20% {
        transform: rotate(15deg);
    }

    40% {
        transform: rotate(-10deg);
    }

    60% {
        transform: rotate(5deg);
    }

    80% {
        transform: rotate(-5deg);
    }

    100% {
        transform: rotate(0deg);
    }
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
        /* Normal size */
    }

    50% {
        transform: scale(1.1);
        /* Enlarged size */
    }
}
</style>