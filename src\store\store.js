// src/store.js
import { createStore } from 'vuex';

export default createStore({
    state: {
        darkMode: false
    },

    mutations: {
        toggleDarkMode(state) {
            state.darkMode = !state.darkMode;
            localStorage.setItem('darkMode', state.darkMode); // Update local storage
        },
    },
    actions: {
        toggleDarkMode({ commit }) {
            commit('toggleDarkMode');
        },
    },

    getters: {
        isDarkMode: (state) => state.darkMode,
    },
});