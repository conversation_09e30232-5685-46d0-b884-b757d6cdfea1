<template>
    <div class="notification-icon">
        <i class="pi pi-bell" @click="toggleNotifications"></i>
        <div v-if="showNotifications" class="notification-list">
            <ul>
                <li v-for="notification in notifications" :key="notification.id">
                    {{ notification.message }}
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            showNotifications: false,
            notifications: [
                { id: 1, message: 'New message from <PERSON>' },
                { id: 2, message: 'Server is down' },
                { id: 3, message: 'New comment on your post' },
            ],
        };
    },
    methods: {
        toggleNotifications() {
            this.showNotifications = !this.showNotifications;
        },
    },
};
</script>

<style scoped>
.notification-icon {
    position: relative;
    cursor: pointer;
}

.notification-list {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #ccc;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
}

.notification-list ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.notification-list li {
    padding: 8px 12px;
}
</style>