<template>
    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading && mu_pp_data && mu_pp_data[0]">
            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <div class='surface-card shadow-3 p-3 border-round'>
                    <!--Titulo-->
                    <div style="display: flex;align-items: center;padding-left: 1rem;">

                        <Button @click.prevent="goBack()"> <- </Button>
                                <h2 style="padding-left: 1rem;">Detalle Matrícula Unificada Posgrado Postítulo {{
                                    anio_proceso }}
                                </h2>
                    </div>
                    <br />

                    <!--Etapa 1-->
                    <div v-if="etapa_actual >= 1" style="margin-left: 1rem;margin-right: 1rem;">
                        <div style="display: flex;justify-content: space-between;align-items: center;">
                            <h3>Etapa N°1</h3>
                            <Button v-if="hasAccess('Administrador Sistema', 'Administracion PIU') || hasAccess('Encargado', 'Mu-posgrado-postitulo') && mu_pp_data[0].fechas[0].validated_by == null
                                & mu_pp_data[0].etapaCounts[0].count > 0"
                                @click="dialogConfirmarMU_post = true, currentEtapaForDialog = 1">Validar</Button>
                        </div>
                        <DataTable sortField="createdAt" :sortOrder="-1" showGridlines scrollable scrollHeight="250px"
                            :value="mu_pp_data" style="width: 100%;">
                            <template #empty>
                                <div style="text-align: center;"> No se encontraron datos </div>

                            </template>
                            <Column style="width: 10%" header="N° Estudiantes" field="" class="center-header">
                                <template #body="slotProps">
                                    {{ slotProps.data.etapaCounts[0].count }}
                                </template>
                            </Column>
                            <Column field="is_finalized" header="Estado" class="center-header" style="width: 5%;">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.fecha_validacion_1 == null"
                                        v-tooltip.bottom="pending_text">
                                        <Button disabled icon="pi pi-hourglass"
                                            style="justify-content: center;color: orangered;" class="p-button-text" />
                                    </div>
                                    <div v-else v-tooltip.bottom="finalized_text">
                                        <Button disabled icon="pi pi-verified"
                                            style="justify-content: center;color: green"
                                            class="p-button-text green-icon" />
                                    </div>
                                </template>
                            </Column>
                            <Column style="width: 5%" header="Fecha límite" field="fecha_limite" class="center-header">
                                <template #body="slotProps">

                                    <div v-if="slotProps.data.fechas && slotProps.data.fechas.length > 0"
                                        v-tooltip.bottom="{ value: tooltipTextEtapa1, pt: { root: { style: 'white-space: nowrap; max-width: none;' } } }">

                                        <Button disabled icon="pi pi-info-circle"
                                            style="justify-content: center;color: orangered;" class="p-button-text" />
                                    </div>
                                </template>
                            </Column>
                            <Column style="width: 10%;" header="Fecha carga" field="fecha_carga_1"
                                class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.fecha_carga_1">
                                        {{ slotProps.data.fecha_carga_1 }}
                                    </div>
                                    <div v-else>
                                        Sin Datos
                                    </div>
                                </template>
                            </Column>
                            <Column style="width: 5%;" header="Fecha validación" field="fecha_validacion_1"
                                class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.fecha_validacion_1">
                                        {{ slotProps.data.fecha_validacion_1 }}
                                    </div>
                                    <div v-else>
                                        Sin Datos
                                    </div>
                                </template>
                            </Column>

                            <Column style="width: 5%;" field="Cargar Datos" header="Cargar Datos" class="center-header">
                                <template #body="slotProps">
                                    <div
                                        v-if="slotProps.data.etapaCounts[0].count == 0 && (hasAccess('Encargado', 'Mu-posgrado-postitulo') || hasAccess('Usuario General', 'Mu-posgrado-postitulo') || hasAccess('Administrador Sistema', 'Administracion PIU'))">
                                        <Button icon="pi pi-upload" style="" class="p-button-text"
                                            @click="dialogCargarDatosisVisible = true, currentEtapaForDialog = 1" />
                                    </div>
                                    <div v-else-if="slotProps.data.etapaCounts[0].count > 0">
                                        Carga finalizada
                                    </div>
                                </template>
                            </Column>
                            <Column style="width: 5%" field="Exportar" header="Exportar" class="center-header">
                                <template #body="slotProps">
                                    <div
                                        v-if="slotProps.data.etapaCounts[0].count > 0 && (hasAccess('Encargado', 'Mu-posgrado-postitulo') || hasAccess('Usuario General', 'Mu-posgrado-postitulo') || hasAccess('Administrador Sistema', 'Administracion PIU'))">
                                        <Button icon="pi pi-download" style="" class="p-button-text"
                                            @click="exportarDatosCSV(anio_proceso, 1)" />
                                    </div>
                                    <div v-else-if="slotProps.data.etapaCounts[0].count == 0 ">
                                        Sin Datos
                                    </div>
                                </template>
                            </Column>
                            <Column style="width: 5%;" field="eliminar" header="Eliminar" class="center-header">
                                <template #body="slotProps">
                                    <div
                                        v-if="hasAccess('Encargado', 'Mu-posgrado-postitulo') || hasAccess('Administrador Sistema', 'Administracion PIU')">
                                        <Button icon="pi pi-trash" class="p-button-text"
                                            @click="dialogVisibleEliminar = true, currentEtapaForDialog = 1" />
                                    </div>
                                </template>
                            </Column>
                        </DataTable>
                    </div>
                    <br />
                </div>
            </div>

            <Dialog v-model:visible="dialogVisibleEliminar" modal header="Eliminar inmueble" :style="{ width: '25rem' }"
                v-on:hide="resetForm()">
                <div v-if="dialogIsLoading && !muPpHasFinalized">
                    <Loading />
                </div>
                <div v-if="mu_pp_data[0].etapaCounts">
                    <div v-if="!dialogIsLoading && !muPpHasFinalized">
                        <div>
                            <!-- Validation error message -->
                            <p>Esto eliminará permanentemente este registro ¿Está seguro?</p>
                        </div>
                        <br />
                        <div class="flex justify-content-center gap-2">
                            <InputSwitch style="scale: 1.5;" v-model="deletePreConfirmation">
                            </InputSwitch>
                        </div>
                        <br />
                        <!-- Validation error message -->
                        <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}<br /></div>


                        <!-- Buttons -->
                        <div class="flex justify-content-end gap-2">
                            <Button type="button" label="Cancelar" severity="secondary"
                                @click="dialogVisibleEliminar = false"></Button>
                            <Button v-if="deletePreConfirmation" type="button" label="Eliminar"
                                @click="eliminarInmueble(currentEtapaForDialog)"></Button>
                        </div>
                    </div>
                </div>
                <div v-if="!dialogIsLoading && muPpHasFinalized && deleteHasErrors"
                    class="flex flex-column align-items-center">
                    <i class="pi pi-exclamation-triangle" style="font-size: 3rem; color: red;"></i>
                    <p class="text-center mt-3" style="color: red">{{ errorMessage }}</p>
                </div>

                <!-- Success message -->
                <div v-if="!dialogIsLoading && muPpHasFinalized && !deleteHasErrors"
                    style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
                    <i class="pi pi-trash" style="font-size: 5rem; color: blue;"></i>
                    <br />
                    <span class="p-text-secondary block mb-5" style="color: blue;">¡Eliminado correctamente!</span>
                </div>

            </Dialog>
            <Dialog v-model:visible="dialogConfirmarMU_post" modal header="Finalizar Etapa de Matrícula Unificada"
                :style="{ width: '25rem' }" v-on:hide="resetForm()">
                <div v-if="dialogIsLoading && !muPpHasFinalized">
                    <Loading />
                </div>

                <div v-if="!dialogIsLoading && !muPpHasFinalized">
                    <div>
                        <p class="block mb-3">Al validar el registro este no se podrá editar ni eliminar ¿Está
                            seguro?
                        </p>
                        <p class="p-error block mb-3">Esta acción es IRREVERSIBLE.
                        </p>
                    </div>
                    <br />
                    <div class="flex justify-content-center gap-2">
                        <InputSwitch style="scale: 1.5;" v-model="validatePreConfirmation"></InputSwitch>
                    </div>
                    <br />
                    <div>
                        <!-- Validation error message -->
                        <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}</div>

                        <br />
                    </div>
                    <!-- Buttons -->
                    <div class="flex justify-content-end gap-2">
                        <Button type="button" label="Cancelar" severity="secondary"
                            @click="dialogConfirmarMU_post = false"></Button>
                        <Button v-if="validatePreConfirmation" type="button" label="Confirmar"
                            @click="validarMatriculaUnificada(currentEtapaForDialog)"></Button>
                    </div>
                </div>

                <!-- Success message -->
                <div v-if="!dialogIsLoading && muPpHasFinalized && !validarhasErrors"
                    style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
                    <i class="pi pi-check" style="font-size: 5rem; color: blue;"></i>
                    <br />
                    <span class="p-text-secondary block mb-5">¡Registro validado correctamente!</span>
                </div>

                <!-- error message -->
                <div v-if="!dialogIsLoading && muPpHasFinalized && validarhasErrors"
                    style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
                    <i class="pi pi-exclamation-triangle" style="font-size: 5rem; color: red;"></i>
                    <br />
                    <span class="p-text-secondary block mb-5" style="color: red;">Ocurrió un error al validar el
                        registro,
                        inténtelo más tarde.</span>
                </div>


            </Dialog>
            <Dialog v-model:visible="dialogCargarDatosisVisible" modal header="Cargar datos de estudiantes"
                :style="{ width: '25rem' }" v-on:hide="resetForm()">

                <div v-show="!cargarDatosIsLoading && !cargarDatosHasError && !cargarDatosisCorrect" class="card">
                    <FileUpload ref="fileUploadRef" name="estudianteData" uploadLabel="Cargar"
                        v-on:select="onSelect($event)" v-on:before-upload="onUpload($event)" :multiple="false"
                        accept=".csv" :maxFileSize="3000000" :fileLimit=1>
                        <template #empty>
                            <p>Arrastra y suelta un archivo aquí para subirlo.</p>
                        </template>
                    </FileUpload>
                </div>

                <div v-if="!cargarDatosIsLoading && cargarDatosisCorrect" class="flex flex-column align-items-center">
                    <i class="pi pi-check-circle" style="font-size: 3rem; color: green;"></i>
                    <p class="text-center mt-3">El archivo se cargó correctamente.</p>
                </div>

                <div v-if="!cargarDatosIsLoading && cargarDatosHasError" class="flex flex-column align-items-center">
                    <i class="pi pi-exclamation-triangle" style="font-size: 3rem; color: red;"></i>
                    <p class="text-center mt-3">Se encontraron errores en el archivo.</p>
                    <Button label="Descargar errores" icon="pi pi-download" class="p-button-danger mt-3"
                        @click="downloadErrors" />
                </div>
                <div v-if="cargarDatosIsLoading && !cargarDatosHasError">
                    <Loading />
                </div>

            </Dialog>

        </div>
        <div v-else class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
                <Loading />
            </div>
        </div>
        <Toast position="bottom-left" group="bl" />
    </div>



</template>

<script>
import { useAuthStore } from '../../../store/auth';
import { ref, computed, onMounted } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import axios from 'axios';
import Papa from 'papaparse';
import { useToast } from 'primevue/usetoast';
import { useRoute, useRouter } from 'vue-router';

export default {

    setup() {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const route = useRoute();
        const router = useRouter();
        const authStore = useAuthStore();
        const toast = useToast();
        const isLoading = ref(false);
        const mu_pp_data = ref([]);

        const dialogIsLoading = ref(false);
        const finalized_text = ref("Finalizado");
        const pending_text = ref("Pendiente");

        // Obtain the stored user data from pinia and the global loading state
        const userName = computed(() => decryptData(authStore.userName));
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const permissionsList = computed(() => decryptData(authStore.permissionsList));
        const isUserAuthenticated = computed(() => decryptData(authStore.userIsAuthenticated));
        const globalLoading = computed(() => decryptData(authStore.isLoading));
        const userToken = computed(() => decryptData(authStore.idToken));
        const anio_proceso = decryptData(route.params.anio_proceso); // Access the params
        const etapa_actual = ref(null); // Etapa actual del preceso (estatico hasta que se valida el registro)
        const muPpHasFinalized = ref(false);
        const errorMessage = ref('');

        const dialogConfirmarMU_post = ref(false);
        const validatePreConfirmation = ref(false);
        const muPpAnioSelecionado = ref({});

        // Dialog validar datos
        const validarhasErrors = ref(false);

        // Dialog cargar datos
        const dialogCargarDatosisVisible = ref(false);
        const cargarDatosIsLoading = ref(false);
        const cargarDatosHasError = ref(false);
        const cargarDatosisCorrect = ref(false);
        const fileUploadRef = ref(null);
        const parsedData = ref([]);
        const currentEtapaForDialog = ref(null); // Etapa seleccionada (varia segun etapa) para la ventana de dialogo
        const errorData = ref([]);
        const errorCSVContent = ref('');

        // Dialog Eliminar
        const dialogVisibleEliminar = ref(false);
        const deletePreConfirmation = ref(false);
        const deleteHasErrors = ref(false);

        // Tooltips with the dates of each etapa
        const tooltipTextEtapa1 = ref();
        const tooltipTextEtapa2 = ref();
        const tooltipTextEtapa3 = ref();
        const tooltipTextEtapa4 = ref();
        const tooltipTextEtapa5 = ref();

        const hasAccess = (allowedRol, requiredRecurso) => {
            const access = permissionsList.value.some(user => user.rol === allowedRol && user.recurso === requiredRecurso);
            if (access) {
                return true;
            } else {
                return false;
            }

        };
        const goBack = () => {
            router.push({ path: "/PP-MU-postgrado-postitulo" });
        };
        onMounted(async () => {
            isLoading.value = true;
            await getMUPpData();
            console.log("test")
            console.log(mu_pp_data.value)
            console.log("test2")
            console.log(etapa_actual.value)
            isLoading.value = false;
        });

        const getMUPpData = async () => {
            try {
                isLoading.value = true;

                const response = await axios.get(API_BASE_URL + `mu_postgrado_postitulo/${anio_proceso}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });

                if (response.status == 200) {
                    /**
                     * Updates the `mu_pp_data` reactive variable with the response data.
                     * If the response data is an array, it assigns it directly to `mu_pp_data`.
                     * If the response data is not an array, it wraps it in an array before assignment.
                     * This ensures that `mu_pp_data` is always an array, regardless of the response format.
                     */
                    etapa_actual.value = response.data.etapa_actual;
                    mu_pp_data.value = Array.isArray(response.data) ? response.data : [response.data];
                    const etapasData = response.data.fechas;
                    if (etapasData) {
                        const etapa1Data = etapasData.find(fecha => fecha.etapa_proceso === 1);
                        if (etapa1Data) {
                            tooltipTextEtapa1.value = `${etapa1Data.fecha_inicio} al ${etapa1Data.fecha_termino}`;
                        }
                    }
                    console.log(tooltipTextEtapa1.value);
                }
            } catch (error) {
                console.error("Error: ", error);
            } finally {
                isLoading.value = false;
            }
        };

        // Function to reset the form for a new creation
        const resetForm = () => {
            errorMessage.value = ''; // Clear any error messages
            muPpHasFinalized.value = false; // Reset success state
            deletePreConfirmation.value = false;
            dialogVisibleEliminar.value = false;
            currentEtapaForDialog.value = null; // Reset currentEtapa to null
            cargarDatosHasError.value = false;
            cargarDatosIsLoading.value = false;
            cargarDatosisCorrect.value = false;
            validatePreConfirmation.value = false;
        }

        const validarMatriculaUnificada = async (etapa_actual) => {
            try {
                dialogIsLoading.value = true;

                const response = await axios.get(API_BASE_URL + `mu_postgrado_postitulo/${anio_proceso}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                muPpAnioSelecionado.value = await response.data;
                if (muPpAnioSelecionado.value.is_finalized == true && etapa_actual < 5) {
                    toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El registro no puede ser modificado', life: 5000 });
                } else {
                    console.log(muPpAnioSelecionado.value.etapaCounts[etapa_actual - 1].count);
                    if (

                        muPpAnioSelecionado.value.etapaCounts[etapa_actual - 1].count > 0
                    ) {
                        try {
                            const body = ref({});
                            body.value.anio_proceso = muPpAnioSelecionado.value.anio_proceso;
                            const response2 = await axios.patch(API_BASE_URL + `mu_postgrado_postitulo/${anio_proceso}`, body.value, {
                                headers: {
                                    Authorization: `Bearer ${userToken.value}`
                                }
                            });
                            // Check if the response is successful (status code 200)
                            if (response2.status == 201) {
                                muPpHasFinalized.value = true;
                                validarhasErrors.value = false;
                                isLoading.value = true;
                                await getMUPpData();
                                isLoading.value = false;
                            }
                        } catch (error) {
                            resetForm();
                            validarhasErrors.value = true;
                            toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Se detectó un error, inténtelo más tarde.', life: 5000 });

                        }

                    } else {

                    }
                }

            }
            catch (error) {
                console.error("Error fetching data:", error);
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Ocurrió un error, inténtalo más tarde', life: 5000 });
            }
            finally {
                dialogIsLoading.value = false;
            }
        };

        const onSelect = async (event) => {
            try {
                cargarDatosIsLoading.value = true;
                console.log("test")
                const file = event.files[0]; // Access the first file from the Proxy array
                const reader = new FileReader();
                reader.readAsText(file, 'windows-1252'); // Use windows-1252 encoding for reading the file
                reader.onload = async (e) => {
                    const fileContent = e.target.result;
                    //TODO: The third column can be empty
                    // Parse the CSV content using PapaParse
                    Papa.parse(fileContent, {
                        header: true,
                        skipEmptyLines: true,
                        delimiter: ";",
                        encoding: "windows-1252", // Ensure PapaParse also uses windows-1252 encoding
                        complete: async (results) => {
                            try {
                                parsedData.value = results.data;
                                // Check for empty fields in the parsed data
                                const hasEmptyFields = parsedData.value.some(row => {
                                    const rowValues = Object.values(row);
                                    return rowValues.some((value, index) =>
                                        (index !== 2 && index !== 4) && // Skip validation for the third and fifth columns
                                        (value === "" || value === null || value === undefined ||
                                            (typeof value === "string" && value.trim() === ""))
                                    );
                                });


                                const headers = [
                                    "TIPO_DOC", "N_DOC", "DV", "PRIMER_APELLIDO", "SEGUNDO_APELLIDO", "NOMBRE",
                                    "SEXO", "FECH_NAC", "NAC", "PAIS_EST_SEC", "COD_SED", "COD_CAR", "MODALIDAD",
                                    "JOR", "VERSION", "FOR_ING_ACT", "ANIO_ING_ACT", "SEM_ING_ACT", "ANIO_ING_ORI",
                                    "SEM_ING_ORI", "VIG"
                                ];
                                const missingHeaders = headers.filter(header => !Object.keys(parsedData.value[0]).includes(header));
                                if (missingHeaders.length > 0) {
                                    const missingHeadersMessage = "El archivo no contiene las siguientes cabeceras:\n" + missingHeaders.map(header => `${header}`).join('\n');
                                    console.error(missingHeadersMessage);
                                    cargarDatosHasError.value = true;
                                    errorData.value = missingHeadersMessage;
                                    generateErrorCSV(errorData.value);
                                    toast.add({
                                        severity: "error",
                                        summary: "Error de Validación",
                                        group: "bl",
                                        detail: "El archivo .csv contiene errores",
                                        life: 10000,
                                    });
                                    return;
                                }

                                // Proceed with further processing
                                console.log("Parsed Data:", parsedData.value);
                                toast.add({
                                    severity: 'success',
                                    summary: 'Éxito',
                                    group: 'bl',
                                    detail: 'El archivo tiene el formato correcto.',
                                    life: 5000
                                });


                            } catch (error) {
                                console.error("Error uploading data:", error);
                                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Ocurrió un error al cargar los datos.', life: 5000 });
                            }
                        },
                        error: (error) => {
                            console.error("Error parsing CSV:", error);
                            toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Ocurrió un error al procesar el archivo.', life: 5000 });
                        },
                    });
                };
            } catch (error) {
                cargarDatosIsLoading.value = false;
                console.error("Error handling file upload:", error);
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Ocurrió un error al procesar el archivo.', life: 5000 });
            } finally {
                cargarDatosIsLoading.value = false;
            }

        };

        const onUpload = async () => {
            try {
                cargarDatosIsLoading.value = true;
                const dataSizeInBytes = new Blob([JSON.stringify(parsedData.value)]).size;
                const dataSizeInMegabytes = dataSizeInBytes / (1024 * 1024);
                console.log(`Data size: ${dataSizeInMegabytes.toFixed(2)} MB`);
                const response = await axios.post(
                    `${API_BASE_URL}mu_postgrado_postitulo/cargarCSV/${anio_proceso}/${currentEtapaForDialog.value}`,
                    { estudiantes: parsedData.value },
                    {
                        headers: {
                            Authorization: `Bearer ${userToken.value}`,
                            "Content-Type": "application/json",
                        },
                    }
                );
                console.log("Response:", response.data);
                if (response.status === 200) {
                    toast.add({
                        severity: "success",
                        summary: "Éxito",
                        group: "bl",
                        detail: "Datos cargados correctamente.",
                        life: 5000,
                    });
                    await getMUPpData();
                    cargarDatosisCorrect.value = true;
                } else {
                    toast.add({
                        severity: "error",
                        summary: "Error",
                        group: "bl",
                        detail: "Ocurrió un error al cargar los datos.",
                        life: 5000,
                    });
                }
            } catch (error) {
                console.error("Error during axios.post:", error);
                if (error.response && error.response.status === 400) {
                    cargarDatosHasError.value = true;
                    errorData.value = error.response.data;
                    generateErrorCSV(errorData.value);
                    toast.add({
                        severity: "error",
                        summary: "Error de Validación",
                        group: "bl",
                        detail: "El archivo .csv contiene errores",
                        life: 10000,
                    });
                } else {
                    toast.add({
                        severity: "error",
                        summary: "Error",
                        group: "bl",
                        detail: "Ocurrió un error al cargar los datos.",
                        life: 5000,
                    });
                }
            } finally {
                cargarDatosIsLoading.value = false;
            }
        }

        const generateErrorCSV = async (errorDataAxios) => {
            try {

                // Store the CSV content for download
                errorCSVContent.value = errorDataAxios;

                console.log('CSV generated successfully');
            } catch (error) {
                console.error('Error generating CSV:', error);
                toast.add({
                    severity: 'error',
                    summary: 'Error',
                    group: 'bl',
                    detail: 'Error al generar el archivo de errores',
                    life: 5000
                });
            }
        }

        // Function to download the generated CSV file using Windows-1252 encoding
        const downloadErrors = () => {
            try {
                if (!errorCSVContent.value) {
                    toast.add({
                        severity: 'error',
                        summary: 'Error',
                        group: 'bl',
                        detail: 'No hay errores para descargar',
                        life: 5000
                    });
                    return;
                }

                // Create a Blob with the CSV content
                const blob = new Blob([errorCSVContent.value], { type: 'text/csv;charset=windows-1252;' });

                // Create a download link
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);

                // Set link properties
                link.setAttribute('href', url);
                link.setAttribute('download', `errores_matricula_${anio_proceso}_${new Date().toISOString().slice(0, 10)}.csv`);
                link.style.visibility = 'hidden';

                // Add to document, click and remove
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch (error) {
                console.error('Error downloading CSV:', error);
                toast.add({
                    severity: 'error',
                    summary: 'Error',
                    group: 'bl',
                    detail: 'Error al descargar el archivo de errores',
                    life: 5000
                });
            }
        }

        const exportarDatosCSV = async (anio_proceso, etapa) => {
            try {
                const response = await axios.get(
                    `${API_BASE_URL}mu_postgrado_postitulo/exportCSV/${anio_proceso}/${etapa}`,
                    {
                        headers: {
                            Authorization: `Bearer ${userToken.value}`,
                        },
                        responseType: 'blob', // IMPORTANT: receive as blob
                    }
                );

                // Now response.data is the Blob
                const blob = response.data;

                // Trigger download
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `matricula_unificada_${anio_proceso}_etapa_${etapa}_PIU.csv`;
                a.style.display = 'none';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                toast.add({ severity: 'success', summary: 'Éxito', group: 'bl', detail: 'Datos exportados correctamente', life: 5000 });


            }
            catch (error) {
                if (error.status === 403) {
                    toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El registro no puede ser modificado', life: 5000 });
                }
                console.error("Error fetching data:", error);
            }
        }

        const eliminarInmueble = async (etapa_actual) => {
            errorMessage.value = '';
            try {
                dialogIsLoading.value = true;
                // The URL in the axios.delete call should match exactly with your backend route
                const response = await axios.delete(API_BASE_URL + `mu_postgrado_postitulo/estudiantes/${anio_proceso}/${etapa_actual}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                console.log(response);
                if (response.status == 204) {
                    muPpHasFinalized.value = true;
                    deleteHasErrors.value = false;
                    await getMUPpData();
                }
            }
            catch (error) {
                console.error("Error during deletion:", error);
                if (error.response && error.response.status === 404) {
                    muPpHasFinalized.value = true;
                    deleteHasErrors.value = true;
                    errorMessage.value = 'El elemento no debe tener registros asociados a él';
                }
                if (error.response && error.response.status === 422) {
                    muPpHasFinalized.value = true;
                    deleteHasErrors.value = true;
                    errorMessage.value = 'Los registros ya fueron validados y no pueden ser eliminados';
                }

            } finally {
                dialogIsLoading.value = false;
            }
        }

        return {
            isLoading,
            globalLoading,
            anio_proceso,
            etapa_actual,
            hasAccess,
            goBack,
            mu_pp_data,
            dialogVisibleEliminar,
            dialogIsLoading,
            deletePreConfirmation,
            muPpHasFinalized,
            resetForm,
            validarMatriculaUnificada,
            dialogConfirmarMU_post,
            dialogIsLoading,
            validatePreConfirmation,
            errorMessage,
            pending_text,
            finalized_text,
            tooltipTextEtapa1,
            tooltipTextEtapa2,
            tooltipTextEtapa3,
            tooltipTextEtapa4,
            tooltipTextEtapa5,
            dialogCargarDatosisVisible,
            cargarDatosIsLoading,
            cargarDatosHasError,
            onSelect,
            fileUploadRef,
            onUpload,
            currentEtapaForDialog,
            errorData,
            downloadErrors,
            cargarDatosisCorrect,
            exportarDatosCSV,
            eliminarInmueble,
            deleteHasErrors,
            validarhasErrors
        }
    }
}

</script>
<style scoped>
:deep() .center-header {
    text-align: center !important;
    align-content: center !important;
}

:deep() .p-datatable .p-column-header-content {
    display: block !important;

}

.yellow-icon .pi {
    color: yellow;
}

.green-icon .pi {
    color: greenyellow;
}

:deep(.p-fileupload-file-thumbnail) {
    display: none !important;
}
</style>