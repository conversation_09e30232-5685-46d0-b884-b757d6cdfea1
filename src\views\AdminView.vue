<template>
   <div>
      <h1>Administrador VIEW</h1>
   </div>
   <!-- Cards -->
   <div v-if="!authStore.loading">
      <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
         <div class='surface-card shadow-2 p-3 border-round'>

            <div class="flex justify-content-between mb-3">
               <div>
                  <h2>Bienvenido</h2>
               </div>
               <div class="flex align-items-center justify-content-center bg-blue-100 border-round"
                  style="width:2.5rem;height:2.5rem">
                  <i class="pi pi-users text-blue-500 text-xl"></i>
               </div>
            </div>
            <p>{{ userName }}</p>
            <p>{{ userEmail }}</p>
            <p>usuario autorizado: {{ isUserAuthenticated }}</p>
            <p v-if="permissions.length > 0">{{ permissions[0].rol }}</p>
            <p v-else>No permissions available</p>


         </div>
      </div>
   </div>
</template>

<script>
import { useAuthStore } from '../store/auth';
import { ref, computed } from 'vue';

export default {

   name: "Admin",

   setup() {
      const authStore = useAuthStore();
      // Access getters
      // Computed properties for userName and userEmail
      const userName = computed(() => authStore.userName);
      const userEmail = computed(() => authStore.userEmail);
      const permissions = computed(() => authStore.permissions);
      const isUserAuthenticated = computed(() => authStore.userIsAuthenticated);
      const theme = ref("dark");

      // Return data object
      return {
         theme,
         authStore,
         userName,
         userEmail,
         permissions,
         isUserAuthenticated
      };
   }
}
</script>