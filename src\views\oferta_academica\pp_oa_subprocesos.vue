<template>
    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading">
            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <form @submit.prevent="submitForm">
                    <div class='surface-card shadow-3 p-3 border-round'>
                        <div
                            style="display: flex;justify-content: space-between;align-items: center;padding-left: 0.1rem;">
                            <h2>Oferta Academica Proceso {{ anio_proceso }}</h2>
                            <Button
                                v-if="hasAccess('Encargado', 'Matricula Unificada') || hasAccess('Usuario General', 'Matricula Unificada') || hasAccess('Administrador Sistema', 'Administracion PIU')"
                                @click="dialogVisibleCrear = true">Nuevo</Button>
                        </div>

                        <DataTable :value="oa_data" stripedRows showGridlines paginator :rows="10"
                            sort-field="tipo" :sort-order=2 tableStyle="min-width: 50rem">
                            <Column field="tipo" header="Tipo" class="center-header"></Column>
                            <Column field="etapa_actual" header="Etapa Actual" class="center-header"></Column>

                            <Column field="validado" header="Estado" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.validado == null || slotProps.data.validado == false"
                                        v-tooltip.bottom="pending_text">
                                        <Button disabled icon="pi pi-hourglass"
                                            style="justify-content: center;color: orangered;" class="p-button-text" />
                                    </div>
                                    <div v-else v-tooltip.bottom="finalized_text">
                                        <Button disabled icon="pi pi-verified"
                                            style="justify-content: center;color: green"
                                            class="p-button-text green-icon" />
                                    </div>
                                </template>
                            </Column>

                            <!-- <Column field="Fechas" header="Fechas Etapas" class="center-header" style="width: 15%;">

                                <template #body="slotProps">
                                    <div style="display: flex; justify-content: center; align-items: center;">
                                        <Button icon="pi pi-calendar" style="justify-content: center;"
                                            class="p-button-text"
                                            @click="goToDatesMU(slotProps.data.anio, slotProps.data.etapa_actual, slotProps.data.is_finalized)" />
                                        <div v-if="slotProps.data.fecha_etapa_count == 5"
                                            v-tooltip.bottom="'Fechas definidas'">
                                            <Button icon="pi pi-check" class="p-button-text" style="color: green;"
                                                disabled></Button>
                                        </div>
                                        <div v-else v-tooltip.bottom="'Fechas no definidas'">
                                            <Button icon="pi pi-times" class="p-button-text" style="color: red;"
                                                disabled></Button>
                                        </div>
                                    </div>
                                </template>
                            </Column> -->
                            <!-- <Column field="subprocesos" header="Subprocesos" class="center-header">
                                <template #body="slotProps">
                                    <Button icon="pi pi-list"
                                        style="justify-content: center;" class="p-button-text"
                                        @click="goToSubprocesos(slotProps.data.proceso_id,slotProps.data.anio)" />
                                </template>
                            </Column> -->
                            <Column field="ver" header="Ver" class="center-header">
                                <template #body="slotProps">
                                    <!-- v-if="slotProps.data.fecha_etapa_count === 5" -->
                                    <Button  icon="pi pi-eye"
                                        style="justify-content: center;" class="p-button-text"
                                        @click="goToEtapas(slotProps.data.id,slotProps.data.tipo, anio_proceso)" />
                                </template>
                            </Column>
                            <Column
                                v-if="hasAccess('Encargado', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU')"
                                field="eliminar" header="Eliminar" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.is_finalized != true">
                                        <Button icon="pi pi-trash" class="p-button-text"
                                            @click="dialogVisibleEliminar = true, selectedSubproceso = slotProps.data.id" />
                                    </div>
                                </template>
                            </Column>
                            <!-- <Column
                                v-if="hasAccess('Encargado', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU')"
                                field="Exportar" header="Exportar" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.is_finalized == true">
                                        <Button icon="pi pi-download" style="" class="p-button-text"
                                            @click="obtenerOASeleccionado(slotProps.data.anio)" />
                                    </div>
                                </template>
                            </Column> -->
                        </DataTable>

                    </div>
                </form>
            </div>

        </div>
        <div v-else class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
                <Loading />
            </div>
        </div>
        <Toast position="bottom-left" group="bl" />
    </div>
    <Dialog v-model:visible="dialogVisibleCrear" modal header="Nuevo Registro" :style="{ width: '30rem' }"

        v-on:hide="resetForm()">
        <div v-if="dialogIsLoading && !oaIsCorrect">
            <Loading />
        </div>

        <!-- Form to create a new report -->
        <div v-if="!dialogIsLoading && !oaIsCorrect">
            <span class="p-text-secondary block mb-5">Crear nuevo subproceso para Oferta Academica {{ anio_proceso }}</span>

            <!-- Year input with validation -->
            <div class="flex align-items-center gap-3 mb-3" style="justify-content: space-between;">
                <div>
                    <label for="tipo" class="font-semibold w-6rem">Tipo </label>
                    <i class="pi pi-info-circle" style="justify-self: center" v-tooltip.bottom="tooltipText" />
                </div>
                <!-- <Dropdown v-model="selectedType" :options="typeOptions" optionLabel="name" placeholder="Select Type" class="w-full md:w-14rem" /> -->
                <div>
                    <Dropdown v-model="inputTipo" :options="[1, 2]" placeholder="Seleccione Tipo" 
                        class="w-full" :class="{ 'p-invalid': inputYearIsInvalid }" />
                </div>

            </div>


            <!-- Validation error message -->
            <div style="text-align: center;" v-if="errorMessageYearIsInvalid" class="p-error block mb-3">{{
                errorMessageYearIsInvalid }}
            </div>
            <div>
                <!-- Etapa 1 Dates -->
                <h4>Etapa 1</h4>
                <div class="flex align-items-center gap-3 mb-3" style="justify-content: space-between;">
                    <div>
                        <label for="etapa1FechaInicio" class="font-semibold w-6rem">Fecha Inicio </label>
                        <i class="pi pi-info-circle" style="justify-content: center" v-tooltip.bottom="tooltipText" />
                    </div>
                    <Calendar v-model="etapa1_fecha_inicial" dateFormat="dd/mm/yy" style="width: 58%;"
                        :disabled="!inputTipo" :showIcon="true"
                        :class="{ 'p-invalid': etapa1_fecha_inicial_isInvalid }" />
                </div>
                <div style="text-align: center;" v-if="errorMsg_Etapa1FechaInicio" class="p-error block mb-3">{{
                    errorMsg_Etapa1FechaInicio }}
                </div>
                <div class="flex align-items-center gap-3 mb-3" style="justify-content: space-between;">
                    <div>
                        <label for="etapa1FechaTermino" class="font-semibold w-6rem">Fecha Término </label>
                        <i class="pi pi-info-circle" style="justify-content: center" v-tooltip.bottom="tooltipText" />
                    </div>
                    <Calendar v-model="etapa1_fecha_final" dateFormat="dd/mm/yy" style="width: 58%;"
                        :disabled="!inputTipo" :showIcon="true"
                        :class="{ 'p-invalid': etapa1_fecha_final_isInvalid }" />
                </div>
                <div style="text-align: center;" v-if="errorMsg_Etapa1FechaTermino" class="p-error block mb-3">{{
                    errorMsg_Etapa1FechaTermino }}
                </div>

                <!-- Etapa 2 Dates -->
                <h4>Etapa 2</h4>
                <div class="flex align-items-center gap-3 mb-3" style="justify-content: space-between;">
                    <div>
                        <label for="etapa2FechaInicio" class="font-semibold w-6rem">Fecha Inicio </label>
                        <i class="pi pi-info-circle" style="justify-content: center" v-tooltip.bottom="tooltipText" />
                    </div>
                    <Calendar v-model="etapa2_fecha_inicial" dateFormat="dd/mm/yy" style="width: 58%;"
                        :disabled="!inputTipo" :showIcon="true"
                        :class="{ 'p-invalid': etapa2_fecha_inicial_isInvalid }" />
                </div>
                <div style="text-align: center;" v-if="errorMsg_Etapa2FechaInicio" class="p-error block mb-3">{{
                    errorMsg_Etapa2FechaInicio }}
                </div>
                <div class="flex align-items-center gap-3 mb-3" style="justify-content: space-between;">
                    <div>
                        <label for="etapa2FechaTermino" class="font-semibold w-6rem">Fecha Término </label>
                        <i class="pi pi-info-circle" style="justify-content: center" v-tooltip.bottom="tooltipText" />
                    </div>
                    <Calendar v-model="etapa2_fecha_final" dateFormat="dd/mm/yy" style="width: 58%;"
                        :disabled="!inputTipo" :showIcon="true"
                        :class="{ 'p-invalid': etapa2_fecha_final_isInvalid }" />
                </div>
                <div style="text-align: center;" v-if="errorMsg_Etapa2FechaTermino" class="p-error block mb-3">{{
                    errorMsg_Etapa2FechaTermino }}
                </div>
            </div>
            <!-- Etapa 3 Dates (only if tipo is 2) -->
            <div v-if="inputTipo === 2">
                <h4>Etapa 3</h4>
                <div class="flex align-items-center gap-3 mb-3" style="justify-content: space-between;">
                    <div>
                        <label for="etapa3FechaInicio" class="font-semibold w-6rem">Fecha Inicio </label>
                        <i class="pi pi-info-circle" style="justify-content: center" v-tooltip.bottom="tooltipText" />
                    </div>
                    <Calendar v-model="etapa3_fecha_inicial" dateFormat="dd/mm/yy" style="width: 58%;"
                        :disabled="!inputTipo" :showIcon="true"
                        :class="{ 'p-invalid': etapa3_fecha_inicial_isInvalid }" />
                </div>
                <div style="text-align: center;" v-if="errorMsg_Etapa3FechaInicio" class="p-error block mb-3">{{
                    errorMsg_Etapa3FechaInicio }}
                </div>
                <div class="flex align-items-center gap-3 mb-3" style="justify-content: space-between;">
                    <div>
                        <label for="etapa3FechaTermino" class="font-semibold w-6rem">Fecha Término </label>
                        <i class="pi pi-info-circle" style="justify-content: center" v-tooltip.bottom="tooltipText" />
                    </div>
                    <Calendar v-model="etapa3_fecha_final" dateFormat="dd/mm/yy" style="width: 58%;"
                        :disabled="!inputTipo" :showIcon="true"
                        :class="{ 'p-invalid': etapa3_fecha_final_isInvalid }" />
                </div>
                <div style="text-align: center;" v-if="errorMsg_Etapa3FechaTermino" class="p-error block mb-3">{{
                    errorMsg_Etapa3FechaTermino }}
                </div>
            </div>
            <!-- Buttons -->
            <div class="flex justify-content-end gap-2">
                <Button type="button" label="Cancelar" severity="secondary"
                    @click="dialogVisibleCrear = false"></Button>
                <Button type="button" label="Crear" @click="createOASubProceso"></Button>
            </div>
        </div>

        <!-- Success message -->
        <div v-if="!dialogIsLoading && oaIsCorrect"
            style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
            <i class="pi pi-check" style="font-size: 5rem; color: blue;"></i>
            <span class="p-text-secondary block mb-5">¡Creación exitosa!</span>
        </div>
    </Dialog>
    <Dialog v-model:visible="dialogVisibleEliminar" modal header="Eliminar Registro" :style="{ width: '25rem' }"
        v-on:hide="resetForm()">
        <div v-if="dialogIsLoading && !oaIsCorrect">
            <Loading />
        </div>

        <div v-if="!dialogIsLoading && !oaIsCorrect">
            <div>
                <!-- Validation error message -->
                <p>Esto eliminará permanentemente este registro ¿Está seguro?</p>
            </div>
            <br />
            <div class="flex justify-content-center gap-2">
                <InputSwitch style="scale: 1.5;" v-model="deletePreConfirmation"></InputSwitch>
            </div>
            <br />
            <!-- Validation error message -->
            <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}<br /></div>


            <!-- Buttons -->
            <div class="flex justify-content-end gap-2">
                <Button type="button" label="Cancelar" severity="secondary"
                    @click="dialogVisibleEliminar = false"></Button>
                <Button v-if="deletePreConfirmation" type="button" label="Eliminar"
                    @click="eliminarOAProcesos"></Button>
            </div>
        </div>

        <!-- Success message -->
        <div v-if="!dialogIsLoading && oaIsCorrect"
            style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
            <i class="pi pi-trash" style="font-size: 5rem; color: blue;"></i>
            <br />
            <span class="p-text-secondary block mb-5">¡Eliminado correctamente!</span>
        </div>

    </Dialog>

</template>

<script>
import { useAuthStore } from '../../store/auth';
import { ref, computed, onMounted } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import axios from 'axios';
// import Papa from 'papaparse'; // Unused import
import { useToast } from 'primevue/usetoast';
import { useRouter } from 'vue-router';

export default {
    name: "pp_oa_subprocesos",

    // Add props declaration
    props: {
        proceso_id: {
            type: String,
            required: true
        },
        anio_proceso: {
            type: String,
            required: true
        }
    },

    setup(props) {  // Add props parameter here
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const router = useRouter();
        // const route = useRoute(); // Unused variable
        const toast = useToast();
        const authStore = useAuthStore();
        const permissionsList = computed(() => decryptData(authStore.permissionsList));
        const globalLoading = computed(() => decryptData(authStore.isLoading));
        const userToken = computed(() => decryptData(authStore.idToken));
        // const userEmail = computed(() => decryptData(authStore.userEmail)); // Unused variable

        // Remove these computed properties since we're now using props
        // const proceso_id = computed(() => decryptData(route.params.proceso_id));
        // const anio_proceso = computed(() => decryptData(route.params.anio_proceso));

        // Use props instead
        const proceso_id = computed(() => decryptData(props.proceso_id));
        const anio_proceso = computed(() => decryptData(props.anio_proceso));

        const isLoading = ref(false);
        const oa_data = ref([]);
        const dialogConfirmarMU_post = ref(false);
        const selectedSubproceso = ref(null);
        const dialogVisibleCrear = ref(false);
        const dialogVisibleEliminar = ref(false);
        const inputYear = ref(null);
        const inputTipo = ref(null);
        const inputYearIsInvalid = ref(false);
        const inputProceso = ref(null);
        const tooltipText = ref("texto ejemplo");
        const errorMessage = ref('');
        const errorMessageYearIsInvalid = ref('');
        // Removed unused variables
        const oaIsCorrect = ref(false);
        const oaAnioSelecionado = ref(null);
        const deletePreConfirmation = ref(false);
        const dialogIsLoading = ref(false);
        const validatePreConfirmation = ref(false);
        const dialogConfirmarMU = ref(false);
        const finalized_text = ref("Finalizado");
        const pending_text = ref("Pendiente");
        // Etapa 1 variables
        const etapa1_fecha_inicial = ref(new Date()); // Initialize with current date
        const etapa1_fecha_inicial_isInvalid = ref(false);
        const etapa1_fecha_final = ref(new Date()); // Initialize with current date
        const etapa1_fecha_final_isInvalid = ref(false);
        const errorMsg_Etapa1FechaInicio = ref('');
        const errorMsg_Etapa1FechaTermino = ref('');

        // Etapa 2 variables
        const etapa2_fecha_inicial = ref(new Date()); // Initialize with current date
        const etapa2_fecha_inicial_isInvalid = ref(false);
        const etapa2_fecha_final = ref(new Date()); // Initialize with current date
        const etapa2_fecha_final_isInvalid = ref(false);
        const errorMsg_Etapa2FechaInicio = ref('');
        const errorMsg_Etapa2FechaTermino = ref('');

        // Etapa 3 variables
        const etapa3_fecha_inicial = ref(new Date()); // Initialize with current date
        const etapa3_fecha_inicial_isInvalid = ref(false);
        const etapa3_fecha_final = ref(new Date()); // Initialize with current date
        const etapa3_fecha_final_isInvalid = ref(false);
        const errorMsg_Etapa3FechaInicio = ref('');
        const errorMsg_Etapa3FechaTermino = ref('');

        const minDateFechaInicio = computed(() => new Date(inputYear.value - 1, 0, 1));
        const maxDateFechaInicio = computed(() => new Date(inputYear.value + 1, 11, 31));
        const minDateFechaTermino = computed(() => new Date(inputYear.value - 1, 0, 1));
        const maxDateFechaTermino = computed(() => new Date(inputYear.value + 1, 11, 31));


        const hasAccess = (allowedRol, requiredRecurso) => {
            const access = permissionsList.value.some(user => user.rol === allowedRol && user.recurso === requiredRecurso);
            if (access) {
                return true;
            } else {
                return false;
            }

        };

        onMounted(async () => {
            isLoading.value = true;
            await getOASubProcesosByProcesoId();
            //await getAllMuPregradoFecha();
            console.log(oa_data.value)
            isLoading.value = false;
        });

        // Function to reset the form for a new creation
        const resetForm = () => {
            // Reset basic form state
            inputYear.value = null;
            inputTipo.value = null;
            errorMessage.value = '';
            errorMessageYearIsInvalid.value = '';
            inputYearIsInvalid.value = false;
            oaIsCorrect.value = false;

            // Reset Etapa 1 fields
            etapa1_fecha_inicial.value = ref(new Date());
            etapa1_fecha_inicial_isInvalid.value = false;
            etapa1_fecha_final.value = ref(new Date());
            etapa1_fecha_final_isInvalid.value = false;
            errorMsg_Etapa1FechaInicio.value = '';
            errorMsg_Etapa1FechaTermino.value = '';

            // Reset Etapa 2 fields
            etapa2_fecha_inicial.value = ref(new Date());
            etapa2_fecha_inicial_isInvalid.value = false;
            etapa2_fecha_final.value = ref(new Date());
            etapa2_fecha_final_isInvalid.value = false;
            errorMsg_Etapa2FechaInicio.value = '';
            errorMsg_Etapa2FechaTermino.value = '';

            // Reset Etapa 3 fields
            etapa3_fecha_inicial.value = ref(new Date());
            etapa3_fecha_inicial_isInvalid.value = false;
            etapa3_fecha_final.value = ref(new Date());
            etapa3_fecha_final_isInvalid.value = false;
            errorMsg_Etapa3FechaInicio.value = '';
            errorMsg_Etapa3FechaTermino.value = '';

            // Reset dialog states
            dialogVisibleCrear.value = false;
            deletePreConfirmation.value = false;
            dialogVisibleEliminar.value = false;
            validatePreConfirmation.value = false;
            dialogConfirmarMU.value = false;
        }
        const goToEtapas = (subproceso_id,subproceso,anio_proceso) => {
            console.log('Navigating to etapas:', subproceso_id, anio_proceso);
            const encrypted_subproceso_id = encryptData(subproceso_id);
            const encrypted_anio= encryptData(anio_proceso);
            const encrypted_subproceso= encryptData(subproceso);
            router.push({ name: 'oferta-academica-sies-etapas', params: { subproceso_id: encrypted_subproceso_id,subproceso:encrypted_subproceso,  anio_proceso: encrypted_anio } });
        };

        // const goToSubprocesos = (proceso_id,anio_proceso) => {
        //     // const encrypted_proceso_id = encryptData(proceso_id);
        //     // const encrypted_anio_proceso = encryptData(anio_proceso);
        //     // router.push({ name: 'oferta-academica-sies-subprocesos', params: { proceso_id: encrypted_proceso_id, anio_proceso: encrypted_anio_proceso } });
        // };
        // This function is currently not used but kept for future implementation
        const goToDatesMU = () => {
            // Implementation will be added in the future
            console.log('goToDatesMU function is not implemented yet');
        };
        const createOASubProceso = async () => {
            // Clear previous error messages
            errorMessage.value = '';
            inputYearIsInvalid.value = false;
            errorMessageYearIsInvalid.value = '';

            // Clear Etapa 1 error messages
            errorMsg_Etapa1FechaInicio.value = '';
            etapa1_fecha_inicial_isInvalid.value = false;
            errorMsg_Etapa1FechaTermino.value = '';
            etapa1_fecha_final_isInvalid.value = false;

            // Clear Etapa 2 error messages
            errorMsg_Etapa2FechaInicio.value = '';
            etapa2_fecha_inicial_isInvalid.value = false;
            errorMsg_Etapa2FechaTermino.value = '';
            etapa2_fecha_final_isInvalid.value = false;

            let hasError = false;

            // Validate Etapa 1 dates
            if (!etapa1_fecha_inicial.value) {
                errorMsg_Etapa1FechaInicio.value = 'La fecha de inicio de Etapa 1 es obligatoria';
                etapa1_fecha_inicial_isInvalid.value = true;
                hasError = true;
            }

            if (!etapa1_fecha_final.value) {
                errorMsg_Etapa1FechaTermino.value = 'La fecha de término de Etapa 1 es obligatoria';
                etapa1_fecha_final_isInvalid.value = true;
                hasError = true;
            }

            if (etapa1_fecha_inicial.value && etapa1_fecha_final.value && etapa1_fecha_inicial.value > etapa1_fecha_final.value) {
                errorMsg_Etapa1FechaInicio.value = 'La fecha de inicio no puede ser mayor que la fecha de término';
                etapa1_fecha_inicial_isInvalid.value = true;
                etapa1_fecha_final_isInvalid.value = true;
                hasError = true;
            }

            // Validate Etapa 2 dates
            if (!etapa2_fecha_inicial.value) {
                errorMsg_Etapa2FechaInicio.value = 'La fecha de inicio de Etapa 2 es obligatoria';
                etapa2_fecha_inicial_isInvalid.value = true;
                hasError = true;
            }

            if (!etapa2_fecha_final.value) {
                errorMsg_Etapa2FechaTermino.value = 'La fecha de término de Etapa 2 es obligatoria';
                etapa2_fecha_final_isInvalid.value = true;
                hasError = true;
            }

            if (etapa2_fecha_inicial.value && etapa2_fecha_final.value && etapa2_fecha_inicial.value > etapa2_fecha_final.value) {
                errorMsg_Etapa2FechaInicio.value = 'La fecha de inicio no puede ser mayor que la fecha de término';
                etapa2_fecha_inicial_isInvalid.value = true;
                etapa2_fecha_final_isInvalid.value = true;
                hasError = true;
            }

            // Validate that Etapa 1 ends before or at the same time as Etapa 2 starts
            if (etapa1_fecha_final.value && etapa2_fecha_inicial.value && etapa1_fecha_final.value > etapa2_fecha_inicial.value) {
                errorMsg_Etapa1FechaTermino.value = 'La Etapa 1 debe terminar antes o al mismo tiempo que comienza la Etapa 2';
                etapa1_fecha_final_isInvalid.value = true;
                hasError = true;
            }

            // Validate Etapa 3 only if tipo is 2
            if (inputTipo.value === 2) {
                if (!etapa3_fecha_inicial.value) {
                    errorMsg_Etapa3FechaInicio.value = 'La fecha de inicio de Etapa 3 es obligatoria';
                    etapa3_fecha_inicial_isInvalid.value = true;
                    hasError = true;
                }

                if (!etapa3_fecha_final.value) {
                    errorMsg_Etapa3FechaTermino.value = 'La fecha de término de Etapa 3 es obligatoria';
                    etapa3_fecha_final_isInvalid.value = true;
                    hasError = true;
                }

                if (etapa3_fecha_inicial.value && etapa3_fecha_final.value && etapa3_fecha_inicial.value > etapa3_fecha_final.value) {
                    errorMsg_Etapa3FechaInicio.value = 'La fecha de inicio no puede ser mayor que la fecha de término';
                    etapa3_fecha_inicial_isInvalid.value = true;
                    etapa3_fecha_final_isInvalid.value = true;
                    hasError = true;
                }

                // Validate that Etapa 2 ends before or at the same time as Etapa 3 starts
                if (etapa2_fecha_final.value && etapa3_fecha_inicial.value && etapa2_fecha_final.value > etapa3_fecha_inicial.value) {
                    errorMsg_Etapa2FechaTermino.value = 'La Etapa 2 debe terminar antes o al mismo tiempo que comienza la Etapa 3';
                    etapa2_fecha_final_isInvalid.value = true;
                    hasError = true;
                }
            }

            if (hasError) {
                return;
            }

            dialogIsLoading.value = true;
            await getOASubProcesosByProcesoId();

            // Check if there's already a subproceso with the same tipo for this proceso
            const isDuplicate = oa_data.value.some(item => item.tipo === inputTipo.value);
            console.log('isDuplicate:', isDuplicate);

            if (isDuplicate) {
                // If a match is found, throw the error
                errorMessageYearIsInvalid.value = 'Ya existe un subproceso con este tipo';
                inputYearIsInvalid.value = true;
                dialogIsLoading.value = false; // Stop loading since an error occurred
                return; // Prevent further execution
            } else {
                const response = await postOASubProceso();
                dialogIsLoading.value = false;
                if (response == "ok") {
                    oaIsCorrect.value = true
                    // If no match is found, proceed with saving the report
                    console.log('Saving subproceso with etapas');
                    await getOASubProcesosByProcesoId();
                }
            }
        }

        const postOASubProceso = async () => {
            // Define the request body for creating subproceso with etapas
            const requestBody = {
                proceso_id: proceso_id.value,
                tipo: inputTipo.value,
                etapa1_fecha_inicial: etapa1_fecha_inicial.value,
                etapa1_fecha_final: etapa1_fecha_final.value,
                etapa2_fecha_inicial: etapa2_fecha_inicial.value,
                etapa2_fecha_final: etapa2_fecha_final.value,
                etapa3_fecha_inicial: inputTipo.value === 2 ? etapa3_fecha_inicial.value : null,
                etapa3_fecha_final: inputTipo.value === 2 ? etapa3_fecha_final.value : null
            };
            try {
                dialogIsLoading.value = true;

                await axios.post(API_BASE_URL + `oa_subproceso/with_etapas/${inputTipo.value}`, requestBody, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                return "ok";
            } catch (error) {
                console.error("Error creating subproceso with etapas: ", error);
                toast.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Error al crear el subproceso con etapas: ' + (error.response?.data?.message || error.message),
                    group: "bl",
                    life: 5000
                });
            } finally {
                dialogIsLoading.value = false;
            }
        };
//getMUPregradoData
        const getOASubProcesosByProcesoId = async () => {
            // console.log("id y anio")
            // console.log(proceso_id.value)
            // console.log(anio_proceso.value)
            try {
                isLoading.value = true;
                const response = await axios.get(API_BASE_URL + `oa_subproceso/proceso/${proceso_id.value}`, {

                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });

                if (response.status == 200) {
                    oa_data.value = response.data;
                }
            } catch (error) {
                console.error("Error: ", error);
            } finally {
                isLoading.value = false;
            }
        };

        // const asd = async () => {
        //     try {
        //         isLoading.value = true;

        //         const response = await axios.get(API_BASE_URL + "oa_proceso", {
        //             headers: {
        //                 Authorization: `Bearer ${userToken.value}`
        //             }
        //         });

        //         if (response.status == 200) {
        //             oa_data.value = response.data;
        //         }
        //     } catch (error) {
        //         console.error("Error: ", error);
        //     } finally {
        //         isLoading.value = false;
        //     }
        // };
        const getAllMuPregradoFecha = async () => {
            try {
                isLoading.value = true;

                const response = await axios.get(`${API_BASE_URL}mu_pregrado_fecha`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    },
                });
                console.log(response.data);
                /*if (response.status == 200) {
                    mu_pregrado_fecha_data.value = response.data;

                    if (mu_pregrado_fecha_data.value.length < 5) {
                        const currentLength = mu_pregrado_fecha_data.value.length;
                        for (let i = currentLength; i < 5; i++) {
                            mu_pregrado_fecha_data.value.push({
                                etapa_proceso: i + 1,
                                fecha_inicio: "Sin definir",
                                fecha_termino: "Sin definir"
                            });
                        }
                    }
                    mu_pregrado_fecha_data.value = mu_pregrado_fecha_data.value.map(item => {
                        return {
                            ...item,
                            estado_etapa: item.etapa_proceso < mu_pregrado_data_actual.value.etapa_actual ? true : false
                        };
                    });
                } else if (response.status == 204) {
                    mu_pregrado_fecha_data.value = [];
                } else {
                    toast.add({
                        severity: 'error',
                        summary: 'Error',
                        detail: 'Error al cargar los datos de la tabla, intentelo nuevamente.',
                        group: "bl",
                        life: 3000
                    });
                    console.error("Error: ", response);
                }*/
            } catch (error) {
                toast.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Error al cargar los datos de la tabla, intentelo nuevamente.',
                    group: "bl",
                    life: 3000
                });
                console.error("Error: ", error);
            } finally {
                isLoading.value = false;
            }
        }

        const eliminarOAProcesos = async () => {
            errorMessage.value = '';

            const eliminarResponse = await deleteSubProceso();

            if (eliminarResponse == "ok") {
                oaIsCorrect.value = true;
                console.log('Deleting subprocess:', selectedSubproceso.value);
                await getOASubProcesosByProcesoId();
            } else if (eliminarResponse == "conflicto") {
                errorMessage.value = 'El elemento no debe tener registros asociados a él';
            } else if (eliminarResponse == "error") {
                errorMessage.value = 'Se detectó un error, inténtelo más tarde';
            }
        }

        const deleteSubProceso = async () => {
            errorMessage.value = '';

            try {
                dialogIsLoading.value = true;
                await axios.delete(API_BASE_URL + `oa_subproceso/${selectedSubproceso.value}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                return "ok";
            } catch (error) {
                console.error("Error: ", error);
                if (error.response?.status === 409) {
                    return "conflicto";
                } else {
                    if (error.response?.status === 403) {
                        toast.add({
                            severity: 'error',
                            summary: 'Error',
                            group: 'bl',
                            detail: 'El registro no puede ser modificado',
                            life: 5000
                        });
                    }
                    return "error";
                }
            } finally {
                dialogIsLoading.value = false;
            }
        }

        const obtenerOASeleccionado = async (anioProceso) => {
            try {
                const response = await axios.get(API_BASE_URL + `oa_proceso/${anioProceso}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                oaAnioSelecionado.value = await response.data;
                if (oaAnioSelecionado.value.is_finalized) {
                    console.log(oaAnioSelecionado.value);
                    exportDataToCSV(anioProceso);
                } else {
                    toast.add({
                        severity: 'error',
                        summary: 'Error',
                        group: 'bl',
                        detail: 'El registro no puede ser modificado',
                        life: 5000
                    });
                }
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        }


        return {
            anio_proceso,
            proceso_id,
            globalLoading,
            isLoading,
            hasAccess,
            oa_data,
            dialogConfirmarMU_post,
            selectedSubproceso,
            dialogVisibleCrear,
            dialogVisibleEliminar,
            resetForm,
            inputYear,
            inputTipo,
            inputProceso,
            tooltipText,
            errorMessage,
            errorMessageYearIsInvalid,
            oaIsCorrect,
            deletePreConfirmation,
            dialogIsLoading,
            validatePreConfirmation,
            dialogConfirmarMU,
            createOASubProceso,
            finalized_text,
            pending_text,
            eliminarOAProcesos,
            obtenerOASeleccionado,
            oaAnioSelecionado,
            goToEtapas,
            goToDatesMU,
            minDateFechaInicio,
            maxDateFechaInicio,
            minDateFechaTermino,
            maxDateFechaTermino,
            inputYearIsInvalid,
            getAllMuPregradoFecha,

            // Etapa 1 variables
            etapa1_fecha_inicial,
            etapa1_fecha_inicial_isInvalid,
            etapa1_fecha_final,
            etapa1_fecha_final_isInvalid,
            errorMsg_Etapa1FechaInicio,
            errorMsg_Etapa1FechaTermino,

            // Etapa 2 variables
            etapa2_fecha_inicial,
            etapa2_fecha_inicial_isInvalid,
            etapa2_fecha_final,
            etapa2_fecha_final_isInvalid,
            errorMsg_Etapa2FechaInicio,
            errorMsg_Etapa2FechaTermino,    

            // Etapa 3 variables
            etapa3_fecha_inicial,
            etapa3_fecha_inicial_isInvalid,
            etapa3_fecha_final,
            etapa3_fecha_final_isInvalid,
            errorMsg_Etapa3FechaInicio,
            errorMsg_Etapa3FechaTermino

        };

    }
}
</script>

<style scoped>
:deep() .center-header {
    text-align: center !important;
    align-content: center !important;
}

:deep() .p-datatable .p-column-header-content {
    display: block !important;

}

.yellow-icon .pi {
    color: yellow;
}

.green-icon .pi {
    color: greenyellow;
}
</style>




