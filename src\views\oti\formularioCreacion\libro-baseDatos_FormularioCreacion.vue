<template>
    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading">

            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <div class='surface-card shadow-3 p-3 border-round'>
                    <form @submit.prevent="submitForm">
                        <div
                            style="display: flex;justify-content: space-between;align-items: center;padding-left: 1rem;">
                            <div style="display: flex;justify-content: space-between;align-items: center;">
                                <Button @click.prevent="goBack()"> <- </Button>
                                        <h2 style="padding-left: 1rem;">Crear Libros y Bases de Datos Digitales -
                                            Proceso {{
                                                anio_proceso }}
                                        </h2>
                            </div>
                        </div>
                        <br />
                        <!--Informacion Basica de los inmuebles de uso restringido-->
                        <div>
                            <h3 style="padding-left: 1rem;">Información básica</h3>
                            <div style="margin-left: 1rem;margin-right: 1rem;">
                                <div v-if="true" class="formgrid grid">
                                    <div class="field col-6" v-for="(field, index) in formFormat1" :key="field.id">
                                        <label :for="field.name" style="padding-right: 1rem ;">{{ field.displayName
                                        }}</label>
                                        <i class="pi pi-info-circle" v-if="!field.dontNeedtoolTip"
                                            style="justify-content: center " v-tooltip.bottom="field.tooltip" />
                                        <InputText :class="{
                                            'w-full': true, 'p-invalid': (formData[field.name] == null || formData[field.name] == '') && submitted
                                        }" v-if="field.contenedor === 'inputText'" :id=field.name autocomplete="off"
                                            :placeholder=field.placeholder :type=field.type style="height:2.6rem"
                                            v-model="formData[field.name]" :disabled="field.disabled"
                                            @input="handleInput($event, field.name)" :maxlength="200">
                                        </InputText>
                                        <Dropdown :class="{
                                            'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                                        }" v-if="field.contenedor === 'dropdown'" v-model="formData[field.name]" filter
                                            :placeholder="field.placeholder" style="height:2.6rem;margin-bottom: 1rem"
                                            :options="getOptions(field.options)" :optionLabel="field.optionLabel"
                                            :optionValue="field.optionValue" class="w-full">
                                        </Dropdown>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--Características de los inmuebles de uso restringido-->
                        <div>
                            <h3 style="padding-left: 1rem;">Características de los libros y bases de datos digitales
                            </h3>
                            <div style="margin-left: 1rem;margin-right: 1rem;">
                                <div v-if="true" class="formgrid grid">
                                    <div class="field col-6" v-for="(field, index) in formFormat2" :key="field.id">
                                        <label :for="field.name" style="padding-right: 1rem ;">{{ field.displayName
                                        }}</label>
                                        <i class="pi pi-info-circle" v-if="!field.dontNeedtoolTip"
                                            style="justify-content: center " v-tooltip.bottom="field.tooltip" />
                                        <InputNumber :class="{
                                            'w-full': true, 'p-invalid': formData[field.name] == null &&
                                                submitted
                                        }" v-if="field.contenedor === 'inputNumber'" inputId="withoutgrouping"
                                            :useGrouping="false" id="inputNumber" class="w-full inputNumber"
                                            style="margin-top: 0.05rem;" v-model="formData[field.name]"
                                            :placeholder="field.placeholder" :inputProps="{ autocomplete: 'off' }"
                                            :min="0" :max="9999999">
                                        </InputNumber>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Vigencia -->
                        <div>
                            <h3 style="padding-left: 1rem;">Vigencia de los registros de infraestructura informados</h3>
                            <div style="margin-left: 1rem;margin-right: 1rem;">
                                <div v-if="true" class="formgrid grid">
                                    <div class="field col-6" v-for="(field, index) in formFormat3" :key="field.id">
                                        <label style="padding-right: 1rem;" :for="field.name">{{ field.displayName
                                        }}</label>
                                        <i style="justify-content: center" class=" pi pi-info-circle"
                                            v-tooltip.bottom="field.tooltip" />
                                        <Dropdown :class="{
                                            'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                                        }" v-if="field.contenedor === 'dropdown'" v-model="formData[field.name]" filter
                                            :placeholder="field.placeholder" style="height:2.6rem;margin-bottom: 1rem"
                                            :options="getOptions(field.options)" :optionLabel="field.optionLabel"
                                            :optionValue="field.optionValue" class="w-full">
                                        </Dropdown>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            style="display: flex; justify-content: center; align-items: center;padding-top: 0.5rem;padding-bottom: 0.5rem;">
                            <Button label="Crear Inmueble" @click="submitForm()" />
                        </div>
                    </form>
                </div>
            </div>
            <Toast position="bottom-left" group="bl" />
        </div>
        <div v-else class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
                <Loading />
            </div>
        </div>
    </div>
</template>

<script>
import { useAuthStore } from '../../../store/auth';
import { ref, computed, onMounted, resolveDirective } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import axios from 'axios';
import InputText from 'primevue/inputtext';
import { useToast } from 'primevue/usetoast';
import { useRouter } from 'vue-router';

export default {
    props: {
        anio_proceso: {
            type: String,
            required: true,
        },
    },

    setup(props) {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const router = useRouter();
        const authStore = useAuthStore();
        const toast = useToast();
        // Obtain the stored user data from pinia and the global loading state
        const userName = computed(() => decryptData(authStore.userName));
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const permissions = computed(() => decryptData(authStore.permissions));
        const isUserAuthenticated = computed(() => decryptData(authStore.userIsAuthenticated));
        const globalLoading = computed(() => decryptData(authStore.isLoading));
        const userToken = computed(() => decryptData(authStore.idToken));
        const listaComunas = ref([]);

        // Variable to store InfraestructuraRecurso data
        const infraestructuraRecursoData = ref(null);

        // Theme for changing the actual theme from dark to light or viceversa
        const theme = ref("dark");

        // Local "Loading" for the current view
        const isLoading = ref(false);
        const formIsLoading = ref(false);

        const anio_proceso = decryptData(props.anio_proceso); // Access the param
        const submitted = ref(false);

        // Define initial form data
        const initialFormData = {
            tipo_infraestructura: "Libros y Bases de Datos Digitales",
            nombre_identificacion: null,
            comuna: null,
            direccion_inmueble: null,
            total_titulos_libros_digitales: null,
            total_suscripciones_digitales: null,
            total_base_datos: null,
            vigencia: 1,
            creador_email: userEmail.value,
            creador_id: "sin asignar",
            anio_proceso: anio_proceso
        };

        const formData = ref({ ...initialFormData });
        const formFormat1 = ref([
            { id: 1, placeholder: '', name: 'tipo_infraestructura', displayName: 'Seleccione tipo de infraestructura', type: 'text', contenedor: 'inputText', disabled: 'true', dontNeedtoolTip: true },
            { id: 2, placeholder: 'Nombre inmueble', name: 'nombre_identificacion', displayName: 'Ingrese nombre o identificacion del inmueble', type: 'text', contenedor: 'inputText', tooltip: 'Nombre con que la institución identifica el inmueble. Varios inmuebles cercanos pueden tener el mismo nombre o identificación.' },
            { id: 3, placeholder: 'Comuna', name: 'comuna', displayName: 'Seleccione comuna', type: 'text', contenedor: 'dropdown', options: 'listaComunas', optionLabel: 'nombre_comuna', optionValue: "nombre_comuna", tooltip: 'Corresponde a la comuna en que se encuentra el inmueble que está siendo informado.' },
            { id: 4, placeholder: 'Dirección inmueble', name: 'direccion_inmueble', displayName: 'Ingrese dirección del inmueble', type: 'text', contenedor: 'inputText', tooltip: 'Indicación del nombre de la calle y la numeración completa que corresponden a la ubicación física del inmueble que se está informando.' },
        ]);

        const formFormat2 = ref([
            { id: 5, placeholder: 'Total de libros digitales o electrónicos', name: 'total_titulos_libros_digitales', displayName: 'Ingrese total de libros digitales o electrónicos', contenedor: 'inputNumber', tooltip: 'Corresponde al número total de libros digitales o electrónicos (e-books) que posea la institución incorporados a su colección de biblioteca, sin distinción sobre si corresponden a versiones restringidas o de acceso ilimitado.' },
            { id: 6, placeholder: 'Total de suscripciones a revistas electrónicas', name: 'total_suscripciones_digitales', displayName: 'Ingrese suscripciones o canjes a revistas electrónicas', contenedor: 'inputNumber', tooltip: 'Corresponde al número de suscripciones o canjes a revistas electrónicas que poseen la institución.' },
            { id: 7, placeholder: 'Total de bases de datos', name: 'total_base_datos', displayName: 'Ingrese numero de bases de datos especializadas', contenedor: 'inputNumber', tooltip: 'Corresponde al número de suscripciones de acceso a bases de datos especializadas que poseen la institución.' },
        ]);
        const formFormat3 = ref([
            { id: 12, placeholder: 'Vigencia del inmueble', name: 'vigencia', displayName: 'Seleccione la vigencia del inmueble', contenedor: 'dropdown', options: 'vigencia_list', optionLabel: 'options', optionValue: "value", tooltip: 'Variable utilizada para mantener o eliminar un registro cargado.' },
        ]);

        const comuna_list = ref([
            { options: 'Antofagasta', value: "Antofagasta" },
            { options: 'Mejillones', value: "Mejillones" },
            { options: 'Sierra Gorda', value: "Sierra Gorda" },
            { options: 'Taltal', value: "Taltal" },
            { options: 'Calama', value: "Calama" },
            { options: 'San Pedro de Atacama', value: "San Pedro de Atacama" },
            { options: 'Tocopilla', value: "Tocopilla" },
            { options: 'María Elena', value: "María Elena" },
            { options: 'Providencia', value: "Providencia" },
        ]);
        const vigencia_list = ref([
            { options: 'Eliminar registro cargado', value: 0 },
            { options: 'Mantener registro cargado', value: 1 },

        ]);
        /**
        * Get options for the dropdown field based on the field options string
        */
        const getOptions = (optionKey) => {
            if (optionKey === 'listaComunas') {
                return listaComunas.value;
            }
            if (optionKey === 'vigencia_list') {
                return vigencia_list.value;
            }
            return [];
        }

        // Function to submit the form
        const submitForm = async () => {
            submitted.value = true;
            if (!validateForm()) {
                // Notify the user about the empty fields
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Existen campos vacios', life: 5000 });
                return;
            }

            try {
                isLoading.value = true;

                const success = await PostInfraestructuraRecursoData(formData.value);

                if (success) {
                    // Notify the user of successful form submission
                    toast.add({ severity: 'success', summary: 'Correcto', group: 'bl', detail: 'Formulario ingresado correctamente', life: 5000 });

                    // Reset form data after successful submission
                    formData.value = { ...initialFormData };
                    submitted.value = false; // Reset submitted state

                    // Redirect to the previous route with a success message
                    router.push({
                        name: 'Pagina-detalle-oti',
                        params: { anio_proceso: props.anio_proceso }, // Replace with the actual route name or path
                        query: { success: 'Formulario ingresado correctamente' }
                    });
                }
            } catch (error) {
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Hubo un problema al enviar el formulario', life: 5000 });
            } finally {
                isLoading.value = false;
            }
        };

        const validateForm = () => {
            let hasErrors = false;

            // Iterate through formData keys and validate non-conditional fields
            Object.keys(formData.value).forEach((key) => {
                const value = formData.value[key];

                // Check if the value is null or empty
                if (value === null || value === "") {
                    hasErrors = true;
                }
            });

            if (hasErrors) {
                // Return false if there are any errors
                return false;
            }
            return true;
        };


        const PostInfraestructuraRecursoData = async (formDataCompleted) => {
            isLoading.value = true; // Start loading

            try {
                if (formDataCompleted.nombre_identificacion != null && formDataCompleted.nombre_identificacion != "") {
                    formDataCompleted.nombre_identificacion = formDataCompleted.nombre_identificacion.trim();
                }
                if (formDataCompleted.direccion_inmueble != null && formDataCompleted.direccion_inmueble != "") {
                    formDataCompleted.direccion_inmueble = formDataCompleted.direccion_inmueble.trim();
                }
                formDataCompleted.userToken = "Bearer " + userToken.value;
                const response = await axios.post(API_BASE_URL + "librosBasesDigitales", formDataCompleted, {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                // Check if the response is successful (status code 200)
                if (response.status === 201) {
                    console.log("Success:", response.data); // Handle the response data if needed
                    return true; // Return true on success
                } else {
                    console.error("Unexpected response status:", response.status);
                    return false; // Return false if not 200 OK
                }
            } catch (error) {
                console.error("Error fetching InfraestructuraRecurso data:", error.response || error);
                return false; // Return false on error
            } finally {
                isLoading.value = false; // End loading
            }
        };

        // On component mount, fetch data
        onMounted(async () => {
            if (anio_proceso) {
                await fetchInfraestructuraRecursoData(anio_proceso);
                await fetchListaComunas();
            }
        });

        // Function to fetch InfraestructuraRecurso data based on the year
        const fetchInfraestructuraRecursoData = async (year) => {
            isLoading.value = true; // Start loading
            try {
                const response = await axios.get(API_BASE_URL + "infraestructuraRecurso/" + year, {
                    headers: {
                        'Authorization': "Bearer " + userToken.value
                    }
                });
                infraestructuraRecursoData.value = response.data;
                console.log(infraestructuraRecursoData.value);
            } catch (error) {
                console.error("Error fetching InfraestructuraRecurso data:", error);
            } finally {
                isLoading.value = false; // End loading
            }
        };

        // Function to fetch InfraestructuraRecurso data based on the year
        const fetchListaComunas = async () => {
            isLoading.value = true; // Start loading
            try {
                const response = await axios.get(API_BASE_URL + "comunas-OTI/", {
                    headers: {
                        'Authorization': "Bearer " + userToken.value
                    }
                });
                listaComunas.value = response.data;
                console.log(listaComunas.value);
            } catch (error) {
                console.error("Error fetching InfraestructuraRecurso data:", error);
            } finally {
                isLoading.value = false; // End loading
            }
        };
        const goBack = () => {
            router.go(-1); // Navigate to the previous route using Vue Router
        };

        return {
            isLoading,
            globalLoading,
            anio_proceso,
            infraestructuraRecursoData,
            fetchInfraestructuraRecursoData,
            fetchListaComunas,
            listaComunas,
            submitted,
            formData,
            formFormat1,
            formFormat2,
            formFormat3,
            comuna_list,
            vigencia_list,
            getOptions,
            submitForm,
            validateForm,
            formIsLoading,
            submitted,
            PostInfraestructuraRecursoData,
            router,
            goBack,
            userToken

        }
    },
    methods: {
        handleInput(event, fieldName) {
            let value = event.target.value;

            let sanitizedValue = value.replace(/^( |[^a-zA-Z0-9 ])|[^a-zA-Z0-9 ]/g, '') // Remove invalid characters and prevent leading space
                .replace(/\s+/g, ' '); // Replace multiple spaces with a single space

            // Update the formData with the sanitized value
            this.formData[fieldName] = sanitizedValue;
        }
    },
}
</script>