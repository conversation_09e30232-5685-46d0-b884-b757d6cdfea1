import { defineS<PERSON> } from "pinia";
import { encryptData, decryptData } from "@/utils/crypto";
import { useWebSocket } from "../websocket/useWebSocket";
import { useToast } from "primevue/usetoast";

let msalInstance;

export const useAuthStore = defineStore("auth", {
  state: () => ({
    name: encryptData(null),
    email: encryptData(null),
    isAuthenticated: encryptData(false),
    permissions: encryptData({ rol: null, recurso: null, accion: null }),
    permissionsList: encryptData(null),
    idToken: encryptData(null),
    tokenExpiresOn: encryptData(null),
    isLoading: encryptData(false),
    refreshTimeout: null,
    notificationNumber: encryptData(0),
    tokenWorker: null,
  }),
  actions: {
    // Method to save worker state to localStorage
    saveWorkerState() {
      const state = {
        tokenExpiresOn: this.tokenExpiresOn,
      };
      localStorage.setItem("workerState", JSON.stringify(state));
    },

    // Method to restore worker state from localStorage
    restoreWorkerState() {
      const savedState = localStorage.getItem("workerState");
      if (savedState) {
        return JSON.parse(savedState);
      }
      return null;
    },

    // Start the token worker, optionally with saved state
    async startTokenWorker() {
      if (window.Worker) {
        console.log("Web Worker is supported");

        // Detect if this is a page reload
        const isReload =
          performance?.navigation?.type === 1 ||
          sessionStorage.getItem("reloaded") === "true";

        // Retrieve saved state if available
        const savedState = isReload ? this.restoreWorkerState() : null;

        // Prevent starting multiple workers
        if (this.workerRunning) {
          console.warn("Worker is already running, skipping creation.");
          return;
        }

        this.tokenWorker = new Worker("/tokenWorker.js");
        this.workerRunning = true; // Mark worker as running

        this.tokenWorker.onmessage = async (event) => {
          console.log("Worker: Message received.", event.data);

          if (event.data === "check_token") {
            const expirationTime = new Date(
              decryptData(this.tokenExpiresOn) * 1000
            ).getTime();
            const currentTime = Date.now();
            let timeLeft = expirationTime - currentTime;
            console.log("timeLeft", timeLeft);
            console.log("timeLeft", 15 * 60 * 1000);
            console.log(
              "Worker trying token refresh",
              new Date(currentTime).toLocaleString()
            );

            if (timeLeft <= 15 * 60 * 1000) {
              // 15 minutes
              console.log(
                "Worker: Token is close to expiration, refreshing..."
              );
              try {
                await this.stopTokenWorker(); // Stop the worker before refreshing the token
                this.workerRunning = false; // Mark worker as not running
                const newToken = await silentTokenRefresh(this.email);
                this.setToken(
                  newToken.idToken,
                  newToken.account.idTokenClaims.exp
                );
                console.log("Worker: Token refreshed successfully!");
                await this.startTokenWorker(); // Start the worker again after successful token refresh
              } catch (error) {
                console.error("Worker: Token refresh failed", error);
              }
            }
          }
        };

        console.log("Token refresh worker started!");

        // If there was a saved state and no worker was running, pass it to the worker
        if (savedState && !this.workerRunning) {
          this.tokenWorker.postMessage(savedState);
        }

        // Start the worker task
        this.tokenWorker.postMessage("start");

        // Save worker state periodically
        setInterval(() => this.saveWorkerState(), 5000); // Save every 5 seconds

        // Mark session as reloaded
        sessionStorage.setItem("reloaded", "true");
      } else {
        console.warn("Web Workers are not supported in this browser.");
      }
    },
    async stopTokenWorker() {
      if (this.tokenWorker) {
        this.tokenWorker.terminate();
        this.tokenWorker = null;
        console.log("Token refresh worker stopped!");
      }
    },

    setMsalInstance(instance) {
      msalInstance = instance;
    },

    getMsalInstance() {
      return msalInstance;
    },

    updateLoadingState(loadingState) {
      this.isLoading = encryptData(loadingState);
    },

    setAccount(account, token, tokenExpiresOn, accesos, auth) {
      this.name = encryptData(account.name) || null;
      this.email = encryptData(account.username) || null;
      this.isAuthenticated = encryptData(auth);
      this.permissions = encryptData(accesos[0]) || null;
      this.permissionsList = encryptData(accesos) || null;
      this.idToken = encryptData(token);
      this.tokenExpiresOn = encryptData(tokenExpiresOn);
    },

    setToken(token, tokenExpiresOn) {
      this.idToken = encryptData(token);
      this.tokenExpiresOn = encryptData(tokenExpiresOn);
    },

    clearTimeout() {
      this.refreshTimeout = null;
    },

    async validateTokenWithBackend(token) {
      try {
        this.isLoading = encryptData(true);
        const response = await fetch(
          import.meta.env.VITE_BACKEND_BASE_URL + "validateLogin",
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.ok) {
          const data = await response.json();
          this.isLoading = encryptData(false);
          return data;
        }
      } catch (error) {
        console.error("Error validating token with backend", error);
        this.clearStorage();
        this.isLoading = encryptData(false);
        return false;
      } finally {
        this.isLoading = encryptData(false);
      }
    },

    async login() {
      this.isLoading = encryptData(true);
      console.log("start logging");
      if (!msalInstance) {
        console.error("MSAL instance is not set.");
        return;
      }
      try {
        const response = await msalInstance.loginRedirect({
          scopes: ["User.Read"],
        });
        this.isLoading = encryptData(false);
      } catch (error) {
        console.error("Login error:", error);
      }
    },

    async logout(router, toast) {
      if (!msalInstance) {
        console.error("MSAL instance is not set.");
        return;
      }
      if (!router) {
        console.error("Router instance is not provided.");
        return;
      }

      try {
        this.isLoading = encryptData(true);
        const { closeWebSocket } = useWebSocket(toast);
        await closeWebSocket();
        await this.stopTokenWorker();
        this.clearStorage();
        await new Promise((resolve) => setTimeout(resolve, 2000));
        this.isLoading = encryptData(false);
        router.push({ name: "login" });
      } catch (error) {
        console.error("Logout error:", error);
        this.isLoading = encryptData(false);
      } finally {
        this.isLoading = encryptData(false);
      }
    },

    clearStorage() {
      return new Promise(async (resolve) => {
        await clearMsalTokens();
        await this.clearAccount();
        resolve();
      });
    },

    clearAccount() {
      this.name = encryptData(null);
      this.email = encryptData(null);
      this.isAuthenticated = encryptData(false);
      this.permissions = encryptData([]);
      this.idToken = encryptData(null);
      this.tokenExpiresOn = encryptData(null);
      this.refreshTimeout = null;
    },
  },
  getters: {
    msalInstance: () => msalInstance,
    userName: (state) => state.name,
    userEmail: (state) => state.email,
    userIsAuthenticated: (state) => state.isAuthenticated,
    getPermissons: (state) => state.permissions,
    hasPermission: (state) => (role, resource, action) => {
      if (Array.isArray(state.permissions)) {
        return state.permissions.some(
          (permission) =>
            permission.rol === role &&
            permission.recurso === resource &&
            permission.accion === action
        );
      } else {
        console.warn("Permissions is not an array:", state.permissions);
        return false;
      }
    },
  },
  persist: {
    key: "auth",
    storage: localStorage,
    paths: [
      "name",
      "email",
      "isAuthenticated",
      "permissions",
      "permissionsList",
      "idToken",
      "loading",
      "tokenExpiresOn",
      "refreshTimeout",
      "notificationNumber",
    ],
  },
});

function clearMsalTokens() {
  const msalKeyPrefix1 = "login";
  const msalKeyPrefix2 = "msal";
  const keysToRemove = [];

  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (key.includes(msalKeyPrefix1) || key.includes(msalKeyPrefix2))) {
      keysToRemove.push(key);
    }
  }

  keysToRemove.forEach((key) => {
    localStorage.removeItem(key);
  });
}

async function silentTokenRefresh(userEmail) {
  const authStore = useAuthStore();
  const currentAccount = msalInstance.getAccountByUsername(
    decryptData(userEmail)
  );
  if (!currentAccount) {
    throw new Error("No user account available.");
  }

  const tokenRequest = {
    account: currentAccount,
    scopes: ["openid", "profile", "User.Read", "email"],
    forceRefresh: true,
  };

  try {
    const tokenResponse = await msalInstance.acquireTokenSilent(tokenRequest);
    if (tokenResponse) {
      await authStore.setToken(
        tokenResponse.idToken,
        tokenResponse.account.idTokenClaims.exp
      );
      return tokenResponse;
    } else {
      throw new Error("Silent token request failed.");
    }
  } catch (error) {
    if (error instanceof InteractionRequiredAuthError) {
      try {
        const tokenResponse = await msalInstance.acquireTokenPopup(
          tokenRequest
        );
        return tokenResponse;
      } catch (innerError) {
        if (innerError instanceof InteractionRequiredAuthError) {
          await authStore.clearStorage();
          await authStore.clearAccount();
        }
        throw innerError;
      }
    } else {
      throw error;
    }
  }
}

export { msalInstance };
