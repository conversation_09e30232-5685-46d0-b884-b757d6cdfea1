<template>
    <div class="surface-ground py-7 md:px-5 lg:px-6">
        <div class="card">
            <TabView v-model:activeIndex="activeIndex">
                <TabPanel header="Crear nuevo usuario">
                    <AdmCreacionUsuario v-if="activeIndex === 0" />
                </TabPanel>
                <TabPanel header="" :disabled="true">
                    <p class="m-0">

                    </p>
                </TabPanel>
                <TabPanel header="" :disabled="true">
                    <p class="m-0">

                    </p>
                </TabPanel>
                <TabPanel header="Permisos usuarios">
                    <admListaUsuario v-if="activeIndex === 3" />
                </TabPanel>
            </TabView>
        </div>
    </div>
    <Toast position="bottom-left" group="bl" />
</template>

<script>
import { useAuthStore } from '../../store/auth';
import { ref, computed } from 'vue';
import AdmCreacionUsuario from './panel_control/adm-creacion-usuario.vue';
import admListaUsuario from './panel_control/adm-lista-usuario.vue';

export default {
    components: {
        AdmCreacionUsuario,
        admListaUsuario
    },

    name: "pg-principal-adm-usuarios",

    setup() {
        const authStore = useAuthStore();
        // Access getters
        // Computed properties for userName and userEmail
        const userName = computed(() => authStore.userName);
        const userEmail = computed(() => authStore.userEmail);
        const permissions = computed(() => authStore.permissions);
        const isUserAuthenticated = computed(() => authStore.userIsAuthenticated);
        const theme = ref("dark");
        const activeIndex = ref(0); // Track the currently active tab

        // Return data object
        return {
            theme,
            authStore,
            userName,
            userEmail,
            permissions,
            isUserAuthenticated,
            activeIndex,
        };
    }
}
</script>

<style></style>