<script setup lang="ts">
import { computed, ref, toRef, watch } from 'vue'
import Panel from 'primevue/panel'
import { useToast } from "primevue/usetoast";
import { Form as VeeForm, useForm } from 'vee-validate'
import * as yup from 'yup'
import mV from '../../utils/validationMessages'
import type { OAcademicaEtapa3PosgradoPostitulo } from '../interfaces/OAcademica';
import { acreditacionOptions, formatoValorOptions} from '../helpers/selectValuesOacademica'
import Dropdown from 'primevue/dropdown'
import useOAcademica from '../composables/useOAcademica'

interface Props {
  oAcademicaEtapa3PosgradoPostitulo: OAcademicaEtapa3PosgradoPostitulo,
    showDialog: boolean
}

const toast = useToast();
const props = defineProps<Props>();
const emit = defineEmits(['closeDialog']);
const oAcademicaEtapa3PosgradoPostitulo = ref<OAcademicaEtapa3PosgradoPostitulo>({...props.oAcademicaEtapa3PosgradoPostitulo});


const { oAcademicaEtapa3PregradoMutation } = useOAcademica(props.oAcademicaEtapa3PosgradoPostitulo.oa_sies_id);

watch(oAcademicaEtapa3PregradoMutation.isSuccess, (value) => {
  if (value) {
    toast.add({ life: 3000, severity: 'success', summary: 'Datos actualizados', detail: 'Los datos han sido actualizados correctamente' });
    oAcademicaEtapa3PregradoMutation.reset();
  }

});

const schema = yup.object({
    acreditacion: yup.number().typeError(mV.number).required(mV.required).oneOf([1, 2], "Debe ser Sí(1) o No(2)"), 
    malla_curricular: yup.string().oneOf([''], 'El campo debe estar vacío'),
    mail_difusion_carrera: yup.string().oneOf([''], 'El campo debe estar vacío'),
    formato_valor: yup.number().typeError(mV.number).required(mV.required).oneOf([1, 2], "Debe ser Pesos(1) o UF(2)"),
    valor_matricula_anual: yup.number().typeError(mV.number).required(mV.required),
    costo_titulacion: yup.number().typeError(mV.number).required(mV.required),
    valor_certificado_diploma: yup.number().typeError(mV.number).required(mV.required),
    arancel_anual: yup.number().typeError(mV.number).required(mV.required),
})

// Configuración del formulario
const { handleSubmit, resetForm, setValues, setFieldValue, errors } = useForm({
    validationSchema: schema,
    initialValues: oAcademicaEtapa3PosgradoPostitulo.value, // Usamos los valores iniciales desde las props
});

// Manejador del formulario
const onSubmit = handleSubmit((values) => {
    console.log('Valores validados:', values);// Emitimos los datos validados al padre
    oAcademicaEtapa3PregradoMutation.mutate(values);
    
});



</script>

<template>
    <div>
      <VeeForm @submit="onSubmit" class="mt-5">
        <div class="formgrid grid">
          <!-- acreditacion (Dropdown) -->
          <div class="field col-12 md:col-6 lg:col-3 mt-3">
            <FloatLabel>
              <Dropdown id="acreditacion"
                        v-model="oAcademicaEtapa3PosgradoPostitulo.acreditacion"
                        :options="acreditacionOptions"
                        optionLabel="name" optionValue="value"
                        :class="{ 'p-invalid': errors.acreditacion }"
                        @update:modelValue="setFieldValue('acreditacion', oAcademicaEtapa3PosgradoPostitulo.acreditacion)"
                        class="w-full" />
              <label for="acreditacion">Acreditación (Dropdown)</label>
              <small v-if="errors.acreditacion" class="p-error">{{ errors.acreditacion }}</small>
            </FloatLabel>
          </div>
  
          <!-- malla_curricular (InputText) -->
          <div class="field col-12 md:col-6 lg:col-3 mt-3">
            <FloatLabel>
              <InputText id="malla_curricular"
                         v-model="oAcademicaEtapa3PosgradoPostitulo.malla_curricular"
                         :class="{ 'p-invalid': errors.malla_curricular }"
                         @input="setFieldValue('malla_curricular', oAcademicaEtapa3PosgradoPostitulo.malla_curricular)"
                         class="w-full" />
              <label for="malla_curricular">Malla Curricular</label>
              <small v-if="errors.malla_curricular" class="p-error">{{ errors.malla_curricular }}</small>
            </FloatLabel>
          </div>
  
          <!-- mail_difusion_carrera (InputText) -->
          <div class="field col-12 md:col-6 lg:col-3 mt-3">
            <FloatLabel>
              <InputText id="mail_difusion_carrera"
                         v-model="oAcademicaEtapa3PosgradoPostitulo.mail_difusion_carrera"
                         :class="{ 'p-invalid': errors.mail_difusion_carrera }"
                         @input="setFieldValue('mail_difusion_carrera', oAcademicaEtapa3PosgradoPostitulo.mail_difusion_carrera)"
                         class="w-full" />
              <label for="mail_difusion_carrera">Mail Difusión Carrera</label>
              <small v-if="errors.mail_difusion_carrera" class="p-error">{{ errors.mail_difusion_carrera }}</small>
            </FloatLabel>
          </div>
  
          <!-- formato_valor (Dropdown) -->
          <div class="field col-12 md:col-6 lg:col-3 mt-3">
            <FloatLabel>
              <Dropdown id="formato_valor"
                        v-model="oAcademicaEtapa3PosgradoPostitulo.formato_valor"
                        :options="formatoValorOptions"
                        optionLabel="name" optionValue="value"
                        :class="{ 'p-invalid': errors.formato_valor }"
                        @update:modelValue="setFieldValue('formato_valor', oAcademicaEtapa3PosgradoPostitulo.formato_valor)"
                        class="w-full" />
              <label for="formato_valor">Formato Valor (Dropdown)</label>
              <small v-if="errors.formato_valor" class="p-error">{{ errors.formato_valor }}</small>
            </FloatLabel>
          </div>
  
          <!-- valor_matricula_anual (InputNumber) -->
          <div class="field col-12 md:col-6 lg:col-3 mt-3">
            <FloatLabel>
              <InputNumber id="valor_matricula_anual"
                           v-model="oAcademicaEtapa3PosgradoPostitulo.valor_matricula_anual"
                           :class="{ 'p-invalid': errors.valor_matricula_anual }"
                           @input="setFieldValue('valor_matricula_anual', oAcademicaEtapa3PosgradoPostitulo.valor_matricula_anual)"
                           class="w-full" />
              <label for="valor_matricula_anual">Valor Matrícula Anual</label>
              <small v-if="errors.valor_matricula_anual" class="p-error">{{ errors.valor_matricula_anual }}</small>
            </FloatLabel>
          </div>
  
          <!-- costo_titulacion (InputNumber) -->
          <div class="field col-12 md:col-6 lg:col-3 mt-3">
            <FloatLabel>
              <InputNumber id="costo_titulacion"
                           v-model="oAcademicaEtapa3PosgradoPostitulo.costo_titulacion"
                           :class="{ 'p-invalid': errors.costo_titulacion }"
                           @input="setFieldValue('costo_titulacion', oAcademicaEtapa3PosgradoPostitulo.costo_titulacion)"
                           class="w-full" />
              <label for="costo_titulacion">Costo Titulación</label>
              <small v-if="errors.costo_titulacion" class="p-error">{{ errors.costo_titulacion }}</small>
            </FloatLabel>
          </div>
  
          <!-- valor_certificado_diploma (InputNumber) -->
          <div class="field col-12 md:col-6 lg:col-3 mt-3">
            <FloatLabel>
              <InputNumber id="valor_certificado_diploma"
                           v-model="oAcademicaEtapa3PosgradoPostitulo.valor_certificado_diploma"
                           :class="{ 'p-invalid': errors.valor_certificado_diploma }"
                           @input="setFieldValue('valor_certificado_diploma', oAcademicaEtapa3PosgradoPostitulo.valor_certificado_diploma)"
                           class="w-full" />
              <label for="valor_certificado_diploma">Valor Certificado Diploma</label>
              <small v-if="errors.valor_certificado_diploma" class="p-error">{{ errors.valor_certificado_diploma }}</small>
            </FloatLabel>
          </div>
  
          <!-- arancel_anual (InputNumber) -->
          <div class="field col-12 md:col-6 lg:col-3 mt-3">
            <FloatLabel>
              <InputNumber id="arancel_anual"
                           v-model="oAcademicaEtapa3PosgradoPostitulo.arancel_anual"
                           :class="{ 'p-invalid': errors.arancel_anual }"
                           @input="setFieldValue('arancel_anual', oAcademicaEtapa3PosgradoPostitulo.arancel_anual)"
                           class="w-full" />
              <label for="arancel_anual">Arancel Anual</label>
              <small v-if="errors.arancel_anual" class="p-error">{{ errors.arancel_anual }}</small>
            </FloatLabel>
          </div>
        </div>
  
        <div class="flex justify-content-end gap-2">
          <ProgressSpinner v-if="oAcademicaEtapa3PregradoMutation.isPending.value" style="width:38px; height:38px;" strokeWidth="7" aria-label="Loading" />
          <Button v-if="!oAcademicaEtapa3PregradoMutation.isPending.value" type="button" label="Cancelar" severity="secondary" @click="emit('closeDialog')" />
          <Button v-if="!oAcademicaEtapa3PregradoMutation.isPending.value" type="submit" label="Guardar" />
        </div>
      </VeeForm>
    </div>
  </template>
  



<style scoped></style>