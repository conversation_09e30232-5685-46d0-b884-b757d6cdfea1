<template>

    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading">
            <form @submit.prevent="submitForm">
                <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                    <div class='surface-card shadow-3 p-3 border-round'>
                        <h2>Crear nueva notificación:</h2>
                        <div v-if="!isLoading">
                            <div class="formgrid grid">
                                <div class="field col-6" v-for="(field, index) in formFormat1" :key="field.id">
                                    <label :for="field.name">{{ field.displayName }}</label>
                                    <InputText :class="{
                                        'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                                    }" v-if="field.contenedor === 'InputText'" :id=field.name
                                        :placeholder=field.placeholder :type=field.type style="height:2.6rem"
                                        v-model="formData[field.name]" :modelValue="field.modelValue">
                                    </InputText>
                                    <Dropdown :class="{
                                        'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                                    }" v-if="field.contenedor === 'dropdown'" v-model="formData[field.name]" filter
                                        :placeholder="field.placeholder" style="height:2.6rem;margin-bottom: 1rem"
                                        :options="getOptions(field.options)" :optionLabel="field.optionLabel"
                                        class="w-full">
                                    </Dropdown>
                                </div>
                            </div>
                        </div>
                        <div class="center">
                            <Button type="submit" label="Confirmar"></Button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div v-else class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
                <Loading />
            </div>
        </div>
        <Toast position="bottom-left" group="bl" />
    </div>


</template>

<script>
import { useAuthStore } from '../../store/auth';
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import { useToast } from "primevue/usetoast";
import axios from "axios";

export default {

    name: "Avisos",

    setup() {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const formData = ref(
            {
                titulo: null,
                mensaje: null,
                tipo: null,
                estado: true,
                ocultar: false,
                eliminar: false,
                destinatario: null,
                creador: null,
            });
        const formFormat1 = ref([
            { id: 1, placeholder: 'Seleccionar Titulo', name: 'titulo', displayName: 'Titulo de la notificacion', type: 'text', contenedor: 'dropdown', options: 'lista_titulo', optionLabel: 'options' },
            { id: 2, placeholder: 'Ingresar Mensaje', name: 'mensaje', displayName: 'Mensaje', type: 'text', contenedor: 'InputText' },
            { id: 3, placeholder: 'Seleccionar tipo de notificación', name: 'tipo', displayName: 'Tipo', type: 'text', contenedor: 'dropdown', options: 'lista_tipo', optionLabel: 'options' },
            { id: 4, placeholder: 'Seleccionar destinatario', name: 'destinatario', displayName: 'Destinatario', type: 'text', contenedor: 'dropdown', options: 'usersDB', optionLabel: 'email' },
        ]);
        const authStore = useAuthStore();
        const toast = useToast();

        // Obtain the stored user data from pinia and the global loading state
        const userName = computed(() => decryptData(authStore.userName));
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const permissions = computed(() => decryptData(authStore.permissions));
        const isUserAuthenticated = computed(() => decryptData(authStore.userIsAuthenticated));
        const globalLoading = computed(() => decryptData(authStore.isLoading));
        const submitted = ref(false);
        const usersDB = ref(null);

        // Theme for changing the actual theme from dark to light or viceversa
        const theme = ref("dark");

        // Local "Loading" for the current view
        const isLoading = ref(false);
        const formIsLoading = ref(false);

        // Get options for dropdown fields
        const getOptions = (optionKey) => {
            if (optionKey === 'lista_titulo') {
                return lista_titulo.value;
            } else if (optionKey === 'lista_tipo') {
                return lista_tipo.value;
            }
            else if (optionKey === 'usersDB') {
                return usersDB.value;
            }
            return [];
        };

        const lista_titulo = ref([
            { options: 'Aviso', value: 1 },
            { options: 'Consulta', value: 2 },
        ]);

        const lista_tipo = ref([
            { options: 'tipo 1', value: 1 },
            { options: 'tipo 2', value: 2 },
        ]);


        onMounted(() => {
            getUserData();
        });

        const getUserData = async () => {
            try {
                isLoading.value = true;
                const response = await axios.get(API_BASE_URL + "usuario/");
                usersDB.value = response.data;
            } catch (error) {
                console.error("Error: ", error);
            } finally {
                isLoading.value = false;
            }
        };
        const submitForm = async () => {
            formIsLoading.value = true;
            formData.value.creador = userEmail.value;

            // Check if any field in formData is null or empty
            const hasEmptyValues = Object.values(formData.value).some(
                (value) => value === null || value === '' || value === undefined
            );
            if (hasEmptyValues && !submitted.value) {
                submitted.value = true;
                console.log('Form has empty values');
                showToast("error", "Campos Vacios", "Se detectaron campos vacios", "4000");
            } else {
                // The form is complete
                console.log("NO EMPTY VALUES detected");
                submitted.value = true;
                isLoading.value = true;
                formData.value.titulo = formData.value.titulo.options;
                formData.value.tipo = formData.value.tipo.options;
                formData.value.destinatario = formData.value.destinatario.email;
                console.log(formData.value);

                try {
                    isLoading.value = true;
                    const response = await axios.post(API_BASE_URL + "notificacion", formData.value);

                } catch (error) {
                    console.error("Error: ", error);
                } finally {
                    isLoading.value = false;
                }
            }
        }

        /**
         * Toast notification used to display success or error of the user inputs
         * @param severity type of toast notification
         * @param summary Title of the notification (success, info, warn, error)
         * @param detail description of the notification
         * @param life duration of the notification
         */
        const showToast = (severity, summary, detail, life) => {
            toast.add({ severity: severity, summary: summary, group: 'bl', detail: detail, life: life });
        };

        // Return data object
        return {
            theme,
            authStore,
            userName,
            userEmail,
            permissions,
            isUserAuthenticated,
            isLoading,
            globalLoading,
            formData,
            formFormat1,
            getOptions, // Add getOptions here
            lista_titulo,
            lista_tipo,
            usersDB,
            submitted,
            submitForm,
            formIsLoading,
            showToast

        };
    }
}
</script>



<style></style>