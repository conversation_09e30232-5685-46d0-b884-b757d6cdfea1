<template>
    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading">
            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <form @submit.prevent="submitForm">
                    <div class='surface-card shadow-3 p-3 border-round'>
                        <div
                            style="display: flex;justify-content: space-between;align-items: center;padding-left: 0.1rem;">
                            <h2>Infraestructura y Recursos Institucionales</h2>
                            <Button
                                v-if="hasAccess('Encargado', 'OTI') || hasAccess('Usuario General', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU')"
                                @click="dialogVisibleCrear = true">Nuevo</Button>
                        </div>

                        <DataTable :value="infraestructuraRecursoData" stripedRows showGridlines paginator :rows="10"
                            tableStyle="min-width: 50rem">
                            <Column field="anio_proceso" header="Proceso" class="center-header"></Column>
                            <Column field="creador_email" header="Autor" class="center-header"></Column>
                            <Column field="childrenLength" header="N° Infraestructura" class="center-header"></Column>
                            <Column field="is_finalized" header="Estado" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.is_finalized == null || slotProps.data.is_finalized == false"
                                        v-tooltip.bottom="pending_text">
                                        <Button disabled icon="pi pi-hourglass"
                                            style="justify-content: center;color: orangered;" class="p-button-text" />
                                    </div>
                                    <div v-else v-tooltip.bottom="finalized_text">
                                        <Button disabled icon="pi pi-verified"
                                            style="justify-content: center;color: green"
                                            class="p-button-text green-icon" />
                                    </div>
                                </template>
                            </Column>

                            <Column field="ver" header="Ver" class="center-header">

                                <template #body="slotProps">
                                    <Button icon="pi pi-eye" style="justify-content: center;" class="p-button-text"
                                        @click="goToDetails(slotProps.data.anio_proceso)" />
                                </template>
                            </Column>
                            <Column
                                v-if="hasAccess('Encargado', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU')"
                                field="eliminar" header="Eliminar" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.is_finalized != true">
                                        <Button icon="pi pi-trash" class="p-button-text"
                                            @click="dialogVisibleEliminar = true, selectedYear = slotProps.data.anio_proceso" />
                                    </div>
                                </template>
                            </Column>
                            <Column
                                v-if="hasAccess('Encargado', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU')"
                                field="Validar" header="Validar" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.is_finalized != true">
                                        <Button icon="pi pi-check" style="justify-content: center;"
                                            class="p-button-text" @click="dialogConfirmarInfraestructura = true,
                                                selectedYear = slotProps.data.anio_proceso" />
                                    </div>
                                </template>
                            </Column>
                            <Column
                                v-if="hasAccess('Encargado', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU')"
                                field="Exportar" header="Exportar" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.is_finalized == true">
                                        <Button icon="pi pi-download" style="" class="p-button-text"
                                            @click="obtenerInmuebleAnioSeleccionado(slotProps.data.anio_proceso)" />
                                    </div>
                                </template>
                            </Column>
                        </DataTable>

                    </div>
                </form>
            </div>

        </div>
        <div v-else class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
                <Loading />
            </div>
        </div>
        <Toast position="bottom-left" group="bl" />
    </div>
    <Dialog v-model:visible="dialogVisibleCrear" modal header="Infraestructura y Recursos" :style="{ width: '30rem' }"
        v-on:hide="resetForm()">
        <div v-if="dialogIsLoading && !infraestructuraRecursoIsCorrect">
            <Loading />
        </div>

        <!-- Form to create a new report -->
        <div v-if="!dialogIsLoading && !infraestructuraRecursoIsCorrect">
            <span class="p-text-secondary block mb-5">Crear nuevo reporte de infraestructura</span>

            <!-- Year input with validation -->
            <div class="flex align-items-center gap-3 mb-3">
                <div>
                    <label for="anio" class="font-semibold w-6rem">Año Proceso </label>
                    <i class="pi pi-info-circle" style="justify-content: center" v-tooltip.bottom="tooltipText" />
                </div>

                <InputNumber v-model="inputYear" inputId="anio" :useGrouping="false" />
            </div>

            <!-- Validation error message -->
            <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}</div>

            <!-- Buttons -->
            <div class="flex justify-content-end gap-2">
                <Button type="button" label="Cancelar" severity="secondary"
                    @click="dialogVisibleCrear = false"></Button>
                <Button type="button" label="Crear" @click="crearInfraestructuraRecurso"></Button>
            </div>
        </div>

        <!-- Success message -->
        <div v-if="!dialogIsLoading && infraestructuraRecursoIsCorrect"
            style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
            <i class="pi pi-check" style="font-size: 5rem; color: blue;"></i>
            <span class="p-text-secondary block mb-5">¡Creación exitosa!</span>
        </div>
    </Dialog>
    <Dialog v-model:visible="dialogVisibleEliminar" modal header="Infraestructura y Recursos"
        :style="{ width: '25rem' }" v-on:hide="resetForm()">
        <div v-if="dialogIsLoading && !infraestructuraRecursoIsCorrect">
            <Loading />
        </div>

        <div v-if="!dialogIsLoading && !infraestructuraRecursoIsCorrect">
            <div>
                <!-- Validation error message -->
                <p>Esto eliminará permanentemente este registro ¿Está seguro?</p>
            </div>
            <br />
            <div class="flex justify-content-center gap-2">
                <InputSwitch style="scale: 1.5;" v-model="deletePreConfirmation"></InputSwitch>
            </div>
            <br />
            <!-- Validation error message -->
            <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}<br /></div>


            <!-- Buttons -->
            <div class="flex justify-content-end gap-2">
                <Button type="button" label="Cancelar" severity="secondary"
                    @click="dialogVisibleEliminar = false"></Button>
                <Button v-if="deletePreConfirmation" type="button" label="Eliminar"
                    @click="eliminarInfraestructuraRecurso"></Button>
            </div>
        </div>

        <!-- Success message -->
        <div v-if="!dialogIsLoading && infraestructuraRecursoIsCorrect"
            style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
            <i class="pi pi-trash" style="font-size: 5rem; color: blue;"></i>
            <br />
            <span class="p-text-secondary block mb-5">¡Eliminado correctamente!</span>
        </div>

    </Dialog>

    <Dialog v-model:visible="dialogConfirmarInfraestructura" modal header="Validar Infraestructura y Recursos"
        :style="{ width: '25rem' }" v-on:hide="resetForm()">
        <div v-if="dialogIsLoading && !infraestructuraRecursoIsCorrect">
            <Loading />
        </div>

        <div v-if="!dialogIsLoading && !infraestructuraRecursoIsCorrect">
            <div>
                <p class="block mb-3">Al validar el registro este no se podrá editar ni eliminar ¿Está
                    seguro?
                </p>
                <p class="p-error block mb-3">Esta acción es IRREVERSIBLE.
                </p>
            </div>
            <br />
            <div class="flex justify-content-center gap-2">
                <InputSwitch style="scale: 1.5;" v-model="validatePreConfirmation"></InputSwitch>
            </div>
            <br />
            <div>
                <!-- Validation error message -->
                <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}</div>

                <br />
            </div>
            <!-- Buttons -->
            <div class="flex justify-content-end gap-2">
                <Button type="button" label="Cancelar" severity="secondary"
                    @click="dialogConfirmarInfraestructura = false"></Button>
                <Button v-if="validatePreConfirmation" type="button" label="Confirmar"
                    @click="validarInfraestructuraRecurso(selectedYear)"></Button>
            </div>
        </div>

        <!-- Success message -->
        <div v-if="!dialogIsLoading && infraestructuraRecursoIsCorrect"
            style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
            <i class="pi pi-check" style="font-size: 5rem; color: blue;"></i>
            <br />
            <span class="p-text-secondary block mb-5">¡Registro validado correctamente!</span>
        </div>

    </Dialog>
</template>

<script>
import { useAuthStore } from '../../store/auth';
import { ref, computed, onMounted } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import axios from 'axios';
import Papa from 'papaparse';
import InputText from 'primevue/inputtext';
import { format } from 'date-fns'; // format dates to yyyy-mm-dd
import { useToast } from 'primevue/usetoast';
import { useRouter, useRoute } from 'vue-router';

export default {
    setup() {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const router = useRouter();
        const route = useRoute();
        const authStore = useAuthStore();
        const toast = useToast();

        // Obtain the stored user data from pinia and the global loading state
        const userName = computed(() => decryptData(authStore.userName));
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const permissionsList = computed(() => decryptData(authStore.permissionsList));
        const isUserAuthenticated = computed(() => decryptData(authStore.userIsAuthenticated));
        const globalLoading = computed(() => decryptData(authStore.isLoading));
        const userToken = computed(() => decryptData(authStore.idToken));
        const selectedYear = ref(null);
        const inmuebleAnioSeleccionado = ref(null);
        const finalized_text = ref("Finalizado");
        const pending_text = ref("Pendiente");
        const tooltipText = ref("El proceso a reportar corresponde a la infraestructura utilizada por la institución el año anterior. Ejemplo: Al ingresar el año 2021 en este campo, se debe reportar la infraestructura que se encontraba utilizando al 31 de diciembre del 2020.");
        // Local "Loading" for the current view
        const isLoading = ref(false);
        const dialogVisibleCrear = ref(false);
        const dialogConfirmarInfraestructura = ref(false);
        const dialogVisibleEliminar = ref(false);
        const deletePreConfirmation = ref(false);
        const validatePreConfirmation = ref(false);
        const inputYear = ref(null);
        const errorMessage = ref(''); // To store validation errors
        const dialogIsLoading = ref(false);
        const infraestructuraRecursoData = ref(null);
        const infraestructuraRecursoIsCorrect = ref(false);
        const canAccess = ref(false);

        // Theme for changing the actual theme from dark to light or viceversa
        const theme = ref("dark");

        // Define the headers for the csv export
        const csvHeaders = [
            "tipo_infraestructura",
            "nombre_identificacion",
            "comuna",
            "direccion_inmueble",
            "situacion_tenencia",
            "anio_inicio_uso_inmueble",
            "uso_exclusivo",
            "porcentaje_uso",
            "nombre_institucion_comparte",
            "fecha_inicio_tenencia",
            "fecha_termino",
            "descripcion_otra_tenencia",
            "funcion_docencia",
            "funcion_investigacion",
            "funcion_extension",
            "funcion_adm_oficinas",
            "funcion_otras",
            "desc_otras_funciones",
            "total_m2_terreno",
            "total_m2_edificados",
            "total_salas_clases",
            "capacidad_salas_clases",
            "total_m2_salas_clases",
            "total_auditorios",
            "capacidad_auditorios",
            "total_m2_auditorios",
            "total_laboratorios",
            "total_m2_laboratorios",
            "total_talleres",
            "total_m2_talleres",
            "total_pc_nb_disponible",
            "total_m2_casinos_cafeterias",
            "total_m2_areas_verdes",
            "ur_desc_actividades",
            "ur_total_m2_terreno",
            "ur_total_m2_construidos",
            "ur_situacion_tenencia",
            "ur_desc_tenencia_otra",
            "total_m2_biblioteca",
            "total_m2_salas_lectura",
            "total_profesionales_biblioteca",
            "horas_personal_biblioteca",
            "total_titulos_disponibles",
            "total_volumenes_disponibles",
            "total_suscripciones_revistas",
            "total_titulos_libros_digitales",
            "total_suscripciones_digitales",
            "total_base_datos",
            "total_hectareas_predio",
            "sistema_gestion_aprendizajes",
            "sistema_video_conferencia",
            "sistema_aplicacion_evaluacion",
            "descripcion_plataforma_virtual",
            "vigencia",
        ];

        const getInfraestructuraRecursoData = async () => {
            try {
                isLoading.value = true;

                const response = await axios.get(API_BASE_URL + "infraestructuraRecurso", {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });

                if (response.status == 200) {
                    infraestructuraRecursoData.value = response.data;

                    infraestructuraRecursoData.value.forEach((item, index) => {
                        const countChildren = ref(0);
                        countChildren.value += item.InmueblePermanentes.length
                        countChildren.value += item.InmuebleRestringidos.length
                        countChildren.value += item.LibrosBasesDigitales.length
                        countChildren.value += item.PlataformaVirtuals.length
                        countChildren.value += item.Predios.length
                        countChildren.value += item.Bibliotecas.length
                        item.childrenLength = countChildren.value; // add the value of the total of "child" a infraestructuraRecurso has
                    });
                }
            } catch (error) {
                console.error("Error: ", error);
            } finally {
                isLoading.value = false;
            }
        };

        const postInfraestructuraRecurso = async () => {
            // Define the request body
            const requestBody = {
                anio_proceso: inputYear.value,
                creador_email: userEmail.value,
                userToken: "Bearer " + userToken.value
            };
            try {
                dialogIsLoading.value = true;
                const response = await axios.post(API_BASE_URL + "infraestructuraRecurso", requestBody, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                return "ok";
            } catch (error) {
                console.error("Error: ", error);
            } finally {
                dialogIsLoading.value = false;
            }
        };

        onMounted(async () => {
            isLoading.value = true;
            await getInfraestructuraRecursoData();

            // Access query parameters using the `route` object
            const successMessage = route.query.success;
            if (successMessage) {
                // Use `$toast` if it is globally injected, or import it if necessary
                this.$toast.add({ severity: 'success', summary: 'Correcto', group: 'bl', detail: successMessage, life: 5000 });
            }
            isLoading.value = false;
        });

        const hasAccess = (allowedRol, requiredRecurso) => {
            const access = permissionsList.value.some(user => user.rol === allowedRol && user.recurso === requiredRecurso);
            if (access) {
                return true;
            } else {
                return false;
            }

        };

        const crearInfraestructuraRecurso = async () => {
            // Clear previous error messages
            errorMessage.value = '';

            // Validate the year
            if (inputYear.value === null || inputYear.value < 1980 || inputYear.value > 2100) {
                errorMessage.value = 'El valor ingresado no es valido';
                return; // Stop the save process if validation fails
            } else {
                dialogIsLoading.value = true;
                await getInfraestructuraRecursoData();

                // Check if `anio_proceso` matches `inputYear.value` in any item of `infraestructuraRecursoData.value`
                const isDuplicate = infraestructuraRecursoData.value.some(item => item.anio_proceso === inputYear.value);

                if (isDuplicate) {
                    // If a match is found, throw the error
                    errorMessage.value = 'El valor ingresado ya existe';
                    dialogIsLoading.value = false; // Stop loading since an error occurred
                    return; // Prevent further execution
                } else {
                    const response = await postInfraestructuraRecurso();
                    dialogIsLoading.value = false;
                    if (response == "ok") {
                        infraestructuraRecursoIsCorrect.value = true
                        // If no match is found, proceed with saving the report
                        console.log('Saving report for year:', inputYear.value);
                        await getInfraestructuraRecursoData();
                    }
                }
            }
        }

        // Function to reset the form for a new creation
        const resetForm = () => {
            inputYear.value = null; // Reset the year input
            errorMessage.value = ''; // Clear any error messages
            infraestructuraRecursoIsCorrect.value = false; // Reset success state
            dialogVisibleCrear.value = false; // Close the dialog
            deletePreConfirmation.value = false;
            dialogVisibleEliminar.value = false;
            validatePreConfirmation.value = false;
            dialogConfirmarInfraestructura.value = false;
        }


        const eliminarInfraestructuraRecurso = async () => {
            errorMessage.value = '';

            const eliminarResponse = await eliminarInfraestructura();

            if (eliminarResponse == "ok") {
                infraestructuraRecursoIsCorrect.value = true;
                console.log('Deleting report for year:', inputYear.value);
                await getInfraestructuraRecursoData();

            } else if (eliminarResponse == "conflicto") {
                errorMessage.value = 'El elemento no debe tener infraestructuras o recursos asociados a él';
            } else if (eliminarResponse == "error") {
                errorMessage.value = 'Se detectó un error, inténtelo más tarde';
            }
        }

        const eliminarInfraestructura = async () => {
            errorMessage.value = '';

            try {
                dialogIsLoading.value = true;
                const body = ref({});
                body.value.userEmail = userEmail.value;
                body.value.userToken = "Bearer " + userToken.value;
                const response = await axios.delete(API_BASE_URL + `infraestructuraRecurso/${selectedYear.value}`, {
                    data: body.value,
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                return "ok";
            } catch (error) {
                console.error("Error: ", error);
                if (error.status == 409) {
                    return "conflicto";
                } else {
                    if (error.status === 403) {
                        toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El registro no puede ser modificado', life: 5000 });
                    }
                    return "error";
                }
            } finally {
                dialogIsLoading.value = false;
            }

        }

        const goToDetails = (data) => {
            const encrypted_year = encryptData(data);
            router.push({ name: 'Pagina-detalle-oti', params: { anio_proceso: encrypted_year, infraestructuraRecurso: "infraestructuraRecurso" } });
        };

        const validarInfraestructuraRecurso = async (anioProceso) => {
            try {
                dialogIsLoading.value = true;

                const response = await axios.get(API_BASE_URL + `infraestructuraRecurso/${anioProceso}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                inmuebleAnioSeleccionado.value = await response.data;
                if (inmuebleAnioSeleccionado.value.is_finalized == true) {
                    toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El registro no puede ser modificado', life: 5000 });
                } else {
                    if (
                        inmuebleAnioSeleccionado.value.Bibliotecas.length > 0 ||
                        inmuebleAnioSeleccionado.value.InmueblePermanentes.length > 0 ||
                        inmuebleAnioSeleccionado.value.InmuebleRestringidos.length > 0 ||
                        inmuebleAnioSeleccionado.value.LibrosBasesDigitales.length > 0 ||
                        inmuebleAnioSeleccionado.value.PlataformaVirtuals.length > 0 ||
                        inmuebleAnioSeleccionado.value.Predios.length > 0
                    ) {
                        try {
                            const body = ref({});
                            body.value.anio_proceso = inmuebleAnioSeleccionado.value.anio_proceso;
                            body.value.is_finalized = true;
                            body.value.validated_by = userEmail.value
                            body.value.userToken = "Bearer " + userToken.value
                            const response2 = await axios.patch(API_BASE_URL + `infraestructuraRecurso/${anioProceso}`, body.value, {
                                headers: {
                                    Authorization: `Bearer ${userToken.value}`
                                }
                            });
                            // Check if the response is successful (status code 200)                            
                            if (response2.status == 201) {
                                infraestructuraRecursoIsCorrect.value = true;
                                isLoading.value = true;
                                await getInfraestructuraRecursoData();
                                isLoading.value = false;
                            }
                        } catch (error) {
                            resetForm();
                            toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Se detectó un error, inténtelo más tarde.', life: 5000 });

                        }

                    } else {
                        // La infraestructura recurso esta vacia (sin inmuebles)
                        resetForm();
                        toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'La infraestructura debe tener inmuebles asociados.', life: 5000 });
                    }
                }

            }
            catch (error) {

                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Ocurrió un error, inténtalo más tarde', life: 5000 });
            }
            finally {
                dialogIsLoading.value = false;
            }
        };


        async function obtenerInmuebleAnioSeleccionado(anioProceso) {
            try {
                const response = await axios.get(API_BASE_URL + `infraestructuraRecurso/${anioProceso}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                inmuebleAnioSeleccionado.value = await response.data;
                if (inmuebleAnioSeleccionado.value.is_finalized == true) {
                    console.log(inmuebleAnioSeleccionado.value);
                    exportDataToCSV(anioProceso);
                } else {
                    toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El registro no puede ser modificado', life: 5000 });
                }

            } catch (error) {
                console.error("Error fetching data:", error);
            }
        }
        // Return data object
        return {
            userName,
            userEmail,
            permissionsList,
            isUserAuthenticated,
            globalLoading,
            isLoading,
            infraestructuraRecursoData,
            dialogVisibleCrear,
            inputYear,
            errorMessage,
            crearInfraestructuraRecurso,
            eliminarInfraestructura,
            dialogIsLoading,
            dialogVisibleEliminar,
            infraestructuraRecursoIsCorrect,
            resetForm,
            selectedYear,
            deletePreConfirmation,
            eliminarInfraestructuraRecurso,
            goToDetails,
            inmuebleAnioSeleccionado,
            obtenerInmuebleAnioSeleccionado,
            validatePreConfirmation,
            dialogConfirmarInfraestructura,
            finalized_text,
            pending_text,
            validarInfraestructuraRecurso,
            hasAccess,
            canAccess,
            userToken,
            tooltipText,
        };

        // Helper function to flatten and format the nested data
        function flattenData(data) {
            const result = [];

            // List of keys that contain nested arrays
            const categories = [
                "InmueblePermanentes",
                "InmuebleRestringidos",
                "Bibliotecas",
                "LibrosBasesDigitales",
                "Predios",
                "PlataformaVirtuals",
            ];

            // List of headers that need to be converted to uppercase
            const uppercaseFields = [
                "nombre_identificacion",
                "comuna",
                "direccion_inmueble",
                "nombre_institucion_comparte",
                "descripcion_otra_tenencia",
                "desc_otras_funciones",
                "ur_desc_actividades",
                "ur_desc_tenencia_otra",
                "sistema_gestion_aprendizajes",
                "sistema_video_conferencia",
                "sistema_aplicacion_evaluacion",
                "descripcion_plataforma_virtual",
            ];

            // Iterate through each category and process nested arrays
            categories.forEach((category) => {
                if (Array.isArray(data[category])) {
                    data[category].forEach((item) => {
                        const row = {};

                        // Map each header to the corresponding field in the item or set it as an empty string
                        csvHeaders.forEach((header) => {
                            let value = item[header] !== undefined ? item[header] : "";

                            // Convert to uppercase if the header is in the uppercaseFields list
                            if (uppercaseFields.includes(header) && typeof value === "string") {
                                value = value.toUpperCase();
                            }

                            row[header] = value;
                        });

                        // Add the row to the result array
                        result.push(row);
                    });
                }
            });

            return result;
        }

        // Function to export data to CSV
        function exportDataToCSV(anioProceso) {
            const csvData = flattenData(inmuebleAnioSeleccionado.value);

            // Convert data to CSV format using PapaParse
            const csv = Papa.unparse({
                fields: csvHeaders,
                data: csvData,
            });

            // Create a Blob for the CSV file and trigger download
            const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.setAttribute("href", url);
            link.setAttribute("download", "infraestructura_recurso-" + anioProceso + ".csv");
            link.style.visibility = "hidden";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

    }
};
</script>

<style scoped>
:deep() .center-header {
    text-align: center !important;
    align-content: center !important;
}

:deep() .p-datatable .p-column-header-content {
    display: block !important;

}

.yellow-icon .pi {
    color: yellow;
}

.green-icon .pi {
    color: greenyellow;
}
</style>