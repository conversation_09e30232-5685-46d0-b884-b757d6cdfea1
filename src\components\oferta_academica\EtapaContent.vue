<template>
    <div style="margin-left: 1rem;margin-right: 1rem;">
        <div style="display: flex;justify-content: space-between;align-items: center;">
            <Button v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                :disabled="cargaData.some(carga => carga.validado)"
                @click="$emit('validate', etapaNumber)">Validar</Button>

        </div
        >
        <!-- DataTable for Insumos -->
        <h3>Insumos</h3>
        <div class="flex justify-content-between align-items-center">
            <DataTable
                :value="insumosData"
                :sortOrder="-1"
                showGridlines
                scrollable
                scrollHeight="250px"
                style="width: 100%;"
            >
                <Column header="N° OAs" field="carrerasCount" style="width: 10%" class="center-header" />
                
<Column header="Fecha carga" field="fecha_carga" dateFormat="dd/mm/yy" style="width: 10%" class="center-header">
    <template #body="slotProps">
        {{ slotProps.data.fecha_carga ? new Date(slotProps.data.fecha_carga).toLocaleDateString() : 'Sin Datos' }}
    </template>
</Column>

                <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                    field="Cargar Datos" header="Cargar Datos" style="width: 5%" class="center-header">
                    <template #body>
                        <Button
                            icon="pi pi-upload"
                            class="p-button-text"
                            @click="$emit('upload', etapaNumber, 'Insumos')" 
                :disabled="cargaData.some(carga => carga.validado)"
                            />
                            
                    </template>
                </Column>
                <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                    field="Exportar" header="Exportar" style="width: 5%" class="center-header">
                    <template #body="slotProps">
                        <!-- <pre>{{ JSON.stringify(slotProps.data, null, 2) }}</pre> -->

                        <Button v-if="slotProps.data.carrerasCount > 0"
                            icon="pi pi-download"
                            class="p-button-text"
                            @click="$emit('export', etapaNumber, 'Insumos')"
                        />
                        <span v-else>Sin Datos</span>
                    </template>
                </Column>
                <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                    field="eliminar" header="Eliminar" style="width: 5%" class="center-header">
                    <template #body>
                        <Button icon="pi pi-trash"
                            class="p-button-text"
                            @click="$emit('delete', etapaNumber, 'Insumos')"
                :disabled="cargaData.some(carga => carga.validado)"

                        />
                    </template>
                </Column>
            </DataTable>
        </div>

        <h3 class="mt-4">Carga</h3>
        <DataTable
            :value="cargaData"
            :sortOrder="-1"
            showGridlines
            scrollable
            scrollHeight="250px"
            style="width: 100%;"
        >
                <Column header="Subido por" field="subido_por" style="width: 10%" class="center-header" />
            <Column header="N° OAs" field="carrerasCount" style="width: 10%" class="center-header" />
            <Column field="validado" header="Estado" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <div v-if="!slotProps.data.validado" v-tooltip.bottom="pendingText">
                        <Button disabled icon="pi pi-hourglass" class="p-button-text" style="color: orangered;" />
                    </div>
                    <div v-else v-tooltip.bottom="finalizedText">
                        <Button disabled icon="pi pi-verified" class="p-button-text green-icon" style="color: green;" />
                    </div>
                </template>
            </Column>
            <Column header="Fecha inicio" field="fecha_inicial" style="width: 10%" class="center-header">
                <template #body="slotProps">
                    {{ new Date(slotProps.data.fecha_inicial).toLocaleDateString() || 'Sin Datos' }}
                </template>
            </Column>
            <Column header="Fecha límite" field="fecha_final" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    {{ new Date(slotProps.data.fecha_final).toLocaleDateString() || 'Sin Datos' }}
                </template>
            </Column>
            <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                field="Cargar Datos" header="Cargar Datos" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <Button
                        icon="pi pi-upload"
                        class="p-button-text"
                        @click="$emit('upload', etapaNumber, 'Carga')"                         
                        :disabled="slotProps.data.validado"
/>
                </template>
            </Column>
            <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                field="Exportar" header="Exportar" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <Button v-if="slotProps.data.carrerasCount > 0"
                        icon="pi pi-download"
                        class="p-button-text"
                        @click="$emit('export', etapaNumber, 'Carga')"
                    />
                    <span v-else>Sin Datos</span>
                </template>
            </Column>
            <Column v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                field="eliminar" header="Eliminar" style="width: 5%" class="center-header">
                <template #body="slotProps">
                    <Button icon="pi pi-trash"
                        class="p-button-text"
                        @click="$emit('delete', etapaNumber, 'Carga')"
                        :disabled="slotProps.data.validado"

                    />
                </template>
            </Column>
        </DataTable>
    </div>
    <br />
</template>

<script>
export default {
    props: {
        etapaNumber: {
            type: Number,
            required: true
        },
        insumosData: {
            type: Array,
            required: true
        },
        cargaData: {
            type: Array,
            required: true
        },
        pendingText: {
            type: String,
            default: "Pendiente"
        },
        finalizedText: {
            type: String,
            default: "Finalizado"
        },
        hasAccess: {
            type: Function,
            required: true
        },

    },
    emits: ['validate', 'upload', 'delete','export'],
}
</script>

<style scoped>
:deep() .center-header {
    text-align: center !important;
    align-content: center !important;
}
</style>
