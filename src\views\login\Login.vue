<template>
  <div v-if="globalLoading">
    <div class="login-container">
      <div class='surface-card shadow-3 p-4 border-round'>
        <Loading></Loading>
      </div>
    </div>
  </div>
  <div v-else>
    <div class="login-container">
      <div v-if="!isLoading" class='surface-card shadow-3 p-4 border-round'>
        <div>
          <div class="center">
            <h2>Iniciar sesión con:</h2>
          </div>
          <div class="center">
            <Button type="button" label="Confirmar" @click="handleLogin" class="bordered-button">
              <img src="../../assets/outlook_icon.png" alt="logo_outlook" style="margin-right: 2rem; width: 50px;" />
              Cuenta institucional UAntof
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import { ref, computed } from 'vue';
import { useAuthStore } from '../../store/auth';
import { useRouter } from 'vue-router';
import ProgressSpinner from 'primevue/progressspinner';
import { encryptData, decryptData } from '@/utils/crypto';

export default {
  components: {
    ProgressSpinner,
  },
  setup() {
    const isLoading = ref(false);
    const authStore = useAuthStore();
    const globalLoading = computed(() => decryptData(authStore.isLoading));


    const handleLogin = () => {
      isLoading.value = true;
      authStore.login();
    };

    return {
      handleLogin,
      authStore,
      isLoading,
      globalLoading
    };
  }
}
</script>

<style>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 40vh;
  /* Full viewport height */
  padding: 20px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 40vh;
  /* Full viewport height */
  padding: 20px;

  width: 300px;
}

.bordered-button {
  border: 2px solid #007bff;
  /* Border color */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  /* Shadow effect */
  background-color: transparent;
  /* No background color */
  color: #007bff;
  /* Text color to match the border */
  padding: 0.5rem 1rem;
  /* Padding for the button */
  font-size: 1rem;
  /* Font size */
  cursor: pointer;
  /* Pointer cursor on hover */
  width: 100%;
}

.bordered-button:hover {
  background-color: rgba(0, 123, 255, 0.1);
  /* Light background on hover */
}
</style>
