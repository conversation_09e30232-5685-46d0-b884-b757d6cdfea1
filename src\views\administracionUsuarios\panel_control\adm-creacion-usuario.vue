<template>
    <div class="surface-ground py-7 md:px-5 lg:px-6">
        <div class="card">
            <form @submit.prevent="submitForm">
                <!--Creacion de un nuevo usuario-->
                <div style="margin-left: 1rem;margin-right: 1rem;">
                    <div v-if="true" class="formgrid grid">
                        <div class="field col-6" v-for="(field, index) in formFormat1" :key="field.id">
                            <label v-if="!field.dontNeedtoolTip" style="padding-right: 1rem ;">{{ field.displayName
                                }}</label>
                            <i class="pi pi-info-circle" v-if="!field.dontNeedtoolTip" style="justify-content: center "
                                v-tooltip.bottom="field.tooltip" />
                            <InputText :class="{
                                'w-full': true, 'p-invalid': (formData[field.name] == null || formData[field.name] == '') && submitted
                            }" v-if="field.contenedor === 'inputText'" :id=field.name autocomplete="off"
                                :placeholder=field.placeholder style="height:2.6rem" v-model="formData[field.name]"
                                :disabled="field.disabled" @input="handleInput($event, field.name)" :maxlength="200">
                            </InputText>
                            <Dropdown :class="{
                                'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                            }" v-if="field.contenedor === 'dropdown' && field.name == 'area_funcionario'"
                                v-model="formData[field.name]" filter :placeholder="field.placeholder"
                                style="height:2.6rem;margin-bottom: 1rem" :options="getOptions(field.options)"
                                :optionLabel="field.optionLabel" :optionValue="field.optionValue" class="w-full">
                            </Dropdown>
                            <Dropdown :class="{
                                'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                            }" v-if="field.contenedor === 'dropdown' && field.name == 'permisos_funcionario' && formData.area_funcionario != null"
                                v-model="formData[field.name]" filter :placeholder="field.placeholder"
                                style="height:2.6rem;margin-bottom: 1rem" :options="getOptions(field.options)"
                                :optionLabel="field.optionLabel" :optionValue="field.optionValue" class="w-full">
                            </Dropdown>
                        </div>
                    </div>
                </div>
                <div
                    style="display: flex; justify-content: center; align-items: center;padding-top: 0.5rem;padding-bottom: 0.5rem;">
                    <Button label="Crear" @click="submitForm()" />
                </div>
            </form>
        </div>
    </div>
    <Toast position="bottom-left" group="bl" />
</template>

<script>
import { useAuthStore } from '../../../store/auth';
import { ref, computed, onMounted, watch } from 'vue';
import axios from 'axios';
import { useToast } from 'primevue/usetoast';
import { encryptData, decryptData } from '@/utils/crypto';

export default {

    name: "adm-creacion-usuario",

    setup() {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const authStore = useAuthStore();
        // Access getters
        // Computed properties for userName and userEmail
        const toast = useToast();
        const userName = computed(() => authStore.userName);
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const permissionsList = computed(() => decryptData(authStore.permissionsList));
        const isUserAuthenticated = computed(() => authStore.userIsAuthenticated);
        const userToken = computed(() => authStore.idToken);
        const theme = ref("dark");
        const submitted = ref(null);
        const isLoading = ref(false);
        const rolesDataOriginal = ref(null);
        const recursosDataOriginal = ref(null);
        const rolesData = ref(null);
        const recursosData = ref(null);
        const isAdmin = ref(false);
        const areasEncargado = ref({});

        // Define initial form data
        const initialFormData = {
            nombre_funcionario: null,
            correo_funcionario: null,
            area_funcionario: null,
            permisos_funcionario: null,
            accion: "all"
        };

        const formData = ref({ ...initialFormData });

        const formFormat1 = ref([
            { id: 1, placeholder: 'Nombre del funcionario', name: 'nombre_funcionario', displayName: 'Nombre completo:', contenedor: 'inputText', tooltip: '' },
            { id: 2, placeholder: 'Correo institucional', name: 'correo_funcionario', displayName: 'Correo institucional:', contenedor: 'inputText', tooltip: '' },
            { id: 3, placeholder: 'Area laboral', name: 'area_funcionario', displayName: 'Area de trabajo:', contenedor: 'dropdown', options: 'recursosData', optionLabel: 'nombre_recurso', optionValue: "nombre_recurso", tooltip: '' },
            { id: 4, placeholder: 'Permisos', name: 'permisos_funcionario', displayName: 'Permisos a asignar:', contenedor: 'dropdown', options: 'rolesData', optionLabel: 'nombre_rol', optionValue: "nombre_rol", tooltip: '' },
        ]);

        /**
        * Get options for the dropdown field based on the field options string
        */
        const getOptions = (optionKey) => {
            if (optionKey === 'rolesData') {
                return rolesData.value;
            }
            if (optionKey === 'recursosData') {
                return recursosData.value;
            }
            return [];
        }

        // On component mount, fetch data
        onMounted(async () => {
            isLoading.value = true;
            await fetchAllRol();
            await fetchAllRecurso();
            await isUserAdmin();
            if (isAdmin.value == false) {
                await areasUsuarioEsEncargado();
                await updateListOfAreasOwned();
            }
            isLoading.value = false;

        });
        const areasUsuarioEsEncargado = async () => {
            // Filter permissionsList to include only items where rol is "Encargado"
            areasEncargado.value = permissionsList.value.filter(permission => permission.rol === 'Encargado' || permission.rol === 'Administrador Sistema');

            // Do something with areasEncargado, like saving it or processing further
            console.log('Filtered Areas:', areasEncargado.value);
        };
        const updateListOfAreasOwned = async () => {
            // Validate that areasEncargado.value and recursosData.value are defined
            if (!areasEncargado.value || !recursosData.value) {
                console.error('areasEncargado or recursosData is not defined.');
                return;
            }

            // Filter recursosData to include only resources present in areasEncargado
            recursosData.value = recursosData.value.filter(recurso =>
                areasEncargado.value.some(area => area.recurso === recurso.nombre_recurso)
            );

            console.log('Updated recursosData:', recursosData.value);
        };

        const isUserAdmin = async () => {
            const x = 'Administracion PIU';
            const y = 'Administrador Sistema';

            for (const permission of permissionsList.value) {
                if (permission.recurso === x && permission.rol === y) {
                    isAdmin.value = true;
                    break; // Exit loop as the match is found
                }
            }
            if (isAdmin.value == false) {
                // removes the option of "Administracion PIU" and "Administrador Sistema" from the dropdown
                recursosData.value = recursosData.value.filter(item => item.nombre_recurso !== "Administracion PIU");
                rolesData.value = rolesData.value.filter(item => item.nombre_rol !== "Administrador Sistema");
            }
        };

        // Watch for changes in "Permisos" and update "Área de trabajo"
        watch(() => formData.value.area_funcionario, async (newValue) => {
            // Set permisos_funcionario to null whenever area_funcionario changes
            formData.value.permisos_funcionario = null;
            rolesData.value = rolesDataOriginal.value;
            await isUserAdmin();
            if (newValue === 'Administracion PIU') {
                rolesData.value = rolesData.value.filter(role => role.nombre_rol === 'Administrador Sistema');
            } else {
                rolesData.value = rolesData.value.filter(role => role.nombre_rol !== 'Administrador Sistema');
            }
        });

        const fetchAllRol = async () => {
            isLoading.value = true; // Start loading
            try {
                const token = decryptData(authStore.idToken);
                const response = await axios.get(API_BASE_URL + "roles/", {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${decryptData(userToken.value)}`,
                    }
                });
                rolesDataOriginal.value = response.data;
                rolesData.value = response.data;
                console.log(rolesDataOriginal.value);
            } catch (error) {
                console.error("Error fetching roles data:", error);
            } finally {
                isLoading.value = false; // End loading
            }
        };

        const fetchAllRecurso = async () => {
            isLoading.value = true; // Start loading
            try {
                const response = await axios.get(API_BASE_URL + "recursos/" + userEmail.value);
                recursosDataOriginal.value = response.data;
                recursosData.value = response.data;
                console.log(recursosDataOriginal.value);
            } catch (error) {
                console.error("Error fetching recursos data:", error);
            } finally {
                isLoading.value = false; // End loading
            }
        };

        const submitForm = async () => {
            submitted.value = true;
            if (!validateForm()) {
                // Notify the user about the empty fields
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Existen campos vacios', life: 5000 });
                return;
            }

            try {
                isLoading.value = true;
                const success = await postNewUserData(formData.value);

                if (success) {
                    // Notify the user of successful form submission
                    toast.add({ severity: 'success', summary: 'Correcto', group: 'bl', detail: 'Formulario ingresado correctamente', life: 5000 });

                    // Reset form data after successful submission
                    formData.value = { ...initialFormData };
                    rolesData.value = rolesDataOriginal.value;
                    recursosData.value = recursosDataOriginal.value;
                    submitted.value = false; // Reset submitted state
                }
            } catch (error) {
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Hubo un problema al enviar el formulario', life: 5000 });
            } finally {
                isLoading.value = false;
            }
        };

        const validateForm = () => {
            let hasErrors = false;

            // Iterate through formData keys and validate non-conditional fields
            Object.keys(formData.value).forEach((key) => {
                const value = formData.value[key];

                // Check if the value is null or empty
                if (value === null || value === "") {
                    hasErrors = true;
                }
            });

            if (hasErrors) {
                // Return false if there are any errors
                return false;
            }
            return true;
        };

        const postNewUserData = async (formDataCompleted) => {
            isLoading.value = true; // Start loading
            try {
                formDataCompleted = {
                    nombre: formDataCompleted.nombre_funcionario,
                    email: formDataCompleted.correo_funcionario,
                    rol: formDataCompleted.permisos_funcionario,
                    recurso: formDataCompleted.area_funcionario,
                    accion: formDataCompleted.accion,
                    creador_email: userEmail.value,
                    userToken: "Bearer " + decryptData(userToken.value)
                }
                const response = await axios.post(API_BASE_URL + "usuario", formDataCompleted, {
                    headers: {
                        'Content-Type': 'application/json', // Set proper headers
                    },
                });
                // Check if the response is successful (status code 200)
                if (response.status === 201) {
                    return true; // Return true on success
                } else {
                    console.error("Unexpected response status:", response.status);
                    return false; // Return false if not 200 OK
                }
            } catch (error) {
                if (error.status == 409) {
                    toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El correo ingresado ya esta registrado en sistema', life: 5000 });
                }
                console.error("Error posting usuario data:", error.response || error);
                return false; // Return false on error
            } finally {
                isLoading.value = false; // End loading
            }
        };



        return {
            theme,
            authStore,
            userName,
            userEmail,
            permissionsList,
            isUserAuthenticated,
            initialFormData,
            isLoading,
            formData,
            formFormat1,
            getOptions,
            submitted,
            fetchAllRol,
            fetchAllRecurso,
            rolesData,
            recursosData,
            submitForm,
            validateForm,
            isAdmin,
            rolesDataOriginal,
            recursosDataOriginal,
            areasEncargado,
            updateListOfAreasOwned


        };
    },
    methods: {
        handleInput(event, fieldName) {
            let value = event.target.value;

            if (fieldName === 'nombre_funcionario') {
                // Remove starting spaces
                value = value.replace(/^\s+/, '');
                // Remove numbers and invalid symbols, but allow common name characters and symbols like spaces, apostrophes, and hyphens
                value = value.replace(/[^a-zA-ZáéíóúÁÉÍÓÚñÑ\s'-]/g, '');
                // Update form data with validated value
                this.formData[fieldName] = value;
            } else if (fieldName === 'correo_funcionario') {
                // Remove leading and trailing spaces
                value = value.trim();
                // Allow only typical characters for an email
                value = value.replace(/[^a-zA-Z0-9._@-]/g, '');
                // Remove spaces in the middle
                value = value.replace(/\s+/g, '');
                // Update form data with validated value
                this.formData[fieldName] = value;
            } else {
                // For other fields, update value directly
                this.formData[fieldName] = value;
            }
        }
    },
}
</script>

<style></style>