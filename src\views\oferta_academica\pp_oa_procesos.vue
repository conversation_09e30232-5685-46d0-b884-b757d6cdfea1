<template>
    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading">
            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <form @submit.prevent="submitForm">
                    <div class='surface-card shadow-3 p-3 border-round'>
                        <div
                            style="display: flex;justify-content: space-between;align-items: center;padding-left: 0.1rem;">
                            <h2>Ofertas Academicas</h2>
                            <Button
                                v-if="hasAccess('Encargado', 'Matricula Unificada') || hasAccess('Usuario General', 'Matricula Unificada') || hasAccess('Administrador Sistema', 'Administracion PIU')"
                                @click="dialogVisibleCrear = true">Nuevo</Button>
                        </div>

                        <DataTable :value="oa_data" stripedRows showGridlines paginator :rows="10"
                            sort-field="anio" :sort-order=-1 tableStyle="min-width: 50rem">
                            <!-- <Column field="nombre" header="nombre" class="center-header"></Column> -->
                            <Column field="anio" header="Año" class="center-header"></Column>
                            <Column field="creado_por" header="Autor" class="center-header"></Column>
                            <!-- <Column field="estudiante_count" header="N° Registros" class="center-header"></Column> -->

                            <Column field="is_finalized" header="Estado" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.is_finalized == null || slotProps.data.is_finalized == false"
                                        v-tooltip.bottom="pending_text">
                                        <Button disabled icon="pi pi-hourglass"
                                            style="justify-content: center;color: orangered;" class="p-button-text" />
                                    </div>
                                    <div v-else v-tooltip.bottom="finalized_text">
                                        <Button disabled icon="pi pi-verified"
                                            style="justify-content: center;color: green"
                                            class="p-button-text green-icon" />
                                    </div>
                                </template>
                            </Column>

                            <!-- <Column field="Fechas" header="Fechas Etapas" class="center-header" style="width: 15%;">

                                <template #body="slotProps">
                                    <div style="display: flex; justify-content: center; align-items: center;">
                                        <Button icon="pi pi-calendar" style="justify-content: center;"
                                            class="p-button-text"
                                            @click="goToDatesMU(slotProps.data.anio, slotProps.data.etapa_actual, slotProps.data.is_finalized)" />
                                        <div v-if="slotProps.data.fecha_etapa_count == 5"
                                            v-tooltip.bottom="'Fechas definidas'">
                                            <Button icon="pi pi-check" class="p-button-text" style="color: green;"
                                                disabled></Button>
                                        </div>
                                        <div v-else v-tooltip.bottom="'Fechas no definidas'">
                                            <Button icon="pi pi-times" class="p-button-text" style="color: red;"
                                                disabled></Button>
                                        </div>
                                    </div>
                                </template>
                            </Column> -->
                            <!-- <Column field="subprocesos" header="Subprocesos" class="center-header">
                                <template #body="slotProps">
                                    <Button icon="pi pi-list"
                                        style="justify-content: center;" class="p-button-text"
                                        @click="goToSubprocesos(slotProps.data.id,slotProps.data.anio)" />
                                </template>
                            </Column> -->
                            <Column field="ver" header="Ver" class="center-header">
                                <template #body="slotProps">
                                    <!-- v-if="slotProps.data.fecha_etapa_count === 5" -->
                                    <Button  icon="pi pi-eye"
                                        style="justify-content: center;" class="p-button-text"
                                        @click="goToSubprocesos(slotProps.data.id,slotProps.data.anio)" />
                                </template>
                            </Column>
                            <Column
                                v-if="hasAccess('Encargado', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU')"
                                field="eliminar" header="Eliminar" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.is_finalized != true">
                                        <Button icon="pi pi-trash" class="p-button-text"
                                            @click="dialogVisibleEliminar = true, selectedYear = slotProps.data.id" />
                                    </div>
                                </template>
                            </Column>
                            <!-- <Column
                                v-if="hasAccess('Encargado', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU')"
                                field="Exportar" header="Exportar" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.is_finalized == true">
                                        <Button icon="pi pi-download" style="" class="p-button-text"
                                            @click="obtenerOASeleccionado(slotProps.data.anio)" />
                                    </div>
                                </template>
                            </Column> -->
                        </DataTable>

                    </div>
                </form>
            </div>

        </div>
        <div v-else class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
                <Loading />
            </div>
        </div>
        <Toast position="bottom-left" group="bl" />
    </div>
    <Dialog v-model:visible="dialogVisibleCrear" modal header="Nuevo Registro" :style="{ width: '30rem' }"
        v-on:hide="resetForm()">
        <div v-if="dialogIsLoading && !oaIsCorrect">
            <Loading />
        </div>

        <!-- Form to create a new report -->
        <div v-if="!dialogIsLoading && !oaIsCorrect">
            <span class="p-text-secondary block mb-5">Crear nuevo proceso de Oferta Academica</span>

            <!-- Year input with validation -->
            <div class="flex align-items-center gap-3 mb-3" style="justify-content: space-between;">
                <div>
                    <label for="anio" class="font-semibold w-6rem">Año Proceso </label>
                    <i class="pi pi-info-circle" style="justify-self: center" v-tooltip.bottom="tooltipText" />
                </div>
                <div>
                    <InputNumber v-model="inputYear" inputId="anio" :useGrouping="false" style="width: 100%;"
                        :min="1950" :max="2100" :class="{ 'p-invalid': inputYearIsInvalid }" />
                </div>

            </div>


            <!-- Validation error message -->
            <div style="text-align: center;" v-if="errorMessageYearIsInvalid" class="p-error block mb-3">{{
                errorMessageYearIsInvalid }}
            </div>
            <div>
                <div class="flex align-items-center gap-3 mb-3" style="justify-content: space-between;">
                    <div>
                        <label for="fechaInicio" class="font-semibold w-6rem">Fecha Inicio </label>
                        <i class="pi pi-info-circle" style="justify-content: center" v-tooltip.bottom="tooltipText" />
                    </div>
                    <Calendar v-if="inputYear" v-model="fecha_inicio_1" dateFormat="dd/mm/yy" style="width: 58%;"
                        :disabled="!inputYear" :minDate="minDateFechaInicio" :maxDate="maxDateFechaInicio"
                        :invalid="fecha_inicio_1_isInvalid" />
                    <Calendar v-else dateFormat="dd/mm/yy" style="width: 58%;" :disabled="!inputYear" />


                </div>
                <div style="text-align: center;" v-if="errorMsg_FechaInicio" class="p-error block mb-3">{{
                    errorMsg_FechaInicio }}
                </div>
                <div class="flex align-items-center gap-3 mb-3" style="justify-content: space-between;">
                    <div>
                        <label for="fechaTermino" class="font-semibold w-6rem">Fecha Término </label>
                        <i class="pi pi-info-circle" style="justify-content: center" v-tooltip.bottom="tooltipText" />
                    </div>
                    <Calendar v-if="inputYear" v-model="fecha_termino_1" dateFormat="dd/mm/yy" style="width: 58%;"
                        :disabled="!inputYear" :minDate="minDateFechaTermino" :maxDate="maxDateFechaTermino"
                        :invalid="fecha_termino_1_isInvalid" />
                    <Calendar v-else dateFormat="dd/mm/yy" style="width: 58%;" :disabled="!inputYear" />
                </div>
            </div>
            <div style="text-align: center;" v-if="errorMsg_FechaTermino" class="p-error block mb-3">{{
                errorMsg_FechaTermino }}
            </div>

            <!-- Buttons -->
            <div class="flex justify-content-end gap-2">
                <Button type="button" label="Cancelar" severity="secondary"
                    @click="dialogVisibleCrear = false"></Button>
                <Button type="button" label="Crear" @click="createOAProceso"></Button>
            </div>
        </div>

        <!-- Success message -->
        <div v-if="!dialogIsLoading && oaIsCorrect"
            style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
            <i class="pi pi-check" style="font-size: 5rem; color: blue;"></i>
            <span class="p-text-secondary block mb-5">¡Creación exitosa!</span>
        </div>
    </Dialog>
    <Dialog v-model:visible="dialogVisibleEliminar" modal header="Eliminar Registro" :style="{ width: '25rem' }"
        v-on:hide="resetForm()">
        <div v-if="dialogIsLoading && !oaIsCorrect">
            <Loading />
        </div>

        <div v-if="!dialogIsLoading && !oaIsCorrect">
            <div>
                <!-- Validation error message -->
                <p>Esto eliminará permanentemente este registro ¿Está seguro?</p>
            </div>
            <br />
            <div class="flex justify-content-center gap-2">
                <InputSwitch style="scale: 1.5;" v-model="deletePreConfirmation"></InputSwitch>
            </div>
            <br />
            <!-- Validation error message -->
            <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}<br /></div>


            <!-- Buttons -->
            <div class="flex justify-content-end gap-2">
                <Button type="button" label="Cancelar" severity="secondary"
                    @click="dialogVisibleEliminar = false"></Button>
                <Button v-if="deletePreConfirmation" type="button" label="Eliminar"
                    @click="eliminarOAProcesos"></Button>
            </div>
        </div>

        <!-- Success message -->
        <div v-if="!dialogIsLoading && oaIsCorrect"
            style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
            <i class="pi pi-trash" style="font-size: 5rem; color: blue;"></i>
            <br />
            <span class="p-text-secondary block mb-5">¡Eliminado correctamente!</span>
        </div>

    </Dialog>

</template>

<script>
import { useAuthStore } from '../../store/auth';
import { ref, computed, onMounted } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import axios from 'axios';
import Papa from 'papaparse';
import { useToast } from 'primevue/usetoast';
import { useRoute, useRouter } from 'vue-router';

export default {

    name: "pp_oa_procesos",

    setup() {

        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const router = useRouter();
        const route = useRoute();
        const toast = useToast();
        const authStore = useAuthStore();
        const permissionsList = computed(() => decryptData(authStore.permissionsList));
        const globalLoading = computed(() => decryptData(authStore.isLoading));
        const userToken = computed(() => decryptData(authStore.idToken));
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const isLoading = ref(false);
        const oa_data = ref([]);
        const dialogConfirmarMU_post = ref(false);
        const selectedYear = ref(null);
        const dialogVisibleCrear = ref(false);
        const dialogVisibleEliminar = ref(false);
        const inputYear = ref(null);
        const inputYearIsInvalid = ref(false);
        const inputProceso = ref(null);

        const errorMessage = ref('');
        const errorMessageYearIsInvalid = ref('');
        const errorMsg_FechaInicio = ref('');
        const errorMsg_FechaTermino = ref('');
        const oaIsCorrect = ref(false);
        const oaAnioSelecionado = ref(null);
        const deletePreConfirmation = ref(false);
        const dialogIsLoading = ref(false);
        const validatePreConfirmation = ref(false);
        const dialogConfirmarMU = ref(false);
        const finalized_text = ref("Finalizado");
        const pending_text = ref("Pendiente");
        const fecha_inicio_1_isInvalid = ref(false);
        const fecha_inicio_1 = ref();
        const fecha_termino_1 = ref();
        const fecha_termino_1_isInvalid = ref(false);
        const minDateFechaInicio = computed(() => new Date(inputYear.value - 1, 0, 1));
        const maxDateFechaInicio = computed(() => new Date(inputYear.value + 1, 11, 31));
        const minDateFechaTermino = computed(() => new Date(inputYear.value - 1, 0, 1));
        const maxDateFechaTermino = computed(() => new Date(inputYear.value + 1, 11, 31));


        const hasAccess = (allowedRol, requiredRecurso) => {
            const access = permissionsList.value.some(user => user.rol === allowedRol && user.recurso === requiredRecurso);
            if (access) {
                return true;
            } else {
                return false;
            }

        };

        onMounted(async () => {
            isLoading.value = true;
            await getAllOAProcesos();
            //await getAllMuPregradoFecha();
            console.log(oa_data.value)
            isLoading.value = false;
        });

        // Function to reset the form for a new creation
        const resetForm = () => {
            inputYear.value = null; // Reset the year input
            errorMessage.value = ''; // Clear any error messages
            oaIsCorrect.value = false; // Reset success state
            dialogVisibleCrear.value = false; // Close the dialog
            deletePreConfirmation.value = false;
            dialogVisibleEliminar.value = false;
            validatePreConfirmation.value = false;
            dialogConfirmarMU.value = false;
        }
        // const goToDetails = (proceso_id) => {
        //     const encrypted_proceso_id = encryptData(proceso_id);
        //     router.push({ name: 'oferta-academica-sies-detalle', params: { proceso_id: encrypted_proceso_id } });
        // };

        const goToSubprocesos = (proceso_id,anio_proceso) => {
            console.log('Navigating to subprocesos:', proceso_id, anio_proceso);
            const encrypted_proceso_id = encryptData(proceso_id);
            const encrypted_anio= encryptData(anio_proceso);
            router.push({ name: 'oferta-academica-sies-subprocesos', params: { proceso_id: encrypted_proceso_id, anio_proceso: encrypted_anio } });
        };
        const goToDatesMU = (anio, etapa, isFinalized) => {
            const encrypted_year = encryptData(anio);
            const encrypted_etapa = encryptData(etapa);
            const encrypted_isFinalized = encryptData(isFinalized);
            router.push({ name: 'MU-pregrado-fechas/anio', params: { anio: encrypted_year, etapa_actual: encrypted_etapa, is_finalized: encrypted_isFinalized } });
        };
        const createOAProceso = async () => {
            // Clear previous error messages
            errorMessage.value = '';
            inputYearIsInvalid.value = false;
            errorMessageYearIsInvalid.value = '';
            errorMsg_FechaInicio.value = '';
            fecha_inicio_1_isInvalid.value = false;
            fecha_termino_1_isInvalid.value = false;
            errorMsg_FechaTermino.value = '';
            // Validate the year input
            if (inputYear.value === null) {
                errorMessageYearIsInvalid.value = 'El valor ingresado no es valido';
                inputYearIsInvalid.value = true;
                return;
            }
            let hasError = false;

            if (inputYear.value === null) {
                errorMessageYearIsInvalid.value = 'El valor ingresado no es valido';
                inputYearIsInvalid.value = true;
                hasError = true;
            }

            if (!fecha_inicio_1.value) {
                errorMsg_FechaInicio.value = 'La fecha de inicio es obligatoria';
                fecha_inicio_1_isInvalid.value = true;
                hasError = true;
            }

            if (!fecha_termino_1.value) {
                errorMsg_FechaTermino.value = 'La fecha de término es obligatoria';
                fecha_termino_1_isInvalid.value = true;
                hasError = true;
            }
            // Validate that fecha_inicio_1 is not greater than fecha_termino_1
            if (fecha_inicio_1.value && fecha_termino_1.value && fecha_inicio_1.value > fecha_termino_1.value) {
                errorMsg_FechaInicio.value = 'La fecha de inicio no puede ser mayor que la fecha de término';
                fecha_inicio_1_isInvalid.value = true;
                fecha_termino_1_isInvalid.value = true;
                hasError = true;
            }

            if (hasError) {
                return;
            }

            dialogIsLoading.value = true;
            await getAllOAProcesos();

            // Check if `anio` matches `inputYear.value` in any item of `infraestructuraRecursoData.value`
            const isDuplicate = oa_data.value.some(item => item.anio === inputYear.value);

            if (isDuplicate) {
                // If a match is found, throw the error
                errorMessageYearIsInvalid.value = 'El valor ingresado ya existe';
                inputYearIsInvalid.value = true;
                dialogIsLoading.value = false; // Stop loading since an error occurred
                return; // Prevent further execution
            } else {
                const response = await postOAProceso();
                dialogIsLoading.value = false;
                if (response == "ok") {
                    oaIsCorrect.value = true
                    // If no match is found, proceed with saving the report
                    console.log('Saving report for year:', inputYear.value);
                    await getAllOAProcesos();
                }
            }

        }

        const postOAProceso = async () => {
            // Define the request body
            const requestBody = {
                anio: inputYear.value,
                fecha_inicio: fecha_inicio_1.value,
                fecha_final: fecha_termino_1.value
            };
            try {
                dialogIsLoading.value = true;
                const response = await axios.post(API_BASE_URL + "oa_proceso", requestBody, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                return "ok";
            } catch (error) {
                console.error("Error: ", error);
            } finally {
                dialogIsLoading.value = false;
            }
        };
//getMUPregradoData
        const getAllOAProcesos = async () => {
            try {
                isLoading.value = true;

                const response = await axios.get(API_BASE_URL + "oa_proceso", {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });

                if (response.status == 200) {
                    oa_data.value = response.data;
                }
            } catch (error) {
                console.error("Error: ", error);
            } finally {
                isLoading.value = false;
            }
        };
        const getAllMuPregradoFecha = async () => {
            try {
                isLoading.value = true;

                const response = await axios.get(`${API_BASE_URL}mu_pregrado_fecha`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    },
                });
                console.log(response.data);
                /*if (response.status == 200) {
                    mu_pregrado_fecha_data.value = response.data;

                    if (mu_pregrado_fecha_data.value.length < 5) {
                        const currentLength = mu_pregrado_fecha_data.value.length;
                        for (let i = currentLength; i < 5; i++) {
                            mu_pregrado_fecha_data.value.push({
                                etapa_proceso: i + 1,
                                fecha_inicio: "Sin definir",
                                fecha_termino: "Sin definir"
                            });
                        }
                    }
                    mu_pregrado_fecha_data.value = mu_pregrado_fecha_data.value.map(item => {
                        return {
                            ...item,
                            estado_etapa: item.etapa_proceso < mu_pregrado_data_actual.value.etapa_actual ? true : false
                        };
                    });
                } else if (response.status == 204) {
                    mu_pregrado_fecha_data.value = [];
                } else {
                    toast.add({
                        severity: 'error',
                        summary: 'Error',
                        detail: 'Error al cargar los datos de la tabla, intentelo nuevamente.',
                        group: "bl",
                        life: 3000
                    });
                    console.error("Error: ", response);
                }*/
            } catch (error) {
                toast.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Error al cargar los datos de la tabla, intentelo nuevamente.',
                    group: "bl",
                    life: 3000
                });
                console.error("Error: ", error);
            } finally {
                isLoading.value = false;
            }
        }

        const eliminarOAProcesos = async () => {
            errorMessage.value = '';

            const eliminarResponse = await eliminarOA();

            if (eliminarResponse == "ok") {
                oaIsCorrect.value = true;
                console.log('Deleting OA process for year:', selectedYear.value);
                await getAllOAProcesos();
            } else if (eliminarResponse == "conflicto") {
                errorMessage.value = 'El elemento no debe tener registros asociados a él';
            } else if (eliminarResponse == "error") {
                errorMessage.value = 'Se detectó un error, inténtelo más tarde';
            }
        }

        const eliminarOA = async () => {
            errorMessage.value = '';

            try {
                dialogIsLoading.value = true;
                const response = await axios.delete(API_BASE_URL + `oa_proceso/${selectedYear.value}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                return "ok";
            } catch (error) {
                console.error("Error: ", error);
                if (error.response?.status === 409) {
                    return "conflicto";
                } else {
                    if (error.response?.status === 403) {
                        toast.add({
                            severity: 'error',
                            summary: 'Error',
                            group: 'bl',
                            detail: 'El registro no puede ser modificado',
                            life: 5000
                        });
                    }
                    return "error";
                }
            } finally {
                dialogIsLoading.value = false;
            }
        }

        const obtenerOASeleccionado = async (anioProceso) => {
            try {
                const response = await axios.get(API_BASE_URL + `oa_proceso/${anioProceso}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                oaAnioSelecionado.value = await response.data;
                if (oaAnioSelecionado.value.is_finalized) {
                    console.log(oaAnioSelecionado.value);
                    exportDataToCSV(anioProceso);
                } else {
                    toast.add({
                        severity: 'error',
                        summary: 'Error',
                        group: 'bl',
                        detail: 'El registro no puede ser modificado',
                        life: 5000
                    });
                }
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        }


        return {
            globalLoading,
            isLoading,
            hasAccess,
            oa_data,
            dialogConfirmarMU_post,
            selectedYear,
            dialogVisibleCrear,
            dialogVisibleEliminar,
            resetForm,
            inputYear,
            inputProceso,
            errorMessage,
            errorMessageYearIsInvalid,
            errorMsg_FechaInicio,
            errorMsg_FechaTermino,
            oaIsCorrect,
            deletePreConfirmation,
            dialogIsLoading,
            validatePreConfirmation,
            dialogConfirmarMU,
            createOAProceso,
            finalized_text,
            pending_text,
            eliminarOAProcesos,
            obtenerOASeleccionado,
            oaAnioSelecionado,
            goToSubprocesos,
            goToDatesMU,
            fecha_inicio_1,
            fecha_termino_1,
            minDateFechaInicio,
            maxDateFechaInicio,
            minDateFechaTermino,
            maxDateFechaTermino,
            inputYearIsInvalid,
            fecha_inicio_1_isInvalid,
            fecha_termino_1_isInvalid,
            getAllMuPregradoFecha


        };

    }
}
</script>

<style scoped>
:deep() .center-header {
    text-align: center !important;
    align-content: center !important;
}

:deep() .p-datatable .p-column-header-content {
    display: block !important;

}

.yellow-icon .pi {
    color: yellow;
}

.green-icon .pi {
    color: greenyellow;
}
</style>
