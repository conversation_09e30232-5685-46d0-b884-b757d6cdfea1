<template>
    <div>
        <div
            :class="{ 'dark-lighter-mode px-4 py-5 md:px-6 lg:px-8': checkDarkMode, 'surface-ground px-4 py-5 md:px-6 lg:px-8': !checkDarkMode }">
            <DataTable id="tableHeader"
                :class="{ 'dataTableClass shadow-4': checkDarkMode, 'shadow-4': !checkDarkMode }"
                v-model:filters="filters" :value="dataForDisplay" v-model:editingRows="editingRows" editMode="row"
                sortMode="multiple" removableSort paginator showGridlines :rows="10" update:filters="handlePageChange"
                :rowsPerPageOptions="[5, 10, 20, 50]" dataKey="Id" filterDisplay="menu" :loading="loading"
                :globalFilterFields="['Nombre', 'Apellido', 'rut', 'Fecha', 'Carrera']" @row-edit-save="onRowEditSave"
                :pt="{
                    table: { style: 'min-width: 50rem' },
                    column: {
                        bodycell: ({ state }) => ({
                            style: state['d_editing'] && 'padding-top: 0.6rem; padding-bottom: 0.6rem'
                        })
                    }
                }">

                <!-- Button clear and search bar -->
                <template #header>
                    <Message v-if=showErrorMessage severity="error">{{ errorOrSuccesMessage }}
                    </Message>
                    <Message v-if=showSuccessMessage severity="success">{{ errorOrSuccesMessage }}</Message>
                    <h2 class="h2">Matrículas: </h2>

                    <div v-if="isDesktop" class="dataTableTOP">
                        <div class="left">
                            <Button type="button" class="shadow-3" icon="pi pi-filter-slash" label="Limpiar" outlined
                                @click="clearFilter()"></Button>
                        </div>
                        <div class="right">
                            <IconField iconPosition="center">
                                <InputIcon>
                                    <i class="pi pi-search"></i>
                                </InputIcon>
                                <InputText class="SearchBar" v-model="filters['global'].value" placeholder="Buscar">
                                </InputText>

                            </IconField>
                        </div>
                    </div>
                    <div v-else-if="isTablet" class="dataTableTOP">
                        <div class="left">
                            <Button type="button" class="shadow-3" icon="pi pi-filter-slash" label="Limpiar" outlined
                                @click="clearFilter()"></Button>
                        </div>
                        <div class="right">
                            <IconField iconPosition="center">
                                <InputIcon>
                                    <i class="pi pi-search"></i>
                                </InputIcon>
                                <InputText class="SearchBar" v-model="filters['global'].value" placeholder="Buscar">
                                </InputText>

                            </IconField>
                        </div>
                    </div>
                    <div v-else class="dataTableTOP">
                        <div class="left">
                            <Button type="button" class="shadow-3" icon="pi pi-filter-slash" label="" outlined
                                @click="clearFilter()"></Button>
                        </div>
                        <div class="right">
                            <IconField iconPosition="center">
                                <InputIcon>
                                    <i class="pi pi-search"></i>
                                </InputIcon>
                                <InputText class="SearchBar" v-model="filters['global'].value" placeholder="">
                                </InputText>

                            </IconField>
                        </div>
                    </div>
                </template>
                <!-- No data found and loading -->
                <template #empty> No se encontró información. </template>
                <template #loading>
                    <div class="form-row">
                        <div class="form-group">
                            <i class="pi pi-spin pi-cog" style="font-size: 6rem"></i>
                            <br></br><br></br>
                            <div><label class="labelLoading">{{ loadingText }}</label></div>
                        </div>
                    </div>
                </template>
                <!-- Column Person Name of DataTable divided by template #editor, #body(display the data) and #filter -->
                <Column field="Nombre" header="Nombre" sortable style="min-width: 12rem">
                    <template #editor="{ data, field }">
                        <InputText v-model="data[field]" />
                    </template>
                    <template #body="{ data, field }">
                        {{ data[field] }}
                    </template>
                    <template #filter="{ filterModel }">
                        <InputText v-model="filterModel.value" type="text" class="p-column-filter"
                            placeholder="Buscar por Nombre" />
                    </template>
                </Column>
                <!-- Column Apellido of DataTable divided by template #editor, #body(display the data) and #filter -->
                <Column field="Apellido" header="Apellido" sortable style="min-width: 12rem">
                    <template #editor="{ data, field }">
                        <InputText v-model="data[field]" />
                    </template>
                    <template #body="{ data, field }">
                        {{ data[field] }}
                    </template>
                    <template #filter="{ filterModel }">
                        <InputText v-model="filterModel.value" type="text" class="p-column-filter"
                            placeholder="Buscar por Apellido" />
                    </template>
                </Column>
                <!-- Column Rut of DataTable divided by template #editor, #body(display the data) and #filter -->
                <Column field="rut" header="Rut" sortable style="min-width: 12rem">
                    <template #editor="{ data, field }">
                        <InputText v-model="data[field]" />
                    </template>
                    <template #body="{ data, field }">
                        {{ data[field] }}
                    </template>
                    <template #filter="{ filterModel }">
                        <InputText v-model="filterModel.value" type="text" class="p-column-filter"
                            placeholder="Buscar por Rut" />
                    </template>
                </Column>
                <!-- Column Date of DataTable divided by template #editor, #body(display the data) and #filter -->
                <Column field="Fecha" header="Fecha" filterField="Fecha" dataType="date" sortable
                    style="min-width: 10rem">
                    <template #editor="{ data, field }">
                        <Calendar v-model="data[field]" dateFormat="dd/mm/yy" :placeholder=formatDate(data.Fecha)
                            mask="99/99/9999" />
                    </template>
                    <template #body="{ data, field }" dateFormat="dd/mm/yy">
                        {{ formatDate(data[field]) }}
                    </template>
                    <template #filter="{ filterModel }">
                        <Calendar v-model="filterModel.value" dateFormat="dd/mm/yy" placeholder="dd/mm/aaaa"
                            mask="99/99/9999" />
                    </template>
                </Column>
                <!-- Column Rut of DataTable divided by template #editor, #body(display the data) and #filter -->
                <Column field="Carrera" header="Carrera" sortable style="min-width: 12rem">
                    <template #editor="{ data, field }">
                        <InputText v-model="data[field]" />
                    </template>
                    <template #body="{ data }">
                        {{ data.Carrera }}
                    </template>
                    <template #filter="{ filterModel }">
                        <InputText v-model="filterModel.value" type="text" class="p-column-filter"
                            placeholder="Buscar por carrera" />
                    </template>
                </Column>
                <!-- Column necesary for the edition of rows-->
                <Column :rowEditor="true" style="width: 10%; min-width: 8rem" bodyStyle="text-align:center"></Column>
            </DataTable>
        </div>
    </div>
</template>


<script>
import { ref, computed, onMounted, onUnmounted, reactive, watch } from 'vue';
import { FilterMatchMode, FilterOperator } from 'primevue/api';
import axios from 'axios';
import store from '../store/store.js';

export default {
    name: 'MatriculaView1',

    setup() {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const filters = ref();
        const loading = ref(false);
        const checkDarkMode = computed(() => store.getters.isDarkMode);
        const editingRows = ref([]);
        const color = ref('');
        const dataForDisplay = reactive([]);
        const error = ref(null);
        const screenWidth = ref(window.innerWidth);
        const isDesktop = computed(() => screenWidth.value >= 1024);
        const isTablet = computed(() => screenWidth.value >= 768 && screenWidth.value < 1024);
        const loadingTexts = ['cargando', 'cargando.', 'cargando..', 'cargando...'];
        const loadingIndex = ref(0);
        const loadingText = ref(loadingTexts[0]);
        const color1 = ref('');
        const color2 = ref('');
        const counter = ref(null);
        const showErrorMessage = ref(false);
        const showSuccessMessage = ref(false);
        const errorOrSuccesMessage = ref('');
        // PrimeVue format for filters used in the DataTable
        const initFilters = () => {
            filters.value = {
                global: { value: null, matchMode: FilterMatchMode.CONTAINS },
                Nombre: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }] },
                Apellido: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }] },
                rut: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }] },
                Fecha: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.DATE_IS }] },
                Carrera: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }] },
            };
        };
        /**
         * Change the format of the date column, to day/month/year for better visibility
         * @param value date of the "estudiante" model
         */
        const formatDate = (value) => {
            const dateObject1 = new Date(value);
            return dateObject1.toLocaleDateString('es', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            });
        };
        /**
         * Clear the filters of the search bar and each filter of the columns
         */
        const clearFilter = () => {

            initFilters();
            if (checkDarkMode.value == true) {
                overrideStyle('#000000');
            } else {
                overrideStyle('#ffffff');
            }

        };

        initFilters();

        /**
         * Checks the value of the window size
         */
        const handleResize = () => {
            screenWidth.value = window.innerWidth;
        };

        /**
         * Update the values of the color1 and color2 and calls "overrideStyle",
         * to match the nightmode color or daymode color
         */
        const updateDayNightMode = () => {
            if (checkDarkMode.value == true) {
                color1.value = '#ffffff';
                color2.value = '#000000';
                overrideStyle('#000000');

            }
            if (checkDarkMode.value == false) {
                color1.value = '#000000';
                color2.value = '#ffffff';
                overrideStyle('#ffffff');
            }
        };

        /**
         * Load the data from the back-end into dataForDisplay array, that has the DataTable format 
         * @param array is the response from the "GET estudiantes"
         */
        const loadDataToDisplay = (array) => {
            console.log(array);
            for (const element of array) {
                element.estudiante_Fecha = new Date(element.estudiante_Fecha);
                dataForDisplay.push({
                    Nombre: element.estudiante_Nombre, Apellido: element.estudiante_Apellido,
                    rut: element.estudiante_Rut, Fecha: element.estudiante_Fecha, Carrera: element.estudiante_Carrera,
                    Id: element.estudiante_id, version: element.version
                });
            }
        };


        /** 
         * Moves the loading text like a gif
        */
        const updateLoadingText = () => {
            setInterval(() => {
                loadingIndex.value = (loadingIndex.value + 1) % loadingTexts.length;
                loadingText.value = loadingTexts[loadingIndex.value];
            }, 500);
        };

        /**
         * Edit the values of the selected row.
         * Check if the values entered by the user are valid.
         * 
         * @param event is automatically generated by PrimeVue  when clicking 
         * the "edit button" whith the values of the specific row
         */
        const onRowEditSave = async (event) => {
            console.log(event);
            loading.value = true;
            let { newData, index } = event;
            if (!newData.Nombre || newData.Nombre == "" || newData.Nombre.length >= 100 || parseInt(newData.Nombre)) {
                showErrorMessage.value = true;
                showSuccessMessage.value = false;
                errorOrSuccesMessage.value = "Error al editar los campos del estudiante";
                loading.value = false;
                return;
            }
            if (!newData.Apellido || newData.Apellido == "" || newData.Apellido.length >= 100 || parseInt(newData.Apellido)) {
                showErrorMessage.value = true;
                showSuccessMessage.value = false;
                errorOrSuccesMessage.value = "Error al editar los campos del estudiante";
                loading.value = false;
                return;
            }
            if (!newData.Carrera || newData.Carrera == "" || newData.Carrera.length >= 150 || parseInt(newData.Carrera)) {
                showErrorMessage.value = true;
                showSuccessMessage.value = false;
                errorOrSuccesMessage.value = "Error al editar los campos del estudiante";
                loading.value = false;
                return;
            }
            if (!newData.rut || newData.rut == "" || !validarRut(newData.rut)) {
                showErrorMessage.value = true;
                showSuccessMessage.value = false;
                errorOrSuccesMessage.value = "Error al editar los campos del estudiante";
                loading.value = false;
                return;
            }
            if (!newData.Fecha || newData.Fecha == "") {
                showErrorMessage.value = true;
                showSuccessMessage.value = false;
                errorOrSuccesMessage.value = "Error al editar los campos del estudiante";
                loading.value = false;
                return;
            }

            newData.Fecha = newData.Fecha.toISOString().split('T')[0];
            newData.rut = newData.rut.replace(/[.\-\s]/g, '') // Remover puntos , guiones Y espacios


            if (await UpdateData(newData)) {
                newData.Fecha = new Date(newData.Fecha);
                dataForDisplay[index] = newData;

            } else {
            }

            loading.value = false;
        };


        /**
         * Makes a PUT call to the back-end with the user rut to modify the given student data
         * 
         * @param data the user modifed data needed to update a register in the database
         */
        const UpdateData = async (data) => {
            console.log(data);
            try {
                loading.value = true;
                const response = await axios.put(API_BASE_URL + "estudiante/" + data.rut, {
                    body: data
                });
                if (response.status === 204) {
                    console.log('Successfully updated');
                    showSuccessMessage.value = true;
                    errorOrSuccesMessage.value = ('Estudiante modificado con éxito');
                    showErrorMessage.value = false;
                    loading.value = false;
                    dataForDisplay.length = 0;
                    await getData();
                    return true;
                }
            } catch (error) {
                console.log(error);
                if (error.response.status === 409) {
                    console.log('Error de concurrencia');
                    errorOrSuccesMessage.value = "Error de concurrencia inténtelo nuevamente";
                    showErrorMessage.value = true;
                    dataForDisplay.length = 0;
                    await getData();
                    handleSuccessMessageClose();                    

                } else {
                    console.log('Error');
                    errorOrSuccesMessage.value = "Error!!!";
                    showErrorMessage.value = true;
                    handleSuccessMessageClose();
                }
            } finally {
                console.log("Finally");
                loading.value = false;
                return null;
            }

        };
        /**
         * Validate the format of a given rut and check if it valid
         * @param rut the rut given by the user
         */
        function validarRut(rut) {
            if (rut !== undefined && typeof rut.toString === 'function') {

                rut = rut.toString();
                rut = rut.replace(/[.\-\s]/g, '') // Remover puntos , guiones Y espacios
                if (!/^[0-9]+[0-9kK]{1}$/.test(rut)) return false; // Formato incorrecto

                // separar digito verificador
                var num = rut.substring(0, rut.length - 1);
                var dig = rut.substring(rut.length - 1);

                // Calcular dígito verificador
                var suma = 0;
                var multiplo = 2;

                // Para cada dígito del número, de derecha a izquierda
                for (var i = num.length - 1; i >= 0; i--) {
                    suma += parseInt(num.charAt(i)) * multiplo;
                    if (multiplo === 7) multiplo = 2;
                    else multiplo++;
                }

                // Calcular el resto de la división
                var resto = suma % 11;
                var dv = 11 - resto;

                // Verificar el dígito verificador
                if (dv === 11) dv = 0;
                else if (dv === 10) dv = 'k';
                else dv = dv.toString();
                //return true si el digito verificador y el digito calculado son iguales
                return dv == dig.toLowerCase() || dv == dig.toUpperCase();
            } else {
                return false
            }
        }


        /**
         * Modify the attributes of the DataTable Styles to match the nightMode and DayMode toggle
         */
        const setclassToDayNightMode = () => {
            //Header
            var element = document.getElementsByClassName("p-datatable-header");
            element[0].setAttribute("style", "background-color: " + color2.value + " !important;");
            var element2 = document.getElementsByClassName("p-datatable-header");
            element2[0].style.color = color1.value;
            var element3 = document.getElementsByClassName("p-button p-component p-button-outlined shadow-3");
            element3[0].style.border = '1px solid';
            element3[0].style.color = color1.value;
            var element4 = document.getElementsByClassName("p-button-icon p-button-icon-left pi pi-filter-slash");
            if (element4.style) {
                element4[0].style.color = color1.value;
            }

            var element5 = document.getElementsByClassName("SearchBar");
            if (element5.style) {
                //console.log(element5);
                element5[0].style.color = "#000000";
            }

            var element6 = document.getElementsByClassName("pi pi-search");
            element6[0].style.color = "#000000";

            //Body columnas
            // Get the <th> elements using querySelectorAll with CSS selectors
            var thElements = document.querySelectorAll('.p-datatable.p-datatable-gridlines .p-datatable-thead>tr>th');
            // Loop through the <th> elements and do something with each one
            thElements.forEach(function (thElement) {
                // Example: Change background color of each <th>
                thElement.style.background = color2.value;
                thElement.style.color = color1.value;
            });

            // JavaScript code
            // Get elements by class name
            var dataTableElements = document.getElementsByClassName("p-datatable");
            var sortableColumnElements = [];

            // Loop through dataTableElements to find .p-sortable-column
            for (var i = 0; i < dataTableElements.length; i++) {
                var dataTable = dataTableElements[i];
                var sortableColumns = dataTable.getElementsByClassName("p-sortable-column");
                for (var j = 0; j < sortableColumns.length; j++) {
                    var sortableColumn = sortableColumns[j];
                    sortableColumnElements.push(sortableColumn);
                }
            }

            // Loop through sortableColumnElements to change the "double arrow" Icon
            for (var k = 0; k < sortableColumnElements.length; k++) {
                var sortableColumn = sortableColumnElements[k];
                var sortableColumnIcon = sortableColumn.getElementsByClassName("p-sortable-column-icon")[0];
                // Example: Change background color of .p-sortable-column-icon
                sortableColumnIcon.style.color = color1.value;
            }


            // Table body
            // Get the <tbody> element using getElementsByClassName
            var tbodyElements = document.getElementsByClassName('p-datatable-tbody');

            // Loop through the <tbody> elements and do something with each one
            for (var i = 0; i < tbodyElements.length; i++) {
                var tbodyElement = tbodyElements[i];
                // Use querySelectorAll to get <tr> elements inside each <tbody>
                var trElements = tbodyElement.querySelectorAll('tr');
                // Loop through the <tr> elements and do something with each one
                trElements.forEach(function (trElement) {
                    // Example: Change background color of each <tr> (text of each row)
                    trElement.setAttribute("style", "background-color: " + color2.value + " !important; color: " + color1.value + " !important;");

                });
            }

            // Edit Text Font
            // Get the elements matching the specified selector
            const elementsEditFont = document.querySelectorAll('.p-inputtext.p-component.p-filled');

            // Loop through each element and change its color
            elementsEditFont.forEach(element => {
                element.style.color = 'black'; // Change color to red
            });

            // Edit icon
            var elements = document.querySelectorAll('.p-datatable .p-datatable-tbody>tr>td .p-row-toggler, .p-datatable .p-datatable-tbody>tr>td .p-row-editor-init, .p-datatable .p-datatable-tbody>tr>td .p-row-editor-save, .p-datatable .p-datatable-tbody>tr>td .p-row-editor-cancel');
            // Loop through the elements and change their background color
            elements.forEach(function (element) {
                // Change background color
                element.style.color = color1.value;
            });

            // Paginator
            var element7 = document.getElementsByClassName("p-paginator")[0];
            element7.style.background = color2.value;

        };

        /**
         * Get the data from the back-end to poblate the DataTable
         */
        const getData = async () => {
            try {
                loading.value = true;
                const response = await axios.get(API_BASE_URL + "estudiante");
                loadDataToDisplay(response.data);
            } catch (error) {
                console.log("Error: " + error);
                error.value = 'Error fetching data';
            } finally {
                console.log("Finally");
                loading.value = false;
            }
        }

        /**
         * Checks the value of "checkDarkMode" and modify the value of color1 and color2, to match the value of nightMode or Daymode
         */
        watch(checkDarkMode, (newchecked) => {
            if (checkDarkMode.value == true) {
                color1.value = '#ffffff';
                color2.value = '#000000';


            }
            if (checkDarkMode.value == false) {
                color1.value = '#000000';
                color2.value = '#ffffff';

            }
            setclassToDayNightMode();


        });

        /*
        watch(filters.value, (newValue, oldValue) => {
            if (dataForDisplay != null && document.getElementsByClassName('p-row-even').length > 0) {

                if (checkDarkMode.value == true) {
                    overrideStyle('#000000');
                }
                if (checkDarkMode.value == false) {
                    overrideStyle('#ffffff');
                }
            }
            if (checkDarkMode.value == true) {
                overrideStyle('#000000');
            }
            if (checkDarkMode.value == false) {
                overrideStyle('#ffffff');
            }

        });*/

        /**
         * Function to execute when SuccessMessage is closed
         */
        const handleSuccessMessageClose = () => {
            console.log('Message closed');
            showSuccessMessage.value = false;
        };


        onMounted(() => {
            // Get the data for the DataTable
            getData();
            // Initialize the "gif" of the loading text
            updateLoadingText();
            // add a listener to check the screen size
            window.addEventListener('resize', handleResize);
            // Checks the value of darkmode to change the colors of the fonts and background of the DataTable
            if (checkDarkMode.value == true) {
                color1.value = '#ffffff';
                color2.value = '#000000';
                overrideStyle("#000000");

            } else {
                color1.value = '#000000';
                color2.value = '#ffffff';
                overrideStyle("#ffffff");

            }

            setclassToDayNightMode();

            /* // Create a MutationObserver to watch for changes in the DOM
             const observer = new MutationObserver((mutationsList) => {
                 for (const mutation of mutationsList) {
                     if (mutation.type === 'attributes') {
                         const target = mutation.target;
                         // Check if the target has the class you are interested in 
                         if (target.classList.contains('p-column-filter-overlay-menu')) {
 
                             var element = document.getElementsByClassName('p-column-filter-overlay-menu')[0];
                             var currentStyles;
 
                             if (element) {
                                 // Get the current styles
                                 currentStyles = element.getAttribute("style") || "";
 
                                 if (element.style.backgroundColor == "") {
                                     if (checkDarkMode.value == true) {
                                         // Append new styles to the existing ones
                                         var newStyles = currentStyles + "background-color: #000000";
                                         element.setAttribute("style", newStyles);
                                         counter.value = 1;
 
                                     }
                                     if (checkDarkMode.value == false) {
                                         // Append new styles to the existing ones
                                         var newStyles = currentStyles + "background-color: #ffffff";
                                         element.setAttribute("style", newStyles);
                                         counter.value = 1;
                                     }
                                 }
 
                                 counter.value = 1;
 
                             }
                         } else {
                             if (counter.value == 1) {
                                 counter.value = 0;
                                 updateDayNightMode();
                             }
 
 
                         }
                     }
                 }
             });
             // Start observing changes in the DOM
             observer.observe(document.body, { attributes: true, subtree: true });*/

        });

        onUnmounted(() => {
            // Remove the listener
            window.removeEventListener('resize', handleResize);
        });

        const overrideStyle = (color) => {
            // Get all stylesheets
            const styleSheets = document.styleSheets;

            // Iterate through each stylesheet
            for (let i = 0; i < styleSheets.length; i++) {
                const styleSheet = styleSheets[i];

                // Iterate through each rule in the stylesheet
                for (let j = 0; j < styleSheet.cssRules.length; j++) {
                    const rule = styleSheet.cssRules[j];

                    // Check if the rule is the body of the DataTable
                    if (rule.selectorText === '.p-datatable .p-datatable-tbody > tr') {
                        // Modify the rule's properties
                        rule.style.backgroundColor = color;
                        break; // Once found, exit the loop
                    }
                }
            }
            // Get the <tbody> element using getElementsByClassName
            var tbodyElements = document.getElementsByClassName('p-datatable-tbody');

            // Loop through the <tbody> elements and do something with each one
            for (var i = 0; i < tbodyElements.length; i++) {
                var tbodyElement = tbodyElements[i];
                // Use querySelectorAll to get <tr> elements inside each <tbody>
                var trElements = tbodyElement.querySelectorAll('tr');
                // Loop through the <tr> elements and do something with each one
                trElements.forEach(function (trElement) {
                    // Example: Change background color of each <tr> (text of each row)
                    trElement.setAttribute("style", "background-color: " + color2.value + " !important; color: " + color1.value + " !important;");

                });
            }
            //Edit icon
            var elements = document.querySelectorAll('.p-datatable .p-datatable-tbody>tr>td .p-row-toggler, .p-datatable .p-datatable-tbody>tr>td .p-row-editor-init, .p-datatable .p-datatable-tbody>tr>td .p-row-editor-save, .p-datatable .p-datatable-tbody>tr>td .p-row-editor-cancel');
            // Loop through the elements and change their background color
            elements.forEach(function (element) {
                // Change background color
                element.style.color = color1.value;
            });
        }
        /** When the DataTable dectect changes of filter, pagination, or order */
        const handlePageChange = (event) => {
            updateLoadingText();
            setclassToDayNightMode();
        }


        return {
            filters,
            loading,
            checkDarkMode,
            formatDate,
            clearFilter,
            onRowEditSave,
            editingRows,
            isDesktop,
            isTablet,
            error,
            loadDataToDisplay,
            dataForDisplay,
            loadingTexts,
            loadingIndex,
            loadingText,
            checkDarkMode,
            color,
            setclassToDayNightMode,
            color1,
            color2,
            overrideStyle,
            counter,
            updateDayNightMode,
            getData,
            handlePageChange,
            showErrorMessage,
            showSuccessMessage,
            errorOrSuccesMessage,
            validarRut,
            handleSuccessMessageClose
        };
    },
}

</script>


<style>
/* For phones */
@media screen and (max-width: 767px) {
    .SearchBar {
        /* Make the SearchBar 50% of the container's width */
        width: 50%;
    }
}

/* For tablets */
@media screen and (min-width: 768px) and (max-width: 1024px) {
    .SearchBar {
        /* Make the SearchBar 50% of the container's width */
        width: 70%;
    }
}

.dataTableTOP {
    display: flex;
    justify-content: space-between;

}

.right {
    display: flex;
    align-items: center;
    text-align-last: end;
    /* Vertically center the content */
}

.right-aligned {
    margin-right: auto;
    /* Push the SearchBar to the right */
}

.surface-ground {
    background-color: var(--secundary-color);
}

.dark-lighter-mode {
    background-color: #1c1b1b;
    color: #fff;
}
</style>