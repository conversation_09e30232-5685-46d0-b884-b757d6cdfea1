import { defineStore } from "pinia";

export const useThemeStore = defineStore("themeStore", {
  state: () => ({
    currentTheme: localStorage.getItem("theme") || "aura-light-indigo", // Default to 'aura-light-indigo'
    inverseTheme: localStorage.getItem("inverseTheme") || "aura-dark-indigo",
  }),
  getters: {
    // Getter to retrieve the current theme
    getCurrentTheme: (state) => state.currentTheme,

    // Getter to retrieve the inverse theme
    getInverseTheme: (state) => state.inverseTheme,
  },
  actions: {
    async setTheme(newTheme, previousTheme) {
      const oldTheme = this.currentTheme; // Previous theme
      this.currentTheme = newTheme || "aura-light-indigo";
      this.inverseTheme = previousTheme || "aura-dark-indigo";
      // Persist theme to localStorage
      localStorage.setItem("theme", this.currentTheme);
      localStorage.setItem("inverseTheme", this.inverseTheme);
    },
    loadInitialTheme() {
      this.setTheme(this.currentTheme); // Set the default theme when loading
    },
  },
  persist: {
    key: "theme",
    storage: localStorage, // Or sessionStorage for session persistence
    paths: ["currentTheme", "inverseTheme"], // Specify which state to persist
  },
});
