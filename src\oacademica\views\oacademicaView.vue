<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { FilterMatchMode, FilterOperator } from 'primevue/api';
import Toolbar from 'primevue/toolbar';
import useOAcademicas from '../composables/useOAcademicas';
import useOAcademicaEtapas from '../composables/useOAcademicaEtapas';
import { useRouter } from "vue-router";
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
import Button from 'primevue/button';
import Dropdown from 'primevue/dropdown';
import type { Etapa } from '../interfaces/Etapa';
const { oacademicas, isLoading,  getCsvOacademicas, setEtapaId } = useOAcademicas();
const { etapasQuery, etapaActualQuery, setYear } = useOAcademicaEtapas();
const router = useRouter();

const col_global_fil: string[] = [];

const columns = [
    {
        field: 'codigo_unico',
        header: 'Codigo Unico'
    },
    {

        field: 'nombre_carrera',
        header: 'Nombre Carrera'
    },


];
const rowsPerPage = [5, 10, 20, 50];

const filters = ref();
const checkDarkMode = false;
const editingRows = ref([]);

const screenWidth = ref(window.innerWidth);
const isDesktop = computed(() => screenWidth.value >= 1024);
const isTablet = computed(() => screenWidth.value >= 768 && screenWidth.value < 1024);
const loadingTexts = ['cargando', 'cargando.', 'cargando..', 'cargando...'];

const loadingText = ref(loadingTexts[0]);
const showDialogHistorial = ref(false);
const filterYear = ref('');
const filterEtapas = ref<Etapa>();
const nombreEtapa = ref('');
watch(etapaActualQuery.data, (newValue) => {
    if (newValue) {
        nombreEtapa.value = newValue.nombre;
    }
});

watch(filterEtapas, (newValue) => {
    if (newValue) {
        console.log('etapa seleccionada : ' + newValue.id);
        setEtapaId(newValue.id);
        showDialogHistorial.value = false;
        nombreEtapa.value = newValue.nombre;
    }
});

// PrimeVue format for filters used in the DataTable
const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        codigo_unico: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }] },
        codigo_ua: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }] },
        nombre_carrera: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }] },
        tipo_carrera: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }] },
        codigo_carrera: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }] },
    };
};
initFilters();
/**
 * Change the format of the date column, to day/month/year for better visibility
 * @param value date of the "estudiante" model
 */
const formatDate = (value: string) => {
    const dateObject1 = new Date(value);
    return dateObject1.toLocaleDateString('es', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
};

const goToDetails = (oa_sies_id: any) => {
    router.push({ name: 'oacademicaFormView', params: { oa_sies_id: oa_sies_id, etapa: etapaActualQuery.data.value?.etapa} });
};

const goToNew = () => {
    router.push({ name: 'oacademicaCreateFormView' });
};


const downloadCsv = async (): Promise<void> => {
    const blob: Blob = await getCsvOacademicas(); // El archivo CSV en formato Blob
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'data.csv'; // Nombre del archivo a descargar
    link.click();
    URL.revokeObjectURL(url);
}

const limpiaFiltroHistorico = () => {
    filterYear.value = '';
    filterEtapas.value = undefined;
    setYear('');
    setEtapaId('');
    initFilters();
    nombreEtapa.value = etapaActualQuery.data.value?.nombre || 'Oferta Académica';
}
watch(showDialogHistorial, (newValue) => {
    if (!newValue) {
        console.log('cerrando dialogo');
        ;
    }
    else {
        console.log('abriendo dialogo');
    }
});
</script>

<template>
    <div>
        <div
            :class="{ 'dark-lighter-mode px-4 py-5 md:px-6 lg:px-8': checkDarkMode, 'surface-ground px-4 py-5 md:px-6 lg:px-8': !checkDarkMode }">
            <Dialog header="Filtrar Datos" v-model:visible="showDialogHistorial" :modal="true" :closable="true">
                <div class="flex align-items-center gap-3 mb-5">
                    <label for="year" class="font-semibold">Año</label>
                    <InputText id="year" class="flex-auto" v-model="filterYear" />
                    <Button icon="pi pi-search" severity="success" text raised rounded aria-label="Search"
                        @click="setYear(filterYear)" />
                </div>
                <div class="flex justify-content-center gap-3 mb-5 w-full">
                    <span v-if="etapasQuery.data.value">
                        <Dropdown id="etapas" v-model="filterEtapas" :options="etapasQuery.data.value"
                            optionLabel="nombre" placeholder="Seleccione una etapa" />
                    </span>
                    <ProgressSpinner v-if="etapasQuery.isLoading.value" style="width: 40px; height: 40px;"
                        aria-label="Loading" />
                </div>

            </Dialog>
            <Toolbar class="mb-4">
                <template #center>
                    <div class="year-input-container">
                        <Button label="Historial" icon="pi pi-clock" severity="info"
                            @click="showDialogHistorial = true" />
                        <Button v-if="filterEtapas" icon="pi pi-times" severity="secondary" text rounded
                            aria-label="Cancel" @click="limpiaFiltroHistorico" />

                    </div>
                </template>
                <template #start>
                    <Button label="Nuevo" icon="pi pi-plus" severity="success" class="mr-2" @click="goToNew" />
                </template>
                <template #end>

                    <Button label="Exportar" icon="pi pi-upload" severity="help" @click="downloadCsv" />
                </template>
            </Toolbar>
            <DataTable id="tableHeader"
                :class="{ 'dataTableClass shadow-4': checkDarkMode, 'shadow-4': !checkDarkMode }"
                v-model:filters="filters" :value="oacademicas" v-model:editingRows="editingRows" editMode="row"
                sortMode="multiple" removableSort paginator showGridlines :rows="10" :rowsPerPageOptions="rowsPerPage"
                dataKey="Id" filterDisplay="menu" :loading="isLoading" :globalFilterFields="col_global_fil" :pt="{
                    table: { style: 'min-width: 50rem' }
                }">

                <!-- Button clear and search bar -->
                <template #header>
                    <!-- <Message v-if=isError severity="error">{{ error }}
                    </Message>
                    <Message v-if=isSuccess severity="success">{{ status }}</Message> -->
                    <h2 class="h2" >{{ nombreEtapa}} </h2>

                    <div v-if="isDesktop" class="dataTableTOP">
                        <div class="left">
                            <Button type="button" class="shadow-3" icon="pi pi-filter-slash" label="Limpiar" outlined
                                @click="initFilters()"></Button>
                        </div>
                        <div class="right">
                            <IconField>
                                <InputIcon>
                                    <i class="pi pi-search"></i>
                                </InputIcon>
                                <InputText class="SearchBar" v-model="filters['global'].value" placeholder="Buscar">
                                </InputText>

                            </IconField>
                        </div>
                    </div>
                    <div v-else-if="isTablet" class="dataTableTOP">
                        <div class="left">
                            <Button type="button" class="shadow-3" icon="pi pi-filter-slash" label="Limpiar" outlined
                                @click="initFilters()"></Button>
                        </div>
                        <div class="right">
                            <IconField>
                                <InputIcon>
                                    <i class="pi pi-search"></i>
                                </InputIcon>
                                <InputText class="SearchBar" v-model="filters['global'].value" placeholder="Buscar">
                                </InputText>

                            </IconField>
                        </div>
                    </div>
                    <div v-else class="dataTableTOP">
                        <div class="left">
                            <Button type="button" class="shadow-3" icon="pi pi-filter-slash" label="" outlined
                                @click="initFilters()"></Button>
                        </div>
                        <div class="right">
                            <IconField>
                                <InputIcon>
                                    <i class="pi pi-search"></i>
                                </InputIcon>
                                <InputText class="SearchBar" v-model="filters['global'].value" placeholder="">
                                </InputText>

                            </IconField>
                        </div>
                    </div>
                </template>
                <!-- No data found and loading -->
                <template #empty> No se encontró información. </template>
                <template #loading>
                    <div class="form-row">
                        <div class="form-group">
                            <i class="pi pi-spin pi-cog" style="font-size: 6rem"></i>
                            <br /><br />
                            <div><label class="labelLoading">{{ loadingText }}</label></div>
                        </div>
                    </div>
                </template>
                <Column v-for="col of columns" :key="col.field" :field="col.field" :header="col.header" sortable
                    style="min-width: 12rem">
                    <template #editor="{ data, field }">
                        <InputText v-model="data[field]" />
                    </template>
                    <template #body="{ data, field }">
                        {{ data[field] }}
                    </template>
                    <template #filter="{ filterModel }">
                        <InputText v-model="filterModel.value" type="text" class="p-column-filter"
                            placeholder="Buscar por {{ col.header }}" />
                    </template>
                </Column>

                <Column header="">
                    <template #body="slotProps">
                        <!-- Aquí puedes poner contenido dinámico basado en slotProps.data -->
                        <!--                        <router-link :to="{ name: 'oacademicaFormView', params: { oa_sies_id: slotProps.data['oa_sies_id'] } }">
                            Ir a ver
                        </router-link>-->
                        <Button icon="pi pi-eye" style="justify-content: center;" class="p-button-text"
                            @click="goToDetails(slotProps.data['oa_sies_id'])" />
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>


<style>
/* For phones */
@media screen and (max-width: 767px) {
    .SearchBar {
        /* Make the SearchBar 50% of the container's width */
        width: 50%;
    }
}

/* For tablets */
@media screen and (min-width: 768px) and (max-width: 1024px) {
    .SearchBar {
        /* Make the SearchBar 50% of the container's width */
        width: 70%;
    }
}

.dataTableTOP {
    display: flex;
    justify-content: space-between;

}

.right {
    display: flex;
    align-items: center;
    text-align-last: end;
    /* Vertically center the content */
}

.right-aligned {
    margin-right: auto;
    /* Push the SearchBar to the right */
}

.surface-ground {
    background-color: var(--secundary-color);
}

.dark-lighter-mode {
    background-color: #1c1b1b;
    color: #fff;
}

.p-datatable .p-datatable-tbody>tr {
    background-color: #ffffff;
}
</style>