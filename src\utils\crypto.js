// src/utils/crypto.js
import CryptoJ<PERSON> from 'crypto-js';

const secretKey = import.meta.env.VITE_CRIPTO_JS_SECRET_KEY; // Use a strong, secure key

export function encryptData(data) {
    return CryptoJS.AES.encrypt(JSON.stringify(data), secretKey).toString();
}

export function decryptData(ciphertext) {
    const bytes = CryptoJS.AES.decrypt(ciphertext, secretKey);
    return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
}