<template>
    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading && oa_data">
            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <div class='surface-card shadow-3 p-3 border-round'>
                    <!--Titulo-->
                    <div style="display: flex;align-items: center;padding-left: 1rem;">

                        <Button @click.prevent="goBack()"> <- </Button>
                                <h2 style="padding-left: 1rem;">Proceso N°1 Oferta Academica Acceso{{
                                    anio_proceso }}
                                </h2>
                    </div>
                    <br />

                    <!--Etapa 1-->
                    <div style="margin-left: 1rem;margin-right: 1rem;">
                        <div style="display: flex;justify-content: space-between;align-items: center;">
                            <h3>Etapa N°1</h3>
                            <Button v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                                @click="dialogConfirmarMU_post = true, currentEtapaForDialog = 1">Validar</Button>
                        </div>
                        <DataTable sortField="createdAt" :sortOrder="-1" showGridlines scrollable scrollHeight="250px"
                            :value="oa_data" style="width: 100%;">
                            <template #empty>
                                <div style="text-align: center;"> No se encontraron datos </div>

                            </template>
                            <Column style="width: 30%" header="Nombre Carrera" field="nombre_carrera"
                                class="center-header">
                            </Column>
                            <!-- <Column field="is_finalized" header="Estado" class="center-header" style="width: 5%;">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.etapa_actual == 1" v-tooltip.bottom="pending_text">
                                        <Button disabled icon="pi pi-hourglass"
                                            style="justify-content: center;color: orangered;" class="p-button-text" />
                                    </div>
                                    <div v-else v-tooltip.bottom="finalized_text">
                                        <Button disabled icon="pi pi-verified"
                                            style="justify-content: center;color: green"
                                            class="p-button-text green-icon" />
                                    </div>
                                </template>
                            </Column> -->
                            <!-- <Column style="width: 5%" header="Fecha límite" field="fecha_limite" class="center-header">
                                <template #body="slotProps">

                                    <div v-if="slotProps.data.fechas && slotProps.data.fechas.length > 0"
                                        v-tooltip.bottom="{ value: tooltipTextEtapa1, pt: { root: { style: 'white-space: nowrap; max-width: none;' } } }">

                                        <Button disabled icon="pi pi-info-circle"
                                            style="justify-content: center;color: orangered;" class="p-button-text" />
                                    </div>
                                </template>
                            </Column> -->
                            <Column style="width: 10%" header="Código Carrera" field="cod_carrera" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.cod_carrera">
                                        {{ slotProps.data.cod_carrera }}
                                    </div>
                                    <div v-else>
                                        Sin Datos
                                    </div >
                                </template>
                            </Column>

                            <Column style="width: 10%;" header="Acreditación" field="acreditacion"
                                class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.acreditacion">
                                        {{ slotProps.data.acreditacion }}
                                    </div>
                                    <div v-else>
                                        Sin Datos
                                    </div>
                                </template>
                            </Column>
                            <Column style="width: 10%;" header="Cod Jornada" field="cod_jornada" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.cod_jornada">
                                        {{ slotProps.data.cod_jornada }}
                                    </div>
                                    <div v-else>
                                        Sin Datos
                                    </div>
                                </template>
                            </Column>

                            <Column style="width: 10%;" header="Versión" field="version" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.version">
                                        {{ slotProps.data.version }}
                                    </div>
                                    <div v-else>
                                        Sin Datos
                                    </div>
                                </template>
                            </Column>

                            <Column style="width: 10%;" header="Cod Nivel Global" field="cod_nivel_global" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.cod_nivel_global">
                                        {{ slotProps.data.cod_nivel_global }}
                                    </div>
                                    <div v-else>
                                        Sin Datos
                                    </div>
                                </template>
                            </Column>

                            <Column style="width: 10%;" header="Vigencia Carrera" field="vigencia_carrera" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.vigencia_carrera">
                                        {{ slotProps.data.vigencia_carrera }}
                                    </div>
                                    <div v-else>
                                        Sin Datos
                                    </div>
                                </template>
                            </Column>
                            <!-- <Column style="width: 5%;" header="Fecha validación" field="fecha_validacion_1"
                                class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.fecha_validacion_1">
                                        {{ slotProps.data.fecha_validacion_1 }}
                                    </div>
                                    <div v-else>
                                        Sin Datos
                                    </div>
                                </template>
                            </Column> -->

                            <!-- <Column style="width: 5%;" v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                                field="Cargar Datos" header="Cargar Datos" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.etapa_actual == 1">
                                        <Button icon="pi pi-upload" style="" class="p-button-text"
                                            @click="dialogCargarDatosisVisible = true, currentEtapaForDialog = 1" />
                                    </div>
                                    <div v-else>
                                        Carga finalizada
                                    </div>
                                </template>
                            </Column> -->
                            <!-- <Column style="width: 5%" v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                                field="Exportar" header="Exportar" class="center-header">
                                <template #body="slotProps">
                                    <div v-if="slotProps.data.is_finalized == true">
                                        <Button icon="pi pi-download" style="" class="p-button-text" @click="" />
                                    </div>
                                    <div v-else>
                                        Sin Datos
                                    </div>
                                </template>
                            </Column>
                            <Column style="width: 5%;" v-if="hasAccess('Administrador Sistema', 'Administracion PIU')"
                                field="eliminar" header="Eliminar" class="center-header">
                                <template #body="slotProps">
                                    <div>
                                        <Button icon="pi pi-trash" class="p-button-text"
                                            @click="dialogVisibleEliminar = true, currentEtapaForDialog = 1" />
                                    </div>
                                </template>
                            </Column> -->
                        </DataTable>
                    </div>
                    <br />
                    <!--Etapa 2-->
                    <div v-if="etapa_actual === 2" style="margin-left: 1rem;margin-right: 1rem;">
                        <div style="display: flex;justify-content: space-between;align-items: center;">
                            <h3>Etapa N°2</h3>

                        </div>

                    </div>
                </div>
            </div>

            <Dialog v-model:visible="dialogVisibleEliminar" modal header="Eliminar inmueble" :style="{ width: '25rem' }"
                v-on:hide="resetForm()">
                <div v-if="dialogIsLoading && !muPregradoisCorrect">
                    <Loading />
                </div>

                <div v-if="!dialogIsLoading && !muPregradoisCorrect">
                    <div>
                        <!-- Validation error message -->
                        <p>Esto eliminará permanentemente este registro ¿Está seguro?</p>
                    </div>
                    <br />
                    <div class="flex justify-content-center gap-2">
                        <InputSwitch :disabled="dialogHasError" style="scale: 1.5;" v-model="deletePreConfirmation">
                        </InputSwitch>
                    </div>
                    <br />
                    <!-- Validation error message -->
                    <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}<br /></div>


                    <!-- Buttons -->
                    <div class="flex justify-content-end gap-2">
                        <Button type="button" label="Cancelar" severity="secondary"
                            @click="dialogVisibleEliminar = false"></Button>
                        <Button :disabled="dialogHasError" v-if="deletePreConfirmation" type="button" label="Eliminar"
                            @click="eliminarInmueble()"></Button>
                    </div>
                </div>

                <!-- Success message -->
                <div v-if="!dialogIsLoading && muPregradoisCorrect"
                    style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
                    <i class="pi pi-trash" style="font-size: 5rem; color: blue;"></i>
                    <br />
                    <span class="p-text-secondary block mb-5">¡Eliminado correctamente!</span>
                </div>

            </Dialog>
            <Dialog v-model:visible="dialogConfirmarMU_post" modal header="Finalizar Etapa de Matrícula Unificada"
                :style="{ width: '25rem' }" v-on:hide="resetForm()">
                <div v-if="dialogIsLoading && !muPregradoisCorrect">
                    <Loading />
                </div>

                <div v-if="!dialogIsLoading && !muPregradoisCorrect">
                    <div>
                        <p class="block mb-3">Al validar el registro este no se podrá editar ni eliminar ¿Está
                            seguro?
                        </p>
                        <p class="p-error block mb-3">Esta acción es IRREVERSIBLE.
                        </p>
                    </div>
                    <br />
                    <div class="flex justify-content-center gap-2">
                        <InputSwitch style="scale: 1.5;" v-model="validatePreConfirmation"></InputSwitch>
                    </div>
                    <br />
                    <div>
                        <!-- Validation error message -->
                        <div v-if="errorMessage" class="p-error block mb-3">{{ errorMessage }}</div>

                        <br />
                    </div>
                    <!-- Buttons -->
                    <div class="flex justify-content-end gap-2">
                        <Button type="button" label="Cancelar" severity="secondary"
                            @click="dialogConfirmarMU = false"></Button>
                        <Button v-if="validatePreConfirmation" type="button" label="Confirmar"
                            @click="validarMatriculaUnificada()"></Button>
                    </div>
                </div>

                <!-- Success message -->
                <div v-if="!dialogIsLoading && muPregradoisCorrect"
                    style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
                    <i class="pi pi-check" style="font-size: 5rem; color: blue;"></i>
                    <br />
                    <span class="p-text-secondary block mb-5">¡Registro validado correctamente!</span>
                </div>

            </Dialog>

            <Dialog v-model:visible="dialogCargarDatosisVisible" modal header="Cargar datos de estudiantes"
                :style="{ width: '25rem' }" v-on:hide="resetForm()">
                <!--todo: implement dialogCARGAR-->

                <div v-show="!cargarDatosIsLoading" class="card">
                    <FileUpload ref="fileUploadRef" name="estudianteData" uploadLabel="Cargar"
                        v-on:select="onSelect($event)" v-on:before-upload="onUpload()" :multiple="false" accept=".csv"
                        :maxFileSize="1000000" :fileLimit=1>
                        <template #empty>
                            <p>Arrastra y suelta un archivo aquí para subirlo.</p>
                        </template>
                    </FileUpload>
                </div>
                <div v-if="cargarDatosIsLoading">
                    <Loading />
                </div>

            </Dialog>

        </div>
        <div v-else class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
                <Loading />
            </div>
        </div>
        <Toast position="bottom-left" group="bl" />
    </div>



</template>

<script>
// import { useAuthStore } from '../../../store/auth';
import { useAuthStore } from '../../store/auth';
import { ref, computed, onMounted } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import axios from 'axios';
import Papa from 'papaparse';
import { useToast } from 'primevue/usetoast';
import { useRoute, useRouter } from 'vue-router';

export default {

    setup() {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const route = useRoute();
        const router = useRouter();
        const authStore = useAuthStore();
        const toast = useToast();
        const isLoading = ref(false);
        const oa_data = ref([]);
        const dialogVisibleEliminar = ref(false);
        const dialogIsLoading = ref(false);
        const finalized_text = ref("Finalizado");
        const pending_text = ref("Pendiente");

        // Obtain the stored user data from pinia and the global loading state
        const userName = computed(() => decryptData(authStore.userName));
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const permissionsList = computed(() => decryptData(authStore.permissionsList));
        const isUserAuthenticated = computed(() => decryptData(authStore.userIsAuthenticated));
        const globalLoading = computed(() => decryptData(authStore.isLoading));
        const userToken = computed(() => decryptData(authStore.idToken));
        const anio_proceso = 2024; // Access the params
        const etapa_actual = decryptData(route.params.etapa); 
        const proceso_sies_id = decryptData(route.params.proceso_sies_id);
        const muPregradoisCorrect = ref(false);
        const errorMessage = ref('');
        const deletePreConfirmation = ref(false);
        const dialogConfirmarMU_post = ref(false);
        const validatePreConfirmation = ref(false);
        const muPregradoAnioSelecionado = ref({});

        //Dialog cargar datos
        const dialogCargarDatosisVisible = ref(false);
        const cargarDatosIsLoading = ref(false);
        const fileUploadRef = ref(null);
        const parsedData = ref([]);
        const currentEtapaForDialog = ref(null); // Etapa seleccionada (varia segun etapa) para la ventana de dialogo

        //tooltip with the dates of each etapa
        const tooltipTextEtapa1 = ref();
        const tooltipTextEtapa2 = ref();
        const tooltipTextEtapa3 = ref();
        const tooltipTextEtapa4 = ref();
        const tooltipTextEtapa5 = ref();

        const hasAccess = (allowedRol, requiredRecurso) => {
            const access = permissionsList.value.some(user => user.rol === allowedRol && user.recurso === requiredRecurso);
            if (access) {
                return true;
            } else {
                return false;
            }

        };
        const goBack = () => {
            router.push({ path: "/PP-MU-pregrado" });
        };
        onMounted(async () => {
            isLoading.value = true;
            await getOfertaAcademicaData();
            console.log(oa_data.value)
            isLoading.value = false;
        });

        const getOfertaAcademicaData = async () => {
            try {
                isLoading.value = true;

                const response = await axios.get(API_BASE_URL + `oacademica/procesosies/${proceso_sies_id}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });

                if (response.status == 200) {
                    /**
                     * Updates the `oa_data` reactive variable with the response data.
                     * If the response data is an array, it assigns it directly to `oa_data`.
                     * If the response data is not an array, it wraps it in an array before assignment.
                     * This ensures that `oa_data` is always an array, regardless of the response format.
                     */
                    oa_data.value = Array.isArray(response.data) ? response.data : [response.data];
                    console.log(oa_data);

                    // const etapasData = response.data.fechas;
                    // if (etapasData) {
                    //     const etapa1Data = etapasData.find(fecha => fecha.etapa_proceso === 1);
                    //     if (etapa1Data) {
                    //         tooltipTextEtapa1.value = `${etapa1Data.fecha_inicio} al ${etapa1Data.fecha_termino}`;
                    //     }
                    //     const etapa2Data = etapasData.find(fecha => fecha.etapa_proceso === 2);
                    //     if (etapa2Data) {
                    //         tooltipTextEtapa2.value = `${etapa2Data.fecha_inicio} al ${etapa2Data.fecha_termino}`;
                    //     }
                    //     const etapa3Data = etapasData.find(fecha => fecha.etapa_proceso === 3);
                    //     if (etapa3Data) {
                    //         tooltipTextEtapa3.value = `${etapa3Data.fecha_inicio} al ${etapa3Data.fecha_termino}`;
                    //     }
                    //     const etapa4Data = etapasData.find(fecha => fecha.etapa_proceso === 4);
                    //     if (etapa4Data) {
                    //         tooltipTextEtapa4.value = `${etapa4Data.fecha_inicio} al ${etapa4Data.fecha_termino}`;
                    //     }
                    //     const etapa5Data = etapasData.find(fecha => fecha.etapa_proceso === 5);
                    //     if (etapa5Data) {
                    //         tooltipTextEtapa5.value = `${etapa5Data.fecha_inicio} al ${etapa5Data.fecha_termino}`;
                    //     }
                    // }
                    // console.log(tooltipTextEtapa1.value);
                    // console.log(tooltipTextEtapa2.value);
                    // console.log(tooltipTextEtapa3.value);
                    // console.log(tooltipTextEtapa4.value);
                    // console.log(tooltipTextEtapa5.value);
                }
            } catch (error) {
                console.error("Error: ", error);
            } finally {
                isLoading.value = false;
            }
        };

        // Function to reset the form for a new creation
        const resetForm = () => {
            errorMessage.value = ''; // Clear any error messages
            muPregradoisCorrect.value = false; // Reset success state
            deletePreConfirmation.value = false;
            dialogVisibleEliminar.value = false;
            currentEtapaForDialog.value = null; // Reset currentEtapa to null
        }

        const validarMatriculaUnificada = async () => {
            try {
                dialogIsLoading.value = true;

                const response = await axios.get(API_BASE_URL + `mu_pregrado/${anio_proceso}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                muPregradoAnioSelecionado.value = await response.data;
                if (muPregradoAnioSelecionado.value.is_finalized == true) {
                    toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El registro no puede ser modificado', life: 5000 });
                } else {
                    console.log(muPregradoAnioSelecionado.value.estudiante_count);
                    if (

                        muPregradoAnioSelecionado.value.estudiante_count >= 0
                    ) {
                        try {
                            const body = ref({});
                            body.value.anio_proceso = muPregradoAnioSelecionado.value.anio_proceso;
                            const response2 = await axios.patch(API_BASE_URL + `mu_pregrado/${anio_proceso}`, body.value, {
                                headers: {
                                    Authorization: `Bearer ${userToken.value}`
                                }
                            });
                            // Check if the response is successful (status code 200)                            
                            if (response2.status == 201) {
                                muPregradoisCorrect.value = true;
                                isLoading.value = true;
                                await getOfertaAcademicaData();
                                isLoading.value = false;
                            }
                        } catch (error) {
                            resetForm();
                            toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Se detectó un error, inténtelo más tarde.', life: 5000 });

                        }

                    } else {
                        // La infraestructura recurso esta vacia (sin inmuebles)
                        resetForm();
                        toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'La infraestructura debe tener inmuebles asociados.', life: 5000 });
                    }
                }

            }
            catch (error) {
                console.error("Error fetching data:", error);
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Ocurrió un error, inténtalo más tarde', life: 5000 });
            }
            finally {
                dialogIsLoading.value = false;
            }
        };

        const onSelect = async (event) => {
            try {
                cargarDatosIsLoading.value = true;

                const file = event.files[0]; // Access the first file from the Proxy array
                const reader = new FileReader();

                reader.onload = async (e) => {
                    const fileContent = e.target.result;

                    // Parse the CSV content using PapaParse
                    Papa.parse(fileContent, {
                        header: true,
                        skipEmptyLines: true,
                        complete: async (results) => {
                            try {
                                parsedData.value = results.data;
                                // Check for empty fields in the parsed data
                                const hasEmptyFields = parsedData.value.some(row =>
                                    Object.values(row).some(value =>
                                        value === "" || value === null || value === undefined ||
                                        (typeof value === "string" && value.trim() === "")
                                    )
                                );

                                if (hasEmptyFields) {
                                    // Handle rows with empty fields
                                    console.log("The file contains rows with empty fields.");
                                    // Clear the file from the upload list
                                    fileUploadRef.value.clear();
                                    toast.add({
                                        severity: 'error',
                                        summary: 'Error',
                                        group: 'bl',
                                        detail: 'El archivo contiene campos vacíos. Por favor, revise y vuelva a intentarlo.',
                                        life: 5000
                                    });
                                } else {
                                    // Proceed with further processing
                                    console.log("The file has no empty fields.");
                                    console.log("Parsed Data:", parsedData.value);
                                    toast.add({
                                        severity: 'success',
                                        summary: 'Éxito',
                                        group: 'bl',
                                        detail: 'El archivo tiene el formato correcto.',
                                        life: 5000
                                    });
                                }

                            } catch (error) {
                                console.error("Error uploading data:", error);
                                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Ocurrió un error al cargar los datos.', life: 5000 });
                            }
                        },
                        error: (error) => {
                            console.error("Error parsing CSV:", error);
                            toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Ocurrió un error al procesar el archivo.', life: 5000 });
                        },
                    });
                };
            } catch (error) {
                cargarDatosIsLoading.value = false;
                console.error("Error handling file upload:", error);
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Ocurrió un error al procesar el archivo.', life: 5000 });
            } finally {
                cargarDatosIsLoading.value = false;
            }

        };
        const onUpload = async () => {
            try {
                cargarDatosIsLoading.value = true;
                console.log("Uploading data:", parsedData.value);
                parsedData.value,
                    console.log(anio_proceso);
                console.log(currentEtapaForDialog.value);
                console.log(API_BASE_URL)
                console.log(parsedData.value)
                const response = await axios.post(
                    `${API_BASE_URL}mu_pregrado/cargarCSV/${anio_proceso}/${currentEtapaForDialog.value}`,
                    parsedData.value,
                    {
                        headers: {
                            Authorization: `Bearer ${userToken.value}`,
                            "Content-Type": "application/json",
                        },
                    }
                );
                if (response.status === 201) {
                    toast.add({
                        severity: "success",
                        summary: "Éxito",
                        group: "bl",
                        detail: "Datos cargados correctamente.",
                        life: 5000,
                    });
                    dialogCargarDatosisVisible.value = false;
                    await getOfertaAcademicaData();
                } else {
                    toast.add({
                        severity: "error",
                        summary: "Error",
                        group: "bl",
                        detail: "Ocurrió un error al cargar los datos.",
                        life: 5000,
                    });
                }
            } catch (error) {
                console.error("Error handling file upload:", error);
            }
        }


        return {
            isLoading,
            globalLoading,
            anio_proceso,
            etapa_actual,
            hasAccess,
            goBack,
            oa_data,
            dialogVisibleEliminar,
            dialogIsLoading,
            deletePreConfirmation,
            muPregradoisCorrect,
            resetForm,
            validarMatriculaUnificada,
            dialogConfirmarMU_post,
            dialogIsLoading,
            validatePreConfirmation,
            errorMessage,
            pending_text,
            finalized_text,
            tooltipTextEtapa1,
            tooltipTextEtapa2,
            tooltipTextEtapa3,
            tooltipTextEtapa4,
            tooltipTextEtapa5,
            dialogCargarDatosisVisible,
            cargarDatosIsLoading,
            onSelect,
            fileUploadRef,
            onUpload,
            currentEtapaForDialog
        }
    }
}

</script>
<style scoped>
:deep() .center-header {
    text-align: center !important;
    align-content: center !important;
}

:deep() .p-datatable .p-column-header-content {
    display: block !important;

}

.yellow-icon .pi {
    color: yellow;
}

.green-icon .pi {
    color: greenyellow;
}

:deep(.p-fileupload-file-thumbnail) {
    display: none !important;
}
</style>