<template>
    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading">
            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <div class='surface-card shadow-3 p-3 border-round'>
                    <!--Titulo-->
                    <div style="display: flex;align-items: center;padding-left: 1rem;">
                        <h2 style="padding-left: 1rem;">Ficha CNA</h2>
                    </div>
                    <!-- Dropdown menu using dataFichaGeneral -->
                    <div style="padding-left: 3rem;">
                        <h3 style="padding-left: 1rem;">Seleccione una ficha: </h3>
                        <Dropdown v-if="dataFichaGeneral && Array.isArray(dataFichaGeneral) && dataFichaGeneral.length"
                            v-model="selectedFicha" :options="dataFichaGeneral"
                            :optionLabel="ficha => `${ficha.tipo_ficha} - ${ficha.anio_ficha} - ${ficha.version}`"
                            placeholder="Seleccione una ficha" style="margin-left: 2rem;" :filter="true"
                            :showClear="true" />
                        <Button v-if="selectedFicha && !isLoading" @click="getDimension()"
                            class="p-button p-component p-button-outlined" style="margin-left: 2rem;">
                            Obtener dimensiones
                        </Button>
                        <div v-if="dataDimension">
                            <h3 style="padding-left: 1rem;">Seleccione una dimensión </h3>
                            <Dropdown v-if="dataDimension && Array.isArray(dataDimension) && dataDimension.length"
                                v-model="selectedDimension" :options="dataDimension"
                                :optionLabel="dimension => `${dimension.nombre_dimension}`"
                                placeholder="Seleccione una dimensión" style="margin-left: 2rem;" :filter="true"
                                :showClear="true" />
                            <Button v-if="selectedDimension && !isLoading" @click="obtenerDatosHeaderTipos"
                                class="p-button p-component p-button-outlined" style="margin-left: 2rem;">
                                Obtener datos
                            </Button>
                            <!-- Display the data for the selected dimension -->
                            <div v-if="selectedDimension && generarFicha">
                                <div v-for="(item, index) in dataDimension" :key="index">
                                    <div v-if="item.Titulos && Array.isArray(item.Titulos)">
                                        <div v-for="(titulo, tIndex) in item.Titulos" :key="tIndex">
                                            <h3>{{ titulo.nombre_titulo }}</h3>
                                            <!-- Check tipo_contenedor and display accordingly -->

                                            <div v-if="titulo.tipo_contenedor === 'matriz tipo 1'">
                                                <DataTable showGridlines stripedRows
                                                    v-if="dataFichaAll && Array.isArray(dataFichaAll.listaHeadersTipo)"
                                                    :value="(() => {
                                                        const matriz = dataFichaAll.listaHeadersTipo.find(x => x.tipo_contenedor === 'matriz tipo 1')?.matriz || [];
                                                        return matriz.slice(1); // skip header row
                                                    })()" class="p-datatable-sm" :rows="10" responsiveLayout="scroll">
                                                    <Column
                                                        v-for="(header, colIndex) in (dataFichaAll.listaHeadersTipo.find(x => x.tipo_contenedor === 'matriz tipo 1')?.matriz?.[0] || [])"
                                                        :key="colIndex" :field="String(colIndex)" :header="header"
                                                        :body="(row) => row[colIndex]" />
                                                </DataTable>

                                            </div>
                                            <div v-else-if="titulo.tipo_contenedor === 'tabla tipo 1'">
                                                
                                                <DataTable
                                                    showGridlines
                                                    stripedRows
                                                    tableStyle="min-width: 50rem"
                                                    :value="(() => {
                                                        const tabla = dataFichaAll && Array.isArray(dataFichaAll.listaHeadersTipo)
                                                            ? dataFichaAll.listaHeadersTipo.find(x => x.tipo_contenedor === 'tabla tipo 1')?.tabla || []
                                                            : [];
                                                        return tabla.slice(2); // skip first two header rows
                                                    })()"
                                                >
                                                    <ColumnGroup type="header">
                                                        <Row>
                                                            <template
                                                                v-if="dataFichaAll && Array.isArray(dataFichaAll.listaHeadersTipo)">
                                                                <template v-for="(header, idx) in (() => {
                                                                    // Get the tabla header row (first row)
                                                                    const tabla = dataFichaAll.listaHeadersTipo.find(x => x.tipo_contenedor === 'tabla tipo 1')?.tabla || [];
                                                                    const firstRow = tabla[0] || [];
                                                                    // Build array of { header, colspan }
                                                                    const result = [];
                                                                    let i = 0;
                                                                    while (i < firstRow.length) {
                                                                        const current = firstRow[i] || '';
                                                                        let colspan = 1;
                                                                        // Count consecutive repeats
                                                                        while (
                                                                            i + colspan < firstRow.length &&
                                                                            firstRow[i + colspan] === current &&
                                                                            current !== ''
                                                                        ) {
                                                                            colspan++;
                                                                        }
                                                                        // If current is empty, always print as single column (no colspan)
                                                                        if (current === '') {
                                                                            result.push({ header: '', colspan: 1 });
                                                                            i += 1;
                                                                        } else if (colspan > 1) {
                                                                            // If repeated, print once with colspan
                                                                            result.push({ header: current, colspan });
                                                                            i += colspan;
                                                                        } else {
                                                                            // Not repeated, print as single column
                                                                            result.push({ header: current, colspan: 1 });
                                                                            i += 1;
                                                                        }
                                                                    }
                                                                    return result;
                                                                })()" :key="idx">
                                                                    <Column
                                                                        :header="header.header === undefined || header.header === null || header.header === '' ? '' : header.header"
                                                                        :colspan="header.colspan > 1 ? header.colspan : undefined"
                                                                        class="center-header" />
                                                                </template>
                                                            </template>
                                                        </Row>
                                                        <Row>
                                                            <template
                                                                v-if="dataFichaAll && Array.isArray(dataFichaAll.listaHeadersTipo)">
                                                                <template v-for="(header, idx) in (() => {
                                                                    // Get the tabla header row (second row)
                                                                    const tabla = dataFichaAll.listaHeadersTipo.find(x => x.tipo_contenedor === 'tabla tipo 1')?.tabla || [];
                                                                    const secondRow = tabla[1] || [];
                                                                    // Build array of { header, colspan }
                                                                    const result = [];
                                                                    let i = 0;
                                                                    while (i < secondRow.length) {
                                                                        const current = secondRow[i] || '';
                                                                        let colspan = 1;
                                                                        while (i + colspan < secondRow.length && secondRow[i + colspan] === current) {
                                                                            colspan++;
                                                                        }
                                                                        result.push({ header: current, colspan });
                                                                        i += colspan;
                                                                    }
                                                                    // If secondRow is not empty, ensure the last column is included
                                                                    if (secondRow.length > 0 && result.length > 0) {
                                                                        const totalColspan = result.reduce((sum, h) => sum + h.colspan, 0);
                                                                        if (totalColspan < secondRow.length) {
                                                                            // Add the missing last column
                                                                            result.push({ header: secondRow[secondRow.length - 1], colspan: 1 });
                                                                        }
                                                                    }
                                                                    return result;
                                                                })()" :key="idx">
                                                                    <Column
                                                                        :header="header.header === undefined || header.header === null || header.header === '' ? '' : header.header"
                                                                        :colspan="header.colspan > 1 ? header.colspan : undefined"
                                                                        class="center-header" />
                                                                </template>
                                                            </template>
                                                        </Row>
                                                    </ColumnGroup>
                                                    <Column
                                                        v-for="(col, colIndex) in (() => {
                                                            const tabla = dataFichaAll && Array.isArray(dataFichaAll.listaHeadersTipo)
                                                                ? dataFichaAll.listaHeadersTipo.find(x => x.tipo_contenedor === 'tabla tipo 1')?.tabla || []
                                                                : [];
                                                            // Find the max length among all rows (header + data)
                                                            return Math.max(...tabla.map(row => row.length));
                                                        })()"
                                                        :key="colIndex"
                                                        :field="String(colIndex)"
                                                        :header="''"
                                                    >
                                                        <template #body="slotProps">
                                                            {{ slotProps.data[colIndex] }}
                                                        </template>
                                                    </Column>
                                                </DataTable>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <br />
                </div>
            </div>
        </div>
        <!-- Loading -->
        <div v-else class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
                <Loading />
            </div>
        </div>

    </div>

    <Toast position="bottom-left" group="bl" />
</template>

<script>
import { useAuthStore } from '../../store/auth';
import { ref, computed, onMounted } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import axios from 'axios';
import Papa from 'papaparse';
import { useToast } from 'primevue/usetoast';
import { useRoute, useRouter } from 'vue-router';

export default {
    setup() {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const permissionsList = computed(() => decryptData(authStore.permissionsList));
        const userToken = computed(() => decryptData(authStore.idToken));
        const route = useRoute();
        const router = useRouter();
        const authStore = useAuthStore();
        const toast = useToast();
        const isLoading = ref(false);
        const globalLoading = ref(false);
        const dataFichaGeneral = ref(null);
        const dataDimension = ref(null)
        const dataFichaAll = ref(null);
        const selectedFicha = ref(null);
        const selectedDimension = ref(null);
        const generarFicha = ref(false);

        onMounted(async () => {
            // Get the data for the DataTable
            await getData();
            console.log(dataFichaGeneral.value);
        });

        /**
        * Get the data from the back-end from fichaUniversal
        */
        const getData = async () => {

            try {
                isLoading.value = true;
                const response = await axios.get(API_BASE_URL + "ficha_universal", {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                dataFichaGeneral.value = response.data;
            } catch (error) {
                console.log("Error: " + error);
            } finally {
                console.log("Finally");
                isLoading.value = false;
            }
        }
        /**
       * Get the data from the back-end from the selected dimension
       */
        const getDimension = async () => {

            try {
                isLoading.value = true;
                const response = await axios.get(API_BASE_URL + "ficha_universal/dimension/" + selectedFicha.value.ficha_universal_id, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                dataDimension.value = response.data;
                console.log(dataDimension.value);
            } catch (error) {
                console.log("Error: " + error);
            } finally {
                console.log("Finally");
                isLoading.value = false;
            }
        }
        /**
         * Get the data from the back-end from the titulos of the selected dimension
         */
        const obtenerDatosHeaderTipos = async () => {
            try {
                isLoading.value = true;

                const titulos_ids = Array.isArray(dataDimension.value)
                    ? dataDimension.value.flatMap(dim =>
                        Array.isArray(dim.Titulos)
                            ? dim.Titulos.map(titulo => titulo.titulo_id)
                            : []
                    )
                    : [];
                console.log("Titulos IDs: ", titulos_ids);

                // Use POST to send body data
                const response = await axios.post(
                    API_BASE_URL + "ficha_universal/headersTipos/",
                    { lista_id_titulos: titulos_ids },
                    {
                        headers: {
                            Authorization: `Bearer ${userToken.value}`
                        }
                    }
                );
                if (response.status === 200) {
                    dataFichaAll.value = response.data;
                    console.log("Datos obtenidos correctamente:", dataFichaAll.value);
                    generarFicha.value = true; // Set to true to indicate data is ready for display                    
                }
            } catch (error) {
                console.log("Error: " + error);
            } finally {
                console.log("Finally");
                isLoading.value = false;
            }

        }

        return {
            globalLoading,
            isLoading,
            getData,
            dataFichaGeneral,
            selectedFicha,
            getDimension,
            dataDimension,
            selectedDimension,
            generarFicha,
            obtenerDatosHeaderTipos,
            dataFichaAll

        }
    }

}

</script>

<style scoped>
:deep() .center-header {
    text-align: center !important;
    align-content: center !important;
}

:deep() .p-datatable .p-column-header-content {
    display: block !important;

}

.yellow-icon .pi {
    color: yellow;
}

.green-icon .pi {
    color: greenyellow;
}
</style>