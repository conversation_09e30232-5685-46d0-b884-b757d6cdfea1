<template>
    <div class="loading-container">
        <div class="center">
            <i class="pi pi-spin pi-cog" style="font-size: 3rem; color: var(--primary-color);"></i>
        </div>
        <div class="center">
            <h3>Cargando...</h3>
        </div>
    </div>
</template>

<script>
export default {
    name: 'Loading'
}
</script>

<style scoped>
.loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.center {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0.5rem 0;
}

h3 {
    margin: 0.5rem 0;
    color: var(--text-color);
}
</style>
