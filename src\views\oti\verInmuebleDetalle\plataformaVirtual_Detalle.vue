<template>
    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading">

            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <div class='surface-card shadow-3 p-3 border-round'>
                    <form @submit.prevent="submitForm">
                        <div style="display: flex;justify-content: space-between;align-items: center;">
                            <div
                                style="display: flex;justify-content: space-between;align-items: center;padding-left: 1rem;">
                                <div style="display: flex;justify-content: space-between;align-items: center;">
                                    <Button @click.prevent="goBack()"> <- </Button>
                                            <h2 style="padding-left: 1rem;">Detalle Plataforma Virtual - Proceso {{
                                                anio_proceso }}
                                            </h2>
                                </div>
                            </div>
                            <Button
                                v-if="infraestructuraRecursoData.is_finalized != true && (hasAccess('Encargado', 'OTI') || hasAccess('Usuario General', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU'))"
                                :disabled="editForm" @click="editingForm">Editar</Button>
                        </div>
                        <br />
                        <!--Informacion Basica de los inmuebles de uso restringido-->
                        <div>
                            <h3 style="padding-left: 1rem;">Información básica</h3>
                            <div style="margin-left: 1rem;margin-right: 1rem;">
                                <div v-if="true" class="formgrid grid">
                                    <div class="field col-6" v-for="(field, index) in formFormat1" :key="field.id">
                                        <label :for="field.name" style="padding-right: 1rem ;">{{ field.displayName
                                            }}</label>
                                        <i class="pi pi-info-circle" v-if="!field.dontNeedtoolTip"
                                            style="justify-content: center " v-tooltip.bottom="field.tooltip" />
                                        <InputText :class="{
                                            'w-full': true, 'p-invalid': (formData[field.name] == null || formData[field.name] == '') && submitted
                                        }" v-if="field.contenedor === 'inputText'" :id=field.name autocomplete="off"
                                            :placeholder=field.placeholder :type=field.type style="height:2.6rem"
                                            v-model="formData[field.name]" :disabled="field.disabled"
                                            @input="handleInput($event, field.name)" :maxlength="200">
                                        </InputText>
                                        <Dropdown :class="{
                                            'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                                        }" v-if="field.contenedor === 'dropdown'" v-model="formData[field.name]" filter
                                            :placeholder="field.placeholder" style="height:2.6rem;margin-bottom: 1rem"
                                            :options="getOptions(field.options)" :optionLabel="field.optionLabel"
                                            :optionValue="field.optionValue" class="w-full" :disabled="field.disabled">
                                        </Dropdown>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--Información sobre Plataformas Virtuales-->
                        <div>
                            <h3 style="padding-left: 1rem;">Información sobre Plataformas Virtuales</h3>
                            <div style="margin-left: 1rem;margin-right: 1rem;">
                                <div v-if="true" class="formgrid grid">
                                    <div class="field col-6" v-for="(field, index) in formFormat2" :key="field.id">
                                        <label :for="field.name" style="padding-right: 1rem ;">{{ field.displayName
                                            }}</label>
                                        <i class="pi pi-info-circle" v-if="!field.dontNeedtoolTip"
                                            style="justify-content: center " v-tooltip.bottom="field.tooltip" />
                                        <InputText :class="{
                                            'w-full': true, 'p-invalid': (formData[field.name] == null || formData[field.name] == '') && submitted
                                        }" v-if="field.contenedor === 'inputText'" :id=field.name autocomplete="off"
                                            :placeholder=field.placeholder :type=field.type style="height:2.6rem"
                                            v-model="formData[field.name]" :modelValue="field.modelValue"
                                            @input="handleInput2($event, field.name)" :disabled="field.disabled"
                                            :maxlength="200">
                                        </InputText>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Vigencia -->
                        <div>
                            <h3 style="padding-left: 1rem;">Vigencia de los registros de infraestructura informados</h3>
                            <div style="margin-left: 1rem;margin-right: 1rem;">
                                <div v-if="true" class="formgrid grid">
                                    <div class="field col-6" v-for="(field, index) in formFormat3" :key="field.id">
                                        <label style="padding-right: 1rem;" :for="field.name">{{ field.displayName
                                            }}</label>
                                        <i style="justify-content: center" class=" pi pi-info-circle"
                                            v-tooltip.bottom="field.tooltip" />
                                        <Dropdown :class="{
                                            'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                                        }" v-if="field.contenedor === 'dropdown'" v-model="formData[field.name]" filter
                                            :placeholder="field.placeholder" style="height:2.6rem;margin-bottom: 1rem"
                                            :options="getOptions(field.options)" :optionLabel="field.optionLabel"
                                            :optionValue="field.optionValue" class="w-full" :disabled="field.disabled">
                                        </Dropdown>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Buttons actualizar - cancelar -->
                        <div
                            style="display: flex; justify-content: center; align-items: center; padding-top: 0.5rem; padding-bottom: 0.5rem;">
                            <Button v-if="editForm" label="Actualizar" @click.prevent="submitForm" />
                            <Button v-if="editForm" severity="danger" @click="cancelEditingForm"
                                style="margin-left: 1rem;">Cancelar</Button>
                        </div>
                    </form>
                </div>
            </div>
            <Toast position="bottom-left" group="bl" />
        </div>
        <div v-else class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
                <Loading />
            </div>
        </div>
    </div>
</template>

<script>
import { useAuthStore } from '../../../store/auth';
import { ref, computed, onMounted, resolveDirective } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import axios from 'axios';
import InputText from 'primevue/inputtext';
import { useToast } from 'primevue/usetoast';
import { useRouter } from 'vue-router';
import Button from 'primevue/button';

export default {
    props: {
        plataformaVirtual_id: {
            type: String,
            required: true,
        },
    },
    setup(props) {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const router = useRouter();
        const authStore = useAuthStore();
        const toast = useToast();
        // Obtain the stored user data from pinia and the global loading state
        const userName = computed(() => decryptData(authStore.userName));
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const permissionsList = computed(() => decryptData(authStore.permissionsList));
        const isUserAuthenticated = computed(() => decryptData(authStore.userIsAuthenticated));
        const globalLoading = computed(() => decryptData(authStore.isLoading));
        const userToken = computed(() => decryptData(authStore.idToken));
        const listaComunas = ref([]);

        // Variable to store InfraestructuraRecurso data
        const plataformaVirtualData = ref(null);
        const infraestructuraRecursoData = ref({});
        const editForm = ref(false);

        // Theme for changing the actual theme from dark to light or viceversa
        const theme = ref("dark");

        // Local "Loading" for the current view
        const isLoading = ref(false);
        const formIsLoading = ref(false);

        const anio_proceso = ref(null); // Access the param
        const submitted = ref(false);

        const hasAccess = (allowedRol, requiredRecurso) => {
            const access = permissionsList.value.some(user => user.rol === allowedRol && user.recurso === requiredRecurso);
            return access;
        };

        // Define initial form data
        const initialFormData = {
            tipo_infraestructura: "Plataforma Virtual",
            nombre_identificacion: null,
            comuna: null,
            direccion_inmueble: null,
            sistema_gestion_aprendizajes: null,
            sistema_video_conferencia: null,
            sistema_aplicacion_evaluacion: null,
            descripcion_plataforma_virtual: null,
            vigencia: 1,
            creador_email: userEmail.value,
            creador_id: "sin asignar",
            anio_proceso: anio_proceso
        };

        const formData = ref({ ...initialFormData });
        const formFormat1 = computed(() => [
            { id: 1, placeholder: '', name: 'tipo_infraestructura', displayName: 'Seleccione tipo de infraestructura', type: 'text', contenedor: 'inputText', disabled: 'true', dontNeedtoolTip: true },
            { id: 2, placeholder: 'Nombre inmueble', name: 'nombre_identificacion', displayName: 'Ingrese nombre o identificacion del inmueble', type: 'text', contenedor: 'inputText', tooltip: 'Nombre con que la institución identifica el inmueble. Varios inmuebles cercanos pueden tener el mismo nombre o identificación.', disabled: !editForm.value },
            { id: 3, placeholder: 'Comuna', name: 'comuna', displayName: 'Seleccione comuna', type: 'text', contenedor: 'dropdown', options: 'listaComunas', optionLabel: 'nombre_comuna', optionValue: "nombre_comuna", tooltip: 'Corresponde a la comuna en que se encuentra el inmueble que está siendo informado.', disabled: !editForm.value },
            { id: 4, placeholder: 'Dirección inmueble', name: 'direccion_inmueble', displayName: 'Ingrese dirección del inmueble', type: 'text', contenedor: 'inputText', tooltip: 'Indicación del nombre de la calle y la numeración completa que corresponden a la ubicación física del inmueble que se está informando.', disabled: !editForm.value },
        ]);

        const formFormat2 = computed(() => [
            { id: 5, placeholder: 'Nombre sistema de gestión aprendizaje', name: 'sistema_gestion_aprendizajes', displayName: 'Ingrese nombre del sistema de gestión de aprendizaje', contenedor: 'inputText', tooltip: 'Corresponde a la identificación del software o sistema (LMS o LCMS) para la gestión, administración, distribución y control de los procesos de formación educativa en línea que utiliza la institución. Ejemplos de estos pueden ser Moodle, BlackBoard, CANVAS, entre otros.', disabled: !editForm.value },
            { id: 6, placeholder: 'Nombre sistema de video conferencia', name: 'sistema_video_conferencia', displayName: 'Ingrese nombre del sistema de video conferencia', contenedor: 'inputText', tooltip: 'Corresponde a la identificación del software o sistema de video conferencia que permite a la institución realizar sus clases en línea, con un académico interactuando remotamente con los estudiantes de un curso, módulo o asignatura.', disabled: !editForm.value },
            { id: 7, placeholder: 'Nombre plataforma o sistema de evaluación', name: 'sistema_aplicacion_evaluacion', displayName: 'Ingrese nombre de la plataforma o sistema de evaluación', contenedor: 'inputText', tooltip: 'Corresponde al software o sistema que utiliza la institución para la aplicación y evaluación de exámenes o pruebas en línea a estudiantes (Proctorio, Examinity, Respondus, entre otros).', disabled: !editForm.value },
            { id: 8, placeholder: 'Descripción plataforma virtual', name: 'descripcion_plataforma_virtual', displayName: 'Ingrese descripción de plataforma virtual', contenedor: 'inputText', tooltip: 'Corresponde a la explicación que la institución pueda querer realizar sobre su forma de uso de su plataforma virtual, especialmente cuando utiliza más de un tipo de sistema de gestión de aprendizajes, orientado a grupos distintos.', disabled: !editForm.value },
        ]);

        const formFormat3 = computed(() => [
            { id: 12, placeholder: 'Vigencia del inmueble', name: 'vigencia', displayName: 'Seleccione la vigencia del inmueble', contenedor: 'dropdown', options: 'vigencia_list', optionLabel: 'options', optionValue: "value", tooltip: 'Variable utilizada para mantener o eliminar un registro cargado.', disabled: !editForm.value },
        ]);

        const comuna_list = ref([
            { options: 'Antofagasta', value: "Antofagasta" },
            { options: 'Mejillones', value: "Mejillones" },
            { options: 'Sierra Gorda', value: "Sierra Gorda" },
            { options: 'Taltal', value: "Taltal" },
            { options: 'Calama', value: "Calama" },
            { options: 'San Pedro de Atacama', value: "San Pedro de Atacama" },
            { options: 'Tocopilla', value: "Tocopilla" },
            { options: 'María Elena', value: "María Elena" },
            { options: 'Providencia', value: "Providencia" },
        ]);

        const vigencia_list = ref([
            { options: 'Eliminar registro cargado', value: 0 },
            { options: 'Mantener registro cargado', value: 1 },

        ]);
        /**
        * Get options for the dropdown field based on the field options string
        */
        const getOptions = (optionKey) => {
            if (optionKey === 'listaComunas') {
                return listaComunas.value;
            }
            if (optionKey === 'vigencia_list') {
                return vigencia_list.value;
            }
            return [];
        }

        // Function to submit the form
        const submitForm = async () => {
            submitted.value = true;
            if (!validateForm()) {
                // Notify the user about the empty fields
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Existen campos vacios', life: 5000 });
                return;
            }

            try {
                isLoading.value = true;

                const success = await editPlataformaVirtualData(formData.value);

                if (success) {
                    // Notify the user of successful form submission
                    toast.add({ severity: 'success', summary: 'Correcto', group: 'bl', detail: 'Formulario actualizado correctamente', life: 5000 });
                    submitted.value = false; // Reset submitted state
                    editForm.value = false;
                    // Redirect to the previous route with a success message
                    router.push({
                        name: 'Pagina-detalle-oti',
                        params: { anio_proceso: encryptData(anio_proceso.value) }, // Replace with the actual route name or path
                        query: { success: 'Formulario actualizado correctamente' }
                    });
                }
            } catch (error) {
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Hubo un problema al enviar el formulario', life: 5000 });
            } finally {
                isLoading.value = false;
            }
        };

        const validateForm = () => {
            let hasErrors = false;

            // Iterate through formData keys and validate non-conditional fields
            Object.keys(formData.value).forEach((key) => {
                const value = formData.value[key];

                // Check if the value is null or empty
                if (value === null || value === "") {
                    hasErrors = true;
                }
            });

            if (hasErrors) {
                // Return false if there are any errors
                return false;
            }
            return true;
        };

        const editingForm = () => {
            editForm.value = true;
        }

        const cancelEditingForm = () => {
            editForm.value = false;
            // Map fetched data to initialFormData dynamically
            Object.keys(initialFormData).forEach(key => {
                if (plataformaVirtualData.value[key] !== undefined) {
                    initialFormData[key] = plataformaVirtualData.value[key];
                }
            });
            formData.value.tipo_infraestructura = "Plataforma Virtual"
        }


        const editPlataformaVirtualData = async (formDataCompleted) => {
            isLoading.value = true; // Start loading
            try {
                if (formDataCompleted.nombre_identificacion != null && formDataCompleted.nombre_identificacion != "") {
                    formDataCompleted.nombre_identificacion = formDataCompleted.nombre_identificacion.trim();
                }
                if (formDataCompleted.direccion_inmueble != null && formDataCompleted.direccion_inmueble != "") {
                    formDataCompleted.direccion_inmueble = formDataCompleted.direccion_inmueble.trim();
                }
                if (formDataCompleted.sistema_gestion_aprendizajes != null && formDataCompleted.sistema_gestion_aprendizajes != "") {
                    formDataCompleted.sistema_gestion_aprendizajes = formDataCompleted.sistema_gestion_aprendizajes.trim();
                }
                if (formDataCompleted.sistema_video_conferencia != null && formDataCompleted.sistema_video_conferencia != "") {
                    formDataCompleted.sistema_video_conferencia = formDataCompleted.sistema_video_conferencia.trim();
                }
                if (formDataCompleted.sistema_aplicacion_evaluacion != null && formDataCompleted.sistema_aplicacion_evaluacion != "") {
                    formDataCompleted.sistema_aplicacion_evaluacion = formDataCompleted.sistema_aplicacion_evaluacion.trim();
                }
                if (formDataCompleted.descripcion_plataforma_virtual != null && formDataCompleted.descripcion_plataforma_virtual != "") {
                    formDataCompleted.descripcion_plataforma_virtual = formDataCompleted.descripcion_plataforma_virtual.trim();
                }
                formDataCompleted.plataformaVirtual_id = plataformaVirtualData.value.plataformaVirtual_id;
                formDataCompleted.infraestructuraRecurso_id = plataformaVirtualData.value.infraestructuraRecurso_id;
                formDataCompleted.version_lock = plataformaVirtualData.value.version_lock;
                formDataCompleted.userToken = "Bearer " + userToken.value;
                const response = await axios.patch(API_BASE_URL + `plataformaVirtual/${formDataCompleted.plataformaVirtual_id}`, formDataCompleted, {
                    headers: {
                        'Authorization': formDataCompleted.userToken
                    },
                });

                // Check if the response is successful (status code 200)
                if (response.status === 200) {
                    console.log("Success:", response.data); // Handle the response data if needed
                    return true; // Return true on success
                } else {
                    console.error("Unexpected response status:", response.status);
                    return false; // Return false if not 200 OK
                }
            } catch (error) {
                if (error.status === 403) {
                    toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El registro no puede ser modificado', life: 5000 });
                }
                if (error.status === 409) {
                    toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El registro fue modificado por otro usuario, inténtelo más tarde', life: 10000 });
                }
                console.error("Error fetching InfraestructuraRecurso data:", error.response || error);
                return false; // Return false on error
            } finally {
                isLoading.value = false; // End loading
            }
        };

        // On component mount, fetch data
        onMounted(async () => {
            if (props.plataformaVirtual_id) {
                await fetchPlataformaVirtual(props.plataformaVirtual_id);
                formData.value = initialFormData
                console.log(formData.value);
                formData.value.tipo_infraestructura = "Plataforma Virtual"
                anio_proceso.value = initialFormData.anio_proceso;
                await fetchInfraestructuraRecursoData(anio_proceso.value);
                await fetchListaComunas();
            }
        });

        // Function to fetch InfraestructuraRecurso data based on the year
        const fetchInfraestructuraRecursoData = async (year) => {
            isLoading.value = true; // Start loading
            try {
                const response = await axios.get(API_BASE_URL + "infraestructuraRecurso/" + year, {
                    headers: {
                        'Authorization': "Bearer " + userToken.value
                    }
                });
                infraestructuraRecursoData.value = response.data;
                console.log(infraestructuraRecursoData.value);
            } catch (error) {
                console.error("Error fetching InfraestructuraRecurso data:", error);
            } finally {
                isLoading.value = false; // End loading
            }
        };

        // Function to fetch plataformaVirtual data based on the plataformaVirtual_id
        const fetchPlataformaVirtual = async (plataformaVirtual_id) => {
            isLoading.value = true; // Start loading
            try {
                const response = await axios.get(API_BASE_URL + "plataformaVirtual/" + plataformaVirtual_id, {
                    headers: {
                        'Authorization': "Bearer " + userToken.value
                    }
                });

                if (response.status === 200) {
                    plataformaVirtualData.value = response.data;

                    // Map fetched data to initialFormData dynamically
                    Object.keys(initialFormData).forEach(key => {
                        if (plataformaVirtualData.value[key] !== undefined) {
                            initialFormData[key] = plataformaVirtualData.value[key];
                        }
                    });
                }
            } catch (error) {
                console.error("Error fetching Biblioteca data:", error);
            } finally {
                isLoading.value = false; // End loading
            }
        };

        // Function to fetch InfraestructuraRecurso data based on the year
        const fetchListaComunas = async () => {
            isLoading.value = true; // Start loading
            try {
                const response = await axios.get(API_BASE_URL + "comunas-OTI/", {
                    headers: {
                        'Authorization': "Bearer " + userToken.value
                    }
                });
                listaComunas.value = response.data;
                console.log(listaComunas.value);
            } catch (error) {
                console.error("Error fetching InfraestructuraRecurso data:", error);
            } finally {
                isLoading.value = false; // End loading
            }
        };
        const goBack = () => {
            router.go(-1); // Navigate to the previous route using Vue Router
        };

        return {
            isLoading,
            globalLoading,
            anio_proceso,
            plataformaVirtualData,
            editingForm,
            fetchPlataformaVirtual,
            fetchListaComunas,
            listaComunas,
            submitted,
            formData,
            formFormat1,
            formFormat2,
            formFormat3,
            comuna_list,
            vigencia_list,
            getOptions,
            submitForm,
            validateForm,
            formIsLoading,
            submitted,
            editPlataformaVirtualData,
            router,
            editForm,
            cancelEditingForm,
            initialFormData,
            goBack,
            fetchInfraestructuraRecursoData,
            infraestructuraRecursoData,
            hasAccess,
            permissionsList,
            userToken
        }
    },
    methods: {
        handleInput(event, fieldName) {
            let value = event.target.value;

            let sanitizedValue = value.replace(/^( |[^a-zA-Z0-9 ])|[^a-zA-Z0-9 ]/g, '') // Remove invalid characters and prevent leading space
                .replace(/\s+/g, ' '); // Replace multiple spaces with a single space

            // Update the formData with the sanitized value
            this.formData[fieldName] = sanitizedValue;
        }
        ,
        handleInput2(event, fieldName) {
            let value = event.target.value;

            // Sanitize the value to allow only letters (lowercase and uppercase), no leading spaces
            let sanitizedValue = value.replace(/^( |[^a-zA-Z ])|[^a-zA-Z ]/g, '') // Remove invalid characters and leading non-letter characters
                .replace(/\s+/g, ' '); // Remove all spaces

            // Update the formData with the sanitized value
            this.formData[fieldName] = sanitizedValue;
        }
    },
}
</script>