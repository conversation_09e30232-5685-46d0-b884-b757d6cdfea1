import { fileURLToPath, URL } from "node:url";

import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import fs from "fs";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue(), vueJsx()],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  server: {
    https: {
      //abrir como administrador el cmd y ejecutar el siguiente comando para instalar mkcert
      //choco install mkcert
      //mkcert -install
      // mkcert localhost
      // cerrar y abrir sin admin el code
      key: fs.readFileSync(path.resolve("localhost-key.pem")),
      cert: fs.readFileSync(path.resolve("localhost.pem")),
    },
    port: 5173,
  },
});
