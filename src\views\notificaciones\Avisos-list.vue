<template>
    <div class="card">
        <DataTable :value="userNotifications" tableStyle="min-width: 50rem" :paginator="true" :rows="10">
            <Column field="titulo" header="Título"></Column>
            <Column field="mensaje" header="Mensaje"></Column>
            <Column field="tipo" header="Tipo"></Column>
            <Column field="createdAt" header="Fecha"></Column>
            <Column field="creador_email" header="De"></Column>
            <Column field="ocultar_Button" header="Ocultar">
                <template #body="slotProps">
                    <Button icon="pi pi-eye-slash" class="p-button-text" @click="onOcultar(slotProps.rowData)" />
                </template>
            </Column>
            <Column field="confirmar_Tipo" header="Confirmar">
                <template #body="slotProps">
                    <Button icon="pi pi-check-square" class="p-button-text" @click="onConfirmar(slotProps.rowData)" />
                </template>
            </Column>
        </DataTable>

    </div>
</template>

<script>
import { useAuthStore } from '../../store/auth';
import { ref, computed, onMounted } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import axios from "axios";

export default {

    name: "Avisos-List",

    setup() {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const authStore = useAuthStore();

        const userEmail = computed(() => decryptData(authStore.userEmail));
        const isLoading = ref(false);
        const userNotifications = ref(null);

        const getNotificationList = async () => {
            try {
                isLoading.value = true;
                const response = await axios.get(API_BASE_URL + "notificacion/" + userEmail.value);
                userNotifications.value = response.data.notificaciones;

                // Function to format the createdAt date
                const formatNotificationDates = () => {
                    userNotifications.value.forEach(notification => {
                        const date = new Date(notification.createdAt);
                        notification.createdAt = date.toLocaleDateString();  // Format date to a more readable form
                    });
                };
                formatNotificationDates();

            } catch (error) {
                console.error("Error: ", error);
            } finally {
                isLoading.value = false;
            }
        }

        onMounted(() => {
            getNotificationList();
        });

        // Return data object
        return {
            authStore,
            userEmail,
            isLoading,
            userNotifications

        };
    }
}
</script>
