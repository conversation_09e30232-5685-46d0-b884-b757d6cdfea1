import { createRouter, createWebHistory } from "vue-router";
import { useAuthStore } from "../store/auth";
import { msalInstance } from "../store/auth";
import { InteractionRequiredAuthError } from "@azure/msal-browser";
import axios from "axios";
import { encryptData, decryptData } from "@/utils/crypto";
import {
  validateAndDecryptParam,
  validateAndDecryptParamID,
  validateAndDecryptParamID2,
  validateAndDecryptAnioProceso,
} from "@/utils/route-guard";

const routes = [
  {
    path: "/login",
    name: "login",
    component: () => import("../views/login/Login.vue"),
    meta: {
      requiresAuth: false,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/",
    name: "home",
    component: () => import("../views/home/<USER>"),
    meta: {
      requiresAuth: true,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/avisos-central",
    name: "avisos-central",
    component: () => import("../views/notificaciones/Avisos-pag-central.vue"),
    meta: {
      requiresAuth: true,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: true, resource: "notificaciones" },
    },
  },
  {
    path: "/admision-view/",
    name: "Admision-View",
    component: () =>
      import("../views/admision/datatable-views/Admision-View.vue"),
    meta: {
      requiresAuth: false,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/admision-form/",
    name: "Admision-Form",
    component: () =>
      import("../views/admision/formulario-creacion/Admision-Form.vue"),
    meta: {
      requiresAuth: false,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/admision-csv/",
    name: "Admision-Csv",
    component: () => import("../views/admision/cargar-csv/LoadCsvFile.vue"),
    meta: {
      requiresAuth: false,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/tabs/",
    name: "Tabs-Csv",
    component: () => import("../views/admision/cargar-csv/Tabs.vue"),
    meta: {
      requiresAuth: false,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/Error",
    name: "Error",
    component: () => import("../views/error/Error.vue"),
    meta: {
      requiresAuth: false,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/Admin",
    name: "Admin",
    component: () => import("../views/AdminView.vue"),
    meta: {
      requiresAuth: true,
      requiresRole: { isRequired: true, role: "Administrador Sistema" },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/Unauthorized",
    name: "Unauthorized",
    component: () => import("../views/Unauthorized.vue"),
    meta: {
      requiresAuth: false,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/Pg-principal-adm-usuarios",
    name: "Pg-principal-adm-usuarios",
    component: () =>
      import("../views/administracionUsuarios/pg-principal-adm-usuarios.vue"),
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Encargado", "Administrador Sistema"],
      },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/Adm-crear-usuario",
    name: "Adm-crear-usuario",
    component: () =>
      import(
        "../views/administracionUsuarios/panel_control/adm-creacion-usuario.vue"
      ),
    meta: {
      requiresAuth: true,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/Adm-lista-usuario",
    name: "Adm-lista-usuario",
    component: () =>
      import(
        "../views/administracionUsuarios/panel_control/adm-lista-usuario.vue"
      ),
    meta: {
      requiresAuth: true,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/configuracion-oti",
    name: "configuracion-oti",
    component: () => import("../views/oti/configuracion/configuracion.vue"),
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Encargado", "Administrador Sistema"],
      },
      requiresResource: {
        isRequired: true,
        resource: ["OTI", "Administracion PIU"],
      },
    },
  },
  {
    path: "/Pagina-principal-oti",
    name: "Pagina-principal-oti",
    component: () => import("../views/oti/pg-principal-oti.vue"),
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: [
          "Invitado",
          "Usuario General",
          "Encargado",
          "Administrador Sistema",
        ],
      },
      requiresResource: {
        isRequired: true,
        resource: ["OTI", "OTI", "OTI", "Administracion PIU"],
      },
    },
  },
  {
    path: "/Pagina-detalle-oti/:anio_proceso",
    name: "Pagina-detalle-oti",
    component: () => import("../views/oti/pg-detalle-oti.vue"),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: [
          "Invitado",
          "Usuario General",
          "Encargado",
          "Administrador Sistema",
        ],
      },
      requiresResource: {
        isRequired: true,
        resource: ["OTI", "OTI", "OTI", "Administracion PIU"],
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptParam(to, next, "anio_proceso");
    },
  },
  {
    path: "/Crear-inmueble-permanente/:anio_proceso",
    name: "Crear-inmueble-permanente",
    component: () =>
      import(
        "../views/oti/formularioCreacion/inmueble-permanente_FormularioCreacion.vue"
      ),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"],
      },
      requiresResource: {
        isRequired: true,
        resource: ["OTI", "OTI", "Administracion PIU"],
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptParam(to, next, "anio_proceso");
    },
  },
  {
    path: "/Crear-inmueble-restringido/:anio_proceso",
    name: "Crear-inmueble-restringido",
    component: () =>
      import(
        "../views/oti/formularioCreacion/inmueble-restringido_FormularioCreacion.vue"
      ),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"],
      },
      requiresResource: {
        isRequired: true,
        resource: ["OTI", "OTI", "Administracion PIU"],
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptParam(to, next, "anio_proceso");
    },
  },
  {
    path: "/Crear-biblioteca/:anio_proceso",
    name: "Crear-biblioteca",
    component: () =>
      import(
        "../views/oti/formularioCreacion/biblioteca_FormularioCreacion.vue"
      ),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"],
      },
      requiresResource: {
        isRequired: true,
        resource: ["OTI", "OTI", "Administracion PIU"],
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptParam(to, next, "anio_proceso");
    },
  },
  {
    path: "/Crear-libro-base-digital/:anio_proceso",
    name: "Crear-libro-base-digital",
    component: () =>
      import(
        "../views/oti/formularioCreacion/libro-baseDatos_FormularioCreacion.vue"
      ),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"],
      },
      requiresResource: {
        isRequired: true,
        resource: ["OTI", "OTI", "Administracion PIU"],
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptParam(to, next, "anio_proceso");
    },
  },
  {
    path: "/Crear-predio/:anio_proceso",
    name: "Crear-predio",
    component: () =>
      import("../views/oti/formularioCreacion/predio_FormularioCreacion.vue"),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"],
      },
      requiresResource: {
        isRequired: true,
        resource: ["OTI", "OTI", "Administracion PIU"],
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptParam(to, next, "anio_proceso");
    },
  },
  {
    path: "/Crear-plataforma-virtual/:anio_proceso",
    name: "Crear-plataforma-virtual",
    component: () =>
      import(
        "../views/oti/formularioCreacion/plataforma-virtual_FormularioCreacion.vue"
      ),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"],
      },
      requiresResource: {
        isRequired: true,
        resource: ["OTI", "OTI", "Administracion PIU"],
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptParam(to, next, "anio_proceso");
    },
  },
  {
    path: "/Ver-inmueble-permanente/:inmueblePermanente_id",
    name: "Ver-inmueble-permanente",
    component: () =>
      import("../views/oti/verInmuebleDetalle/inmueblePermanente_Detalle.vue"),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"],
      },
      requiresResource: {
        isRequired: true,
        resource: ["OTI", "OTI", "Administracion PIU"],
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptParamID2(
        to,
        next,
        "inmueblePermanente_id",
        "inmueblePermanente"
      );
    },
  },
  {
    path: "/Ver-inmueble-restringido/:inmuebleRestringido_id",
    name: "Ver-inmueble-restringido",
    component: () =>
      import("../views/oti/verInmuebleDetalle/inmuebleRestringido_Detalle.vue"),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"],
      },
      requiresResource: {
        isRequired: true,
        resource: ["OTI", "OTI", "Administracion PIU"],
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptParamID2(
        to,
        next,
        "inmuebleRestringido_id",
        "inmuebleRestringido"
      );
    },
  },
  {
    path: "/Ver-detalle-biblioteca/:biblioteca_id",
    name: "Ver-detalle-biblioteca",
    component: () =>
      import("../views/oti/verInmuebleDetalle/biblioteca_Detalle.vue"),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"],
      },
      requiresResource: {
        isRequired: true,
        resource: ["OTI", "OTI", "Administracion PIU"],
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptParamID2(to, next, "biblioteca_id", "biblioteca");
    },
  },
  {
    path: "/Ver-libro-base-digital/:librosBasesDigitales_id",
    name: "Ver-libro-base-digital",
    component: () =>
      import(
        "../views/oti/verInmuebleDetalle/librosBasesDatosDigitales_Detalle.vue"
      ),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"],
      },
      requiresResource: {
        isRequired: true,
        resource: ["OTI", "OTI", "Administracion PIU"],
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptParamID2(
        to,
        next,
        "librosBasesDigitales_id",
        "librosBasesDigitales"
      );
    },
  },
  {
    path: "/Ver-predio/:predio_id",
    name: "Ver-predio",
    component: () =>
      import("../views/oti/verInmuebleDetalle/predio_Detalle.vue"),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"],
      },
      requiresResource: {
        isRequired: true,
        resource: ["OTI", "OTI", "Administracion PIU"],
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptParamID2(to, next, "predio_id", "predio");
    },
  },
  {
    path: "/Ver-plataforma-virtual/:plataformaVirtual_id",
    name: "Ver-plataforma-virtual",
    component: () =>
      import("../views/oti/verInmuebleDetalle/plataformaVirtual_detalle.vue"),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"],
      },
      requiresResource: {
        isRequired: true,
        resource: ["OTI", "OTI", "Administracion PIU"],
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptParamID2(
        to,
        next,
        "plataformaVirtual_id",
        "plataformaVirtual"
      );
    },
  },
  // Rutas de Matricula Unificada pregrado
  {
    path: "/PP-MU-pregrado",
    name: "PP-MU-pregrado",
    component: () =>
      import("../views/MatriculaUnificada/Pregrado/pp_pregrado.vue"),
    props: false,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"], // CHANGE ROLES
      },
      requiresResource: {
        isRequired: true,
        resource: ["Mu-pregrado", "Mu-pregrado", "Administracion PIU"], // CHANGE RESOURCES
      },
    },
  },
  {
    path: "/PD-MU-pregrado/:anio_proceso/",
    name: "PD-MU-pregrado/",
    component: () =>
      import("../views/MatriculaUnificada/Pregrado/pp_pregrado_detalle.vue"),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"], // CHANGE ROLES
      },
      requiresResource: {
        isRequired: true,
        resource: ["Mu-pregrado", "Mu-pregrado", "Administracion PIU"], // CHANGE RESOURCES
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptAnioProceso(to, next, "anio_proceso", "mu_pregrado");
    },
  },
  {
    path: "/MU-pregrado-fechas/:anio_proceso/:etapa_actual/:is_finalized",
    name: "MU-pregrado-fechas/anio_proceso",
    component: () =>
      import("../views/MatriculaUnificada/Pregrado/pp_fechas_mu_pregrado.vue"),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"], // CHANGE ROLES
      },
      requiresResource: {
        isRequired: true,
        resource: ["Mu-pregrado", "Mu-pregrado", "Administracion PIU"], // CHANGE RESOURCES
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptAnioProceso(to, next, "anio_proceso", "mu_pregrado_fecha");
    },
  },
  // Rutas de Matricula Unificada postgrado postitulo
  {
    path: "/PP-MU-postgrado-postitulo",
    name: "PP-MU-postgrado-postitulo",
    component: () =>
      import(
        "../views/MatriculaUnificada/Postgrado-Postitulo/pp_postgrado_postitulo.vue"
      ),
    props: false,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"], // CHANGE ROLES
      },
      requiresResource: {
        isRequired: true,
        resource: ["Mu-posgrado-postitulo", "Mu-posgrado-postitulo", "Administracion PIU"], // CHANGE RESOURCES
      },
    },
  },

  {
    path: "/PD-MU-postgrado-postitulo/:anio_proceso/",
    name: "PD-MU-postgrado-postitulo/",
    component: () =>
      import("../views/MatriculaUnificada/Postgrado-Postitulo/pp_postgrado_postitulo_detalle.vue"),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"], // CHANGE ROLES
      },
      requiresResource: {
        isRequired: true,
        resource: ["Mu-posgrado-postitulo", "Mu-posgrado-postitulo", "Administracion PIU"], // CHANGE RESOURCES
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptAnioProceso(to, next, "anio_proceso", "mu_postgrado_postitulo");
    },
  },
  {
    path: "/MU-postgrado-postitulo-fechas/:anio_proceso/:etapa_actual/:is_finalized",
    name: "MU-postgrado-postitulo-fechas/anio_proceso",
    component: () =>
      import("../views/MatriculaUnificada/Postgrado-Postitulo/pp_fechas_mu_postgrado.vue"),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Usuario General", "Encargado", "Administrador Sistema"], // CHANGE ROLES
      },
      requiresResource: {
        isRequired: true,
        resource: ["Mu-posgrado-postitulo", "Mu-posgrado-postitulo", "Administracion PIU"], // CHANGE RESOURCES
      },
    },
    beforeEnter: (to, from, next) => {
      validateAndDecryptAnioProceso(to, next, "anio_proceso", "mu_postgrado_postitulo_fecha");
    },
  },
  {
    path: "/oacademicaView",
    name: "oacademicaView",
    component: () => import("../oacademica/views/oacademicaView.vue"),
    meta: {
      requiresAuth: false,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/oacademicaFormView/:oa_sies_id/:etapa",
    name: "oacademicaFormView",
    component: () => import("../oacademica/views/oacademicaFormView.vue"),
    props: true,
    meta: {
      requiresAuth: false,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/oacademicaCreateFormView/",
    name: "oacademicaCreateFormView",
    component: () => import("../oacademica/views/oacademicaCreateFormView.vue"),
    props: true,
    meta: {
      requiresAuth: false,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  //rutas david oferta academica
  {
    path: "/oferta-academica-sies/",
    name: "oferta-academica-sies",
    component: () => import("../views/oferta_academica/pp_oa_procesos.vue"),
    props: true,
    meta: {
      requiresAuth: false,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/oferta-academica-sies-subprocesos/:proceso_id/:anio_proceso",
    name: "oferta-academica-sies-subprocesos",
    component: () => import("../views/oferta_academica/pp_oa_subprocesos.vue"),
    props: true,
    meta: {
      requiresAuth: false,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },
  {
    path: "/oferta-academica-sies-etapas/:subproceso_id/:subproceso/:anio_proceso",
    name: "oferta-academica-sies-etapas",
    component: () => import("../views/oferta_academica/pp_oa_etapas.vue"),
    props: true,
    meta: {
      requiresAuth: false,
      requiresRole: { isRequired: false, role: null },
      requiresResource: { isRequired: false, resource: null },
    },
  },

  // routes for ficha-cna
  {
    path: "/ficha-cna",
    name: "ficha-cna",
    component: () =>
      import("../views/fichaCNA/ficha_cna.vue"),
    props: true,
    meta: {
      requiresAuth: true,
      requiresRole: {
        isRequired: true,
        role: ["Administrador Sistema"],
      },
      requiresResource: {
        isRequired: true,
        resource: ["Administracion PIU"],
      },
    },
  },
  // if the route is not found redirect to the home (if not logged get redirected to the login)
  {
    path: "/:catchAll(.*)",
    redirect: { name: "home" },
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

let isRedirectInProgress = false;

/**
 * Before accessing a route this will be executed
 */
router.beforeEach(async (to, from, next) => {
  // Import the pinia store
  const authStore = useAuthStore();

  // Route variables
  const routeRequiresAuth = to.meta.requiresAuth;
  const routeRequiresRole = to.meta.requiresRole.isRequired;
  const routeRequiresResource = to.meta.requiresResource.isRequired;
  const routeRole = to.meta.requiresRole.role;
  const routeResource = to.meta.requiresResource.resource;

  try {
    // Validate token with the backend
    const userInformation = await checkTokenBackEnd(
      decryptData(authStore.idToken)
    );

    // Prevent authenticated users from accessing the login page
    if (to.name === "login" && userInformation) {
      console.log("User is already authenticated, redirecting to home.");
      return next({ name: "home" }); // Redirect to the home page
    }

    // Check if the route requires authentication
    if (routeRequiresAuth && !userInformation) {
      console.log(userInformation);
      console.log("User is not authenticated");
      authStore.clearAccount();
      return next({ name: "login" }); // Redirect to the login page
    }
    if (userInformation) {
      const isAuthorized = await checkAccess(
        userInformation.user.accesos,
        to.meta.requiresRole,
        to.meta.requiresResource
      );
      // Check if the route requires a specific resource
      if (isAuthorized == false) {
        console.log("User is Unauthorized");
        return next({ name: "Unauthorized" }); // Redirect to the unauthorized page
      }
    }

    if (userInformation) {
      var response = scheduleTokenRefresh();
      if (response === false) {
        return next({ name: "login" });
      }
    }

    // Allow the navigation to proceed
    console.log("proceeding to next route: ", to.name);
    next();
  } catch (error) {
    console.error("Error during authentication check:", error);
    return next({ name: "Error" }); // Redirect to an error page or handle it appropriately
  }
});

/**
 *
 * @param {*} token
 * @returns the user data if the token is valid (to redirect to the requested route) OR null if the token is invalid
 */
async function checkTokenBackEnd(token) {
  if (!token) {
    return false;
  }
  try {
    const response = await axios.get(
      import.meta.env.VITE_BACKEND_BASE_URL + "validateLogin",
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (response.status === 200) {
      return response.data;
    }
  } catch (error) {
    if (error.status === 400) {
      console.error("An error occurred:", error); // Handle the error
      alert("Se perdió la conexión con la base de datos");
      return false;
    }
    console.error("An error occurred:", error); // Handle the error
    return false;
  }
}

async function scheduleTokenRefresh() {
  const authStore = useAuthStore();
  const expirationTime = new Date(
    decryptData(authStore.tokenExpiresOn) * 1000
  ).getTime();
  const currentTime = Math.floor(Date.now()); // Current time in seconds
  var timeLeft = expirationTime - currentTime;
  timeLeft = timeLeft - 5 * 60 * 1000;
  console.log("Current time:", new Date().toLocaleString());
  // Convert the remaining time to hours, minutes, and seconds
  const hours = Math.floor(timeLeft / (1000 * 60 * 60));
  const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

  // Display the remaining time
  console.log(
    `Time remaining: ${hours} hours, ${minutes} minutes, ${seconds} seconds`
  );

  // Clear any existing refresh timeout to prevent accumulation
  if (authStore.refreshTimeout) {
    clearTimeout(authStore.refreshTimeout);
    authStore.clearTimeout();
  }

  if (timeLeft <= 0) {
    authStore.clearStorage();
    authStore.clearAccount();
    alert("Se perdio la conexion con el servidor.");
    return false; // Exit if token has already expired
  }
}

async function checkAccess(userAccesses, requiresRole, requiresResource) {
  if (requiresRole.isRequired && requiresResource.isRequired) {
    // Ensure roles and resources arrays have the same length for comparison
    if (requiresRole.role.length !== requiresResource.resource.length) {
      return false; // Mismatched lengths, cannot proceed
    }

    for (let i = 0; i < requiresRole.role.length; i++) {
      const role = requiresRole.role[i];
      const resource = requiresResource.resource[i];

      // Check if userAccesses contains a matching entry for both role and resource
      const hasAccess = userAccesses.some((access) => {
        return access.rol == role && access.recurso == resource;
      });
      if (hasAccess) {
        return true; // No matching role-resource pair found
      }
    }

    return false; // All role-resource pairs matched and didnt find a match
  }

  return null; // If either requiresRole or requiresResource isn't required, return null
}

export default router;
