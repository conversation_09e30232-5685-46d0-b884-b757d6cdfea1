<script setup lang="ts">
import { toRef } from 'vue';
import type { OAcademica } from '../interfaces/OAcademica';
import mV from '../../utils/validationMessages'
import Panel from 'primevue/panel'
import { Form as VeeForm, useForm } from 'vee-validate'
import * as yup from 'yup'
import { acreditacionOptions, area_actualOptions, area_destinoOPtions, codJornadaOPtions, codNivelGlobaloptions, codTipoPlanCarreraOPtions, elegible_beca_pedagogiaOptions, modalidadOPtions, ped_med_odont_otroOptions, regimenOptions, requisito_ingresoOptions } from '../helpers/selectValuesOacademica';
import InputNumber from 'primevue/inputnumber';



interface Props {
    oAcademica: OAcademica,
    isLoading: boolean
}
interface Emits {
    (event: 'submitForm', values: OAcademica): void;
}
const emits = defineEmits<Emits>();
const props = defineProps<Props>();
const oAcademica = toRef(props, 'oAcademica');
const isLoading = toRef(props, 'isLoading');



// Esquema de validación
const schema = yup.object({
    cod_carrera: yup
        .string()
        .required(mV.required)
        .max(100, mV.max(100))
        .label('Código Carrera'),
    nombre_carrera: yup
        .string()
        .required(mV.required)
        .max(150, mV.max(150))
        .label('Nombre Sede'),
    cod_nivel_global: yup
        .string()
        .required(mV.required)
        .max(150, mV.max(150)),
    cod_nivel_carrera: yup
        .string()
        .required(mV.required),

    cod_demre: yup
        .string()
        .required(mV.required)
        .label('Código DEMRE'),
    anio_inicio: yup
        .string()
        .required(mV.required)
        .label('Año de Inicio'),
    acreditacion: yup
        .string()
        .required(mV.required)
        .label('Acreditación'),
    elegible_beca_pedagogia: yup
        .string()
        .required(mV.required)
        .label('Elegible Beca Pedagogía'),
    ped_med_odont_otro: yup
        .string()
        .required(mV.required)
        .label('Pedagogía, Medicina, Odontología u Otro'),
    requisito_ingreso: yup
        .string()
        .required(mV.required)
        .label('Requisito de Ingreso'),
    semestres_reconocidos: yup
        .string()
        .required(mV.required)
        .label('Semestres Reconocidos'),
    vigencia_carrera: yup
        .string()
        .required(mV.required)
        .label('Vigencia Carrera'),
    cod_sede: yup
        .string()
        .required(mV.required)
        .label('Código Sede'),
    nombre_sede: yup
        .string()
        .required(mV.required)
        .label('Nombre Sede'),

    modalidad: yup
        .string()
        .required(mV.required)
        .label('Modalidad'),
    cod_jornada: yup
        .string()
        .required(mV.required)
        .label('Código Jornada'),
    version: yup
        .string()
        .required(mV.required)
        .label('Versión'),

    duracion_estudios: yup
        .string()
        .required(mV.required)
        .label('Duración Estudios'),
    duracion_titulacion: yup
        .string()
        .required(mV.required)
        .label('Duración Titulación'),
    duracion_total: yup
        .string()
        .required(mV.required)
        .label('Duración Total'),
    regimen: yup
        .string()
        .required(mV.required)
        .label('Régimen'),
    duracion_regimen: yup
        .string()
        .required(mV.required)
        .label('Duración Régimen'),

    nombre_titulo: yup
        .string()
        .required(mV.required)
        .label('Nombre Título'),
    nombre_grado: yup
        .string()
        .required(mV.required)
        .label('Nombre Grado'),

    area_actual: yup
        .string()
        .required(mV.required)
        .label('Área Actual'),
    area_destino_agricultura: yup
        .string()
        .required(mV.required)
        .label('Área Destino Agricultura'),
    area_destino_ciencias: yup
        .string()
        .required(mV.required)
        .label('Área Destino Ciencias'),
    area_destino_cs_sociales: yup
        .string()
        .required(mV.required)
        .label('Área Destino Ciencias Sociales'),
    area_destino_educacion: yup
        .string()
        .required(mV.required)
        .label('Área Destino Educación'),
    area_destino_humanidades: yup
        .string()
        .required(mV.required)
        .label('Área Destino Humanidades'),
    area_destino_ingenieria: yup
        .string()
        .required(mV.required)
        .label('Área Destino Ingeniería'),
    area_destino_salud: yup
        .string()
        .required(mV.required)
        .label('Área Destino Salud'),
    area_destino_servicios: yup
        .string()
        .required(mV.required)
        .label('Área Destino Servicios'),

    ponderacion_nem: yup
        .number()
        .required(mV.required)
        .label('Ponderación NEM'),
    ponderacion_ranking: yup
        .number()
        .required(mV.required)
        .label('Ponderación Ranking'),
    ponderacion_c_lectora: yup
        .number()
        .required(mV.required)
        .label('Ponderación Comprensión Lectora'),
    ponderacion_matematica_1: yup
        .number()
        .required(mV.required)
        .label('Ponderación Matemáticas 1'),
    ponderacion_matematica_2: yup
        .number()
        .required(mV.required)
        .label('Ponderación Matemáticas 2'),
    ponderacion_historia: yup
        .number()
        .required(mV.required)
        .label('Ponderación Historia'),
    ponderacion_ciencias: yup
        .number()
        .required(mV.required)
        .label('Ponderación Ciencias'),
    ponderacion_otros: yup
        .number()
        .required(mV.required)
        .label('Ponderación Otros'),

    vacantes_primer_semestre: yup
        .number()
        .required(mV.required)
        .label('Vacantes Primer Semestre'),
    vacantes_segundo_semestre: yup
        .number()
        .required(mV.required)
        .label('Vacantes Segundo Semestre'),
    vacantes_pace: yup
        .number()
        .required(mV.required)
        .label('Vacantes PACE'),

    cod_tipo_plan_carrera: yup
        .string()
        .required(mV.required)
        .label('Código Tipo Plan Carrera'),
    caracteristicas_tipo_plan: yup
        .string()
        .required(mV.required)
        .label('Características Tipo Plan'),
    malla_curricular: yup
        .string()
        .required(mV.required)
        .label('Malla Curricular'),
    perfil_egreso: yup
        .string()
        .required(mV.required)
        .label('Perfil de Egreso'),
    texto_requerido_ingreso: yup
        .string()
        .required(mV.required)
        .label('Texto Requisito de Ingreso'),
    otros_requisitos: yup
        .string()
        .required(mV.required)
        .label('Otros Requisitos'),
    mail_difusion_carrera: yup
        .string()
        .required(mV.required)
        .label('Mail Difusión Carrera'),
    formato_valor: yup
        .string()
        .required(mV.required)
        .label('Formato Valor'),
    valor_matricula_anual: yup
        .string()
        .required(mV.required)
        .label('Valor Matrícula Anual'),
    costo_titulacion: yup
        .string()
        .required(mV.required)
        .label('Costo Titulación'),
    valor_certificado_diploma: yup
        .string()
        .required(mV.required)
        .label('Valor Certificado Diploma'),
});

// Configuración del formulario
const { handleSubmit, resetForm, setValues, setFieldValue, errors } = useForm({
    validationSchema: schema,
    initialValues: oAcademica.value, // Usamos los valores iniciales desde las props
});
// Manejador del formulario
const onSubmit = handleSubmit((values) => {
    console.log('Valores validados:', values);
    emits('submitForm', values); // Emitimos los datos validados al padre
});


</script>
<template>


    <div>

        <!-- <VeeForm @submit.prevent="emits('submitForm')" :initial_values="oAcademica"> -->
        <VeeForm @submit="onSubmit">
            <!-- Panel para la tabla Carrera -->
            <Panel header="CARRERA" id="carrera">
                <div class="formgrid grid">

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="cod_carrera">Código Carrera</label>
                            <InputText name="cod_carrera" id="cod_carrera" type="text" v-model="oAcademica.cod_carrera"
                                :class="{ 'p-invalid': errors.cod_carrera }"
                                @input="setFieldValue('cod_carrera', oAcademica.cod_carrera)" class="w-full" />
                            <small v-if="errors.cod_carrera" class="p-error">{{ errors.cod_carrera }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="nombre_carrera">Nombre Carrera</label>
                            <InputText id="nombre_carrera" type="text" v-model="oAcademica.nombre_carrera"
                                class="w-full" :class="{ 'p-invalid': errors.nombre_carrera }"
                                @input="setFieldValue('nombre_carrera', oAcademica.nombre_carrera)" />
                            <small v-if="errors.nombre_carrera" class="p-error">{{ errors.nombre_carrera }}</small>

                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="cod_nivel_global" v-model="oAcademica.cod_nivel_global"
                                :options="codNivelGlobaloptions" optionLabel="name" optionValue="value"
                                :class="{ 'p-invalid': errors.cod_nivel_global }"
                                @update:modelValue="setFieldValue('cod_nivel_global', oAcademica.cod_nivel_global)"
                                class="w-full" />
                            <label for="cod_nivel_global">Código Nivel Global</label>
                            <small v-if="errors.cod_nivel_global" class="p-error">{{ errors.cod_nivel_global }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="cod_nivel_carrera" v-model="oAcademica.cod_nivel_carrera"
                                :options="codNivelGlobaloptions" optionLabel="name" optionValue="value"
                                :class="{ 'p-invalid': errors.cod_nivel_carrera }"
                                @update:modelValue="setFieldValue('cod_nivel_carrera', oAcademica.cod_nivel_carrera)"
                                class="w-full" />
                            <label for="cod_nivel_carrera">Código Nivel Carrera</label>
                            <small v-if="errors.cod_nivel_carrera" class="p-error">{{ errors.cod_nivel_carrera
                                }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="cod_demre">Código DEMRE</label>
                            <InputText id="cod_demre" type="text" v-model="oAcademica.cod_demre" class="w-full"
                                :class="{ 'p-invalid': errors.cod_demre }"
                                @input="setFieldValue('cod_demre', oAcademica.cod_demre)" />
                            <small v-if="errors.cod_demre" class="p-error">{{ errors.cod_demre }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="anio_inicio">Año de Inicio</label>
                            <InputText id="anio_inicio" type="text" v-model="oAcademica.anio_inicio" class="w-full"
                                :class="{ 'p-invalid': errors.anio_inicio }"
                                @input="setFieldValue('anio_inicio', oAcademica.anio_inicio)" />
                            <small v-if="errors.anio_inicio" class="p-error">{{ errors.anio_inicio }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="acreditacion" v-model="oAcademica.acreditacion" :options="acreditacionOptions"
                                optionLabel="name" optionValue="value" :class="{ 'p-invalid': errors.acreditacion }"
                                @update:modelValue="setFieldValue('acreditacion', oAcademica.acreditacion)"
                                class="w-full" />
                            <label for="acreditacion">Acreditación</label>
                            <small v-if="errors.acreditacion" class="p-error">{{ errors.acreditacion }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">

                        <FloatLabel>
                            <Dropdown id="elegible_beca_pedagogia" v-model="oAcademica.elegible_beca_pedagogia"
                                :options="elegible_beca_pedagogiaOptions" optionLabel="name" optionValue="value"
                                :class="{ 'p-invalid': errors.elegible_beca_pedagogia }"
                                @update:modelValue="setFieldValue('elegible_beca_pedagogia', oAcademica.elegible_beca_pedagogia)"
                                class="w-full" />
                            <label for="elegible_beca_pedagogia">Elegible Beca Pedagogía</label>
                            <small v-if="errors.elegible_beca_pedagogia" class="p-error">{{
                                errors.elegible_beca_pedagogia
                                }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="ped_med_odont_otro" v-model="oAcademica.ped_med_odont_otro"
                                :options="ped_med_odont_otroOptions" optionLabel="name" optionValue="value"
                                :class="{ 'p-invalid': errors.ped_med_odont_otro }"
                                @update:modelValue="setFieldValue('ped_med_odont_otro', oAcademica.ped_med_odont_otro)"
                                class="w-full" />
                            <label for="ped_med_odont_otro">Pedagogía, Medicina, Odontología u Otro</label>
                            <small v-if="errors.ped_med_odont_otro" class="p-error">{{ errors.ped_med_odont_otro
                            }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="requisito_ingreso" v-model="oAcademica.requisito_ingreso"
                                :options="requisito_ingresoOptions" optionLabel="name" optionValue="value"
                                :class="{ 'p-invalid': errors.requisito_ingreso }"
                                @update:modelValue="setFieldValue('requisito_ingreso', oAcademica.requisito_ingreso)"
                                class="w-full" />
                            <label for="requisito_ingreso">Requisito de Ingreso</label>
                            <small v-if="errors.requisito_ingreso" class="p-error">{{ errors.requisito_ingreso
                                }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="semestres_reconocidos">Semestres Reconocidos</label>
                            <InputNumber id="semestres_reconocidos" type="text"
                                v-model="oAcademica.semestres_reconocidos" class="w-full"
                                :class="{ 'p-invalid': errors.semestres_reconocidos }"
                                @input="setFieldValue('semestres_reconocidos', oAcademica.semestres_reconocidos)" />
                            <small v-if="errors.semestres_reconocidos" class="p-error">{{ errors.semestres_reconocidos
                                }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="vigencia_carrera">Vigencia Carrera</label>
                            <InputText id="vigencia_carrera" type="text" v-model="oAcademica.vigencia_carrera"
                                class="w-full" :class="{ 'p-invalid': errors.vigencia_carrera }"
                                @input="setFieldValue('vigencia_carrera', oAcademica.vigencia_carrera)" />
                            <small v-if="errors.vigencia_carrera" class="p-error">{{ errors.vigencia_carrera }}</small>
                        </FloatLabel>
                    </div>
                </div>
            </Panel>

            <!-- Panel para la tabla Sede -->
            <Panel header="SEDE" id="sede" class="mt-8">
                <div class="formgrid grid">
                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="cod_sede">Código Sede</label>
                            <InputText id="cod_sede" type="text" v-model="oAcademica.cod_sede" class="w-full"
                                :class="{ 'p-invalid': errors.cod_sede }"
                                @input="setFieldValue('cod_sede', oAcademica.cod_sede)" />
                            <small v-if="errors.cod_sede" class="p-error">{{ errors.cod_sede }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="nombre_sede">Nombre Sede</label>
                            <InputText id="nombre_sede" type="text" v-model="oAcademica.nombre_sede" class="w-full"
                                :class="{ 'p-invalid': errors.nombre_sede }"
                                @input="setFieldValue('nombre_sede', oAcademica.nombre_sede)" />
                            <small v-if="errors.nombre_sede" class="p-error">{{ errors.nombre_sede }}</small>
                        </FloatLabel>
                    </div>
                </div>
            </Panel>

            <!-- Panel para la tabla Sies -->
            <Panel header="SIES" id="sies" class="mt-8">
                <div class="formgrid grid">
                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="modalidad" v-model="oAcademica.modalidad" :options="modalidadOPtions"
                                optionLabel="name" optionValue="value" :class="{ 'p-invalid': errors.modalidad }"
                                @update:modelValue="setFieldValue('modalidad', oAcademica.modalidad)" class="w-full" />
                            <label for="modalidad">Modalidad</label>
                            <small v-if="errors.modalidad" class="p-error">{{ errors.modalidad }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="cod_jornada" v-model="oAcademica.cod_jornada" :options="codJornadaOPtions"
                                optionLabel="name" optionValue="value" :class="{ 'p-invalid': errors.cod_jornada }"
                                @update:modelValue="setFieldValue('cod_jornada', oAcademica.cod_jornada)"
                                class="w-full" />
                            <label for="cod_jornada">Código Jornada</label>
                            <small v-if="errors.cod_jornada" class="p-error">{{ errors.cod_jornada }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="version">Versión</label>
                            <InputText id="version" type="text" v-model="oAcademica.version" class="w-full"
                                :class="{ 'p-invalid': errors.version }"
                                @input="setFieldValue('version', oAcademica.version)" />
                            <small v-if="errors.version" class="p-error">{{ errors.version }}</small>
                        </FloatLabel>
                    </div>
                </div>
            </Panel>

            <!-- Panel para la tabla Duracion -->
            <Panel header="DURACIÓN" id="duracion" class="mt-8">
                <div class="formgrid grid">
                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="duracion_estudios">Duración Estudios</label>
                            <InputText id="duracion_estudios" type="text" v-model="oAcademica.duracion_estudios"
                                class="w-full" :class="{ 'p-invalid': errors.duracion_estudios }"
                                @input="setFieldValue('duracion_estudios', oAcademica.duracion_estudios)" />
                            <small v-if="errors.duracion_estudios" class="p-error">{{ errors.duracion_estudios
                                }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="duracion_titulacion">Duración Titulación</label>
                            <InputText id="duracion_titulacion" type="text" v-model="oAcademica.duracion_titulacion"
                                class="w-full" :class="{ 'p-invalid': errors.duracion_titulacion }"
                                @input="setFieldValue('duracion_titulacion', oAcademica.duracion_titulacion)" />
                            <small v-if="errors.duracion_titulacion" class="p-error">{{ errors.duracion_titulacion
                                }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="duracion_total">Duración Total</label>
                            <InputText id="duracion_total" type="text" v-model="oAcademica.duracion_total"
                                class="w-full" :class="{ 'p-invalid': errors.duracion_total }"
                                @input="setFieldValue('duracion_total', oAcademica.duracion_total)" />
                            <small v-if="errors.duracion_total" class="p-error">{{ errors.duracion_total }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="regimen" v-model="oAcademica.regimen" :options="regimenOptions"
                                optionLabel="name" optionValue="value" :class="{ 'p-invalid': errors.regimen }"
                                @update:modelValue="setFieldValue('regimen', oAcademica.regimen)" class="w-full" />
                            <label for="regimen">Régimen</label>
                            <small v-if="errors.regimen" class="p-error">{{ errors.regimen }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="duracion_regimen">Duración Régimen</label>
                            <InputNumber id="duracion_regimen" type="text" v-model="oAcademica.duracion_regimen"
                                class="w-full" :class="{ 'p-invalid': errors.duracion_regimen }"
                                @input="setFieldValue('duracion_regimen', oAcademica.duracion_regimen)" />
                            <small v-if="errors.duracion_regimen" class="p-error">{{ errors.duracion_regimen }}</small>
                        </FloatLabel>
                    </div>
                </div>
            </Panel>

            <!-- Panel para la tabla Perfil Profesional -->
            <Panel header="PERFIL PROFESIONAL" id="perfilprof" class="mt-8">
                <div class="formgrid grid">
                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="nombre_titulo">Nombre Título</label>
                            <InputText id="nombre_titulo" type="text" v-model="oAcademica.nombre_titulo" class="w-full"
                                :class="{ 'p-invalid': errors.nombre_titulo }"
                                @input="setFieldValue('nombre_titulo', oAcademica.nombre_titulo)" />
                            <small v-if="errors.nombre_titulo" class="p-error">{{ errors.nombre_titulo }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="nombre_grado">Nombre Grado</label>
                            <InputText id="nombre_grado" type="text" v-model="oAcademica.nombre_grado" class="w-full"
                                :class="{ 'p-invalid': errors.nombre_grado }"
                                @input="setFieldValue('nombre_grado', oAcademica.nombre_grado)" />
                            <small v-if="errors.nombre_grado" class="p-error">{{ errors.nombre_grado }}</small>
                        </FloatLabel>
                    </div>
                </div>
            </Panel>

            <!-- Panel para la tabla Área -->
            <Panel header="ÁREA" id="area" class="mt-8">
                <div class="formgrid grid">
                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="area_actual" v-model="oAcademica.area_actual" :options="area_actualOptions"
                                optionLabel="name" optionValue="value" :class="{ 'p-invalid': errors.area_actual }"
                                @update:modelValue="setFieldValue('area_actual', oAcademica.area_actual)"
                                class="w-full" />
                            <label for="area_actual">Área Actual</label>
                            <small v-if="errors.area_actual" class="p-error">{{ errors.area_actual }}</small>

                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="area_destino_agricultura" v-model="oAcademica.area_destino_agricultura"
                                :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                                :class="{ 'p-invalid': errors.area_destino_agricultura }"
                                @update:modelValue="setFieldValue('area_destino_agricultura', oAcademica.area_destino_agricultura)"
                                class="w-full" />
                            <label for="area_destino_agricultura">Área Destino Agricultura</label>
                            <small v-if="errors.area_destino_agricultura" class="p-error">{{
                                errors.area_destino_agricultura }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="area_destino_ciencias" v-model="oAcademica.area_destino_ciencias"
                                :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                                :class="{ 'p-invalid': errors.area_destino_ciencias }"
                                @update:modelValue="setFieldValue('area_destino_ciencias', oAcademica.area_destino_ciencias)"
                                class="w-full" />
                            <label for="area_destino_ciencias">Área Destino Ciencias</label>
                            <small v-if="errors.area_destino_ciencias" class="p-error">{{ errors.area_destino_ciencias
                            }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="area_destino_cs_sociales" v-model="oAcademica.area_destino_cs_sociales"
                                :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                                :class="{ 'p-invalid': errors.area_destino_cs_sociales }"
                                @update:modelValue="setFieldValue('area_destino_cs_sociales', oAcademica.area_destino_cs_sociales)"
                                class="w-full" />
                            <label for="area_destino_cs_sociales">Área Destino Ciencias Sociales</label>
                            <small v-if="errors.area_destino_cs_sociales" class="p-error">{{
                                errors.area_destino_cs_sociales }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="area_destino_educacion" v-model="oAcademica.area_destino_educacion"
                                :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                                :class="{ 'p-invalid': errors.area_destino_educacion }"
                                @update:modelValue="setFieldValue('area_destino_educacion', oAcademica.area_destino_educacion)"
                                class="w-full" />
                            <label for="area_destino_educacion">Área Destino Educación</label>
                            <small v-if="errors.area_destino_educacion" class="p-error">{{ errors.area_destino_educacion
                            }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="area_destino_humanidades" v-model="oAcademica.area_destino_humanidades"
                                :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                                :class="{ 'p-invalid': errors.area_destino_humanidades }"
                                @update:modelValue="setFieldValue('area_destino_humanidades', oAcademica.area_destino_humanidades)"
                                class="w-full" />
                            <label for="area_destino_humanidades">Área Destino Humanidades</label>
                            <small v-if="errors.area_destino_humanidades" class="p-error">{{
                                errors.area_destino_humanidades }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="area_destino_ingenieria" v-model="oAcademica.area_destino_ingenieria"
                                :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                                :class="{ 'p-invalid': errors.area_destino_ingenieria }"
                                @update:modelValue="setFieldValue('area_destino_ingenieria', oAcademica.area_destino_ingenieria)"
                                class="w-full" />
                            <label for="area_destino_ingenieria">Área Destino Ingeniería</label>
                            <small v-if="errors.area_destino_ingenieria" class="p-error">{{
                                errors.area_destino_ingenieria }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="area_destino_salud" v-model="oAcademica.area_destino_salud"
                                :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                                :class="{ 'p-invalid': errors.area_destino_salud }"
                                @update:modelValue="setFieldValue('area_destino_salud', oAcademica.area_destino_salud)"
                                class="w-full" />
                            <label for="area_destino_salud">Área Destino Salud</label>
                            <small v-if="errors.area_destino_salud" class="p-error">{{ errors.area_destino_salud
                                }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="area_destino_servicios" v-model="oAcademica.area_destino_servicios"
                                :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                                :class="{ 'p-invalid': errors.area_destino_servicios }"
                                @update:modelValue="setFieldValue('area_destino_servicios', oAcademica.area_destino_servicios)"
                                class="w-full" />
                            <label for="area_destino_servicios">Área Destino Servicios</label>
                            <small v-if="errors.area_destino_servicios" class="p-error">{{ errors.area_destino_servicios
                            }}</small>
                        </FloatLabel>
                    </div>

                </div>
            </Panel>

            <!-- Panel para la tabla Ponderación -->
            <Panel header="PONDERACIÓN" id="ponderacion" class="mt-8">
                <div class="formgrid grid">
                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="ponderacion_nem">Ponderación NEM</label>
                            <InputNumber id="ponderacion_nem" type="text" v-model="oAcademica.ponderacion_nem"
                                class="w-full" :class="{ 'p-invalid': errors.ponderacion_nem }"
                                @input="setFieldValue('ponderacion_nem', oAcademica.ponderacion_nem)" />
                            <small v-if="errors.ponderacion_nem" class="p-error">{{ errors.ponderacion_nem }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="ponderacion_ranking">Ponderación Ranking</label>
                            <InputNumber id="ponderacion_ranking" type="text" v-model="oAcademica.ponderacion_ranking"
                                class="w-full" :class="{ 'p-invalid': errors.ponderacion_ranking }"
                                @input="setFieldValue('ponderacion_ranking', oAcademica.ponderacion_ranking)" />
                            <small v-if="errors.ponderacion_ranking" class="p-error">{{ errors.ponderacion_ranking
                                }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="ponderacion_c_lectora">Ponderación Comprensión Lectora</label>
                            <InputNumber id="ponderacion_c_lectora" type="text" v-model="oAcademica.ponderacion_c_lectora"
                                class="w-full" :class="{ 'p-invalid': errors.ponderacion_c_lectora }"
                                @input="setFieldValue('ponderacion_c_lectora', oAcademica.ponderacion_c_lectora)" />
                            <small v-if="errors.ponderacion_c_lectora" class="p-error">{{ errors.ponderacion_c_lectora
                                }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="ponderacion_matematica_1">Ponderación Matemáticas 1</label>
                            <InputNumber id="ponderacion_matematica_1" type="text"
                                v-model="oAcademica.ponderacion_matematica_1" class="w-full"
                                :class="{ 'p-invalid': errors.ponderacion_matematica_1 }"
                                @input="setFieldValue('ponderacion_matematica_1', oAcademica.ponderacion_matematica_1)" />
                            <small v-if="errors.ponderacion_matematica_1" class="p-error">{{
                                errors.ponderacion_matematica_1 }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="ponderacion_matematica_2">Ponderación Matemáticas 2</label>
                            <InputNumber id="ponderacion_matematica_2" type="text"
                                v-model="oAcademica.ponderacion_matematica_2" class="w-full"
                                :class="{ 'p-invalid': errors.ponderacion_matematica_2 }"
                                @input="setFieldValue('ponderacion_matematica_2', oAcademica.ponderacion_matematica_2)" />
                            <small v-if="errors.ponderacion_matematica_2" class="p-error">{{
                                errors.ponderacion_matematica_2 }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="ponderacion_historia">Ponderación Historia</label>
                            <InputNumber id="ponderacion_historia" type="text" v-model="oAcademica.ponderacion_historia"
                                class="w-full" :class="{ 'p-invalid': errors.ponderacion_historia }"
                                @input="setFieldValue('ponderacion_historia', oAcademica.ponderacion_historia)" />
                            <small v-if="errors.ponderacion_historia" class="p-error">{{ errors.ponderacion_historia
                                }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="ponderacion_ciencias">Ponderación Ciencias</label>
                            <InputNumber id="ponderacion_ciencias" type="text" v-model="oAcademica.ponderacion_ciencias"
                                class="w-full" :class="{ 'p-invalid': errors.ponderacion_ciencias }"
                                @input="setFieldValue('ponderacion_ciencias', oAcademica.ponderacion_ciencias)" />
                            <small v-if="errors.ponderacion_ciencias" class="p-error">{{ errors.ponderacion_ciencias
                                }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="ponderacion_otros">Ponderación Otros</label>
                            <InputNumber id="ponderacion_otros" type="text" v-model="oAcademica.ponderacion_otros"
                                class="w-full" :class="{ 'p-invalid': errors.ponderacion_otros }"
                                @input="setFieldValue('ponderacion_otros', oAcademica.ponderacion_otros)" />
                            <small v-if="errors.ponderacion_otros" class="p-error">{{ errors.ponderacion_otros
                                }}</small>
                        </FloatLabel>
                    </div>

                </div>
            </Panel>

            <!-- Panel para la tabla Vacantes -->
            <Panel header="VACANTES" id="vacantes" class="mt-8">
                <div class="formgrid grid">
                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="vacantes_primer_semestre">Vacantes Primer Semestre</label>
                            <InputText id="vacantes_primer_semestre" type="text"
                                v-model="oAcademica.vacantes_primer_semestre" class="w-full"
                                :class="{ 'p-invalid': errors.vacantes_primer_semestre }"
                                @input="setFieldValue('vacantes_primer_semestre', oAcademica.vacantes_primer_semestre)" />
                            <small v-if="errors.vacantes_primer_semestre" class="p-error">{{
                                errors.vacantes_primer_semestre }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="vacantes_segundo_semestre">Vacantes Segundo Semestre</label>
                            <InputText id="vacantes_segundo_semestre" type="text"
                                v-model="oAcademica.vacantes_segundo_semestre" class="w-full"
                                :class="{ 'p-invalid': errors.vacantes_segundo_semestre }"
                                @input="setFieldValue('vacantes_segundo_semestre', oAcademica.vacantes_segundo_semestre)" />
                            <small v-if="errors.vacantes_segundo_semestre" class="p-error">{{
                                errors.vacantes_segundo_semestre }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="vacantes_pace">Vacantes PACE</label>
                            <InputNumber id="vacantes_pace" type="text" v-model="oAcademica.vacantes_pace" class="w-full"
                                :class="{ 'p-invalid': errors.vacantes_pace }"
                                @input="setFieldValue('vacantes_pace', oAcademica.vacantes_pace)" />
                            <small v-if="errors.vacantes_pace" class="p-error">{{ errors.vacantes_pace }}</small>
                        </FloatLabel>
                    </div>

                </div>
            </Panel>

            <!-- Panel para la tabla Otros -->
            <Panel header="OTROS" id="otros" class="mt-8">
                <div class="formgrid grid">
                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <Dropdown id="cod_tipo_plan_carrera" v-model="oAcademica.cod_tipo_plan_carrera"
                                :options="codTipoPlanCarreraOPtions" optionLabel="name" optionValue="value"
                                :class="{ 'p-invalid': errors.cod_tipo_plan_carrera }"
                                @update:modelValue="setFieldValue('cod_tipo_plan_carrera', oAcademica.cod_tipo_plan_carrera)"
                                class="w-full" />
                            <label for="cod_tipo_plan_carrera">Código Tipo Plan Carrera</label>
                            <small v-if="errors.cod_tipo_plan_carrera" class="p-error">{{ errors.cod_tipo_plan_carrera
                                }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="caracteristicas_tipo_plan">Características Tipo Plan</label>
                            <InputText id="caracteristicas_tipo_plan" type="text"
                                v-model="oAcademica.caracteristicas_tipo_plan" class="w-full"
                                :class="{ 'p-invalid': errors.caracteristicas_tipo_plan }"
                                @input="setFieldValue('caracteristicas_tipo_plan', oAcademica.caracteristicas_tipo_plan)" />
                            <small v-if="errors.caracteristicas_tipo_plan" class="p-error">{{
                                errors.caracteristicas_tipo_plan }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="vacantes_pace">Vacantes PACE</label>
                            <InputNumber id="vacantes_pace" type="text" v-model="oAcademica.vacantes_pace" class="w-full"
                                :class="{ 'p-invalid': errors.vacantes_pace }"
                                @input="setFieldValue('vacantes_pace', oAcademica.vacantes_pace)" />
                            <small v-if="errors.vacantes_pace" class="p-error">{{ errors.vacantes_pace }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="malla_curricular">Malla Curricular</label>
                            <InputText id="malla_curricular" type="text" v-model="oAcademica.malla_curricular"
                                class="w-full" :class="{ 'p-invalid': errors.malla_curricular }"
                                @input="setFieldValue('malla_curricular', oAcademica.malla_curricular)" />
                            <small v-if="errors.malla_curricular" class="p-error">{{ errors.malla_curricular }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="perfil_egreso">Perfil de Egreso</label>
                            <InputText id="perfil_egreso" type="text" v-model="oAcademica.perfil_egreso" class="w-full"
                                :class="{ 'p-invalid': errors.perfil_egreso }"
                                @input="setFieldValue('perfil_egreso', oAcademica.perfil_egreso)" />
                            <small v-if="errors.perfil_egreso" class="p-error">{{ errors.perfil_egreso }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="texto_requerido_ingreso">Texto Requisito de Ingreso</label>
                            <InputText id="texto_requerido_ingreso" type="text"
                                v-model="oAcademica.texto_requerido_ingreso" class="w-full"
                                :class="{ 'p-invalid': errors.texto_requerido_ingreso }"
                                @input="setFieldValue('texto_requerido_ingreso', oAcademica.texto_requerido_ingreso)" />
                            <small v-if="errors.texto_requerido_ingreso" class="p-error">{{
                                errors.texto_requerido_ingreso }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="otros_requisitos">Otros Requisitos</label>
                            <InputText id="otros_requisitos" type="text" v-model="oAcademica.otros_requisitos"
                                class="w-full" :class="{ 'p-invalid': errors.otros_requisitos }"
                                @input="setFieldValue('otros_requisitos', oAcademica.otros_requisitos)" />
                            <small v-if="errors.otros_requisitos" class="p-error">{{ errors.otros_requisitos }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="mail_difusion_carrera">Mail Difusión Carrera</label>
                            <InputText id="mail_difusion_carrera" type="text" v-model="oAcademica.mail_difusion_carrera"
                                class="w-full" :class="{ 'p-invalid': errors.mail_difusion_carrera }"
                                @input="setFieldValue('mail_difusion_carrera', oAcademica.mail_difusion_carrera)" />
                            <small v-if="errors.mail_difusion_carrera" class="p-error">{{ errors.mail_difusion_carrera
                                }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="formato_valor">Formato Valor</label>
                            <InputText id="formato_valor" type="text" v-model="oAcademica.formato_valor" class="w-full"
                                :class="{ 'p-invalid': errors.formato_valor }"
                                @input="setFieldValue('formato_valor', oAcademica.formato_valor)" />
                            <small v-if="errors.formato_valor" class="p-error">{{ errors.formato_valor }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="valor_matricula_anual">Valor Matrícula Anual</label>
                            <InputNumber id="valor_matricula_anual" type="text" v-model="oAcademica.valor_matricula_anual"
                                class="w-full" :class="{ 'p-invalid': errors.valor_matricula_anual }"
                                @input="setFieldValue('valor_matricula_anual', oAcademica.valor_matricula_anual)" />
                            <small v-if="errors.valor_matricula_anual" class="p-error">{{ errors.valor_matricula_anual
                                }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="costo_titulacion">Costo Titulación</label>
                            <InputNumber id="costo_titulacion" type="text" v-model="oAcademica.costo_titulacion"
                                class="w-full" :class="{ 'p-invalid': errors.costo_titulacion }"
                                @input="setFieldValue('costo_titulacion', oAcademica.costo_titulacion)" />
                            <small v-if="errors.costo_titulacion" class="p-error">{{ errors.costo_titulacion }}</small>
                        </FloatLabel>
                    </div>

                    <div class="field col-12 md:col-6 lg:col-3 mt-3">
                        <FloatLabel>
                            <label for="valor_certificado_diploma">Valor Certificado Diploma</label>
                            <InputNumber id="valor_certificado_diploma" type="text"
                                v-model="oAcademica.valor_certificado_diploma" class="w-full"
                                :class="{ 'p-invalid': errors.valor_certificado_diploma }"
                                @input="setFieldValue('valor_certificado_diploma', oAcademica.valor_certificado_diploma)" />
                            <small v-if="errors.valor_certificado_diploma" class="p-error">{{
                                errors.valor_certificado_diploma }}</small>
                        </FloatLabel>
                    </div>

                </div>
            </Panel>
            <Panel>
                <Button label="submit" type="submit" :disabled="isLoading">GUARDAR</Button>
            </Panel>
        </VeeForm>

    </div>
</template>


<style lang="scss" scoped></style>