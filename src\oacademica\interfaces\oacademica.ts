export interface OAcademica {
  oa_sies_id: string //carrera
  cod_sede: string //sed
  nombre_sede: string //sede
  cod_carrera: string //carrera
  nombre_carrera: string //carrera
  modalidad: string //sies
  cod_jornada: string //sies
  version: string //sies
  cod_tipo_plan_carrera: string //consultar
  caracteristicas_tipo_plan: string //consultar
  duracion_estudios: string //duracion
  duracion_titulacion: string //duracion
  duracion_total: string //duracion
  regimen: number //duracion
  duracion_regimen: number //duracion
  nombre_titulo: string //perfil academico
  nombre_grado: string //perfil academico
  cod_nivel_global: string //Carrera
  cod_nivel_carrera: string //Carrera
  cod_demre: string //Carrera
  anio_inicio: string //Carrera
  acreditacion: number //Carrera
  elegible_beca_pedagogia: number //Carrera
  ped_med_odont_otro: string //Carrera
  requisito_ingreso: number //Carrera
  semestres_reconocidos: number //Carrera
  area_actual: string //area
  area_destino_agricultura: number //area
  area_destino_ciencias: number //area
  area_destino_cs_sociales: number //area
  area_destino_educacion: number //area
  area_destino_humanidades: number //area
  area_destino_ingenieria: number //area
  area_destino_salud: number //area
  area_destino_servicios: number //area
  ponderacion_nem: number //ponderacion
  ponderacion_ranking: number //ponderacion
  ponderacion_c_lectora: number //ponderacion
  ponderacion_matematica_1: number //ponderacion
  ponderacion_matematica_2: number //ponderacion
  ponderacion_historia: number //ponderacion
  ponderacion_ciencias: number //ponderacion
  ponderacion_otros: number //ponderacion
  vacantes_primer_semestre: string //vacantes
  vacantes_segundo_semestre: string //vacantes
  vacantes_pace: number //] consultar
  malla_curricular: string
  perfil_egreso: string
  texto_requerido_ingreso: string
  otros_requisitos: string
  mail_difusion_carrera: string
  formato_valor: string
  valor_matricula_anual: number
  costo_titulacion: number
  valor_certificado_diploma: number
  arancel_anual: number //arancel
  vigencia_carrera: string //carrera
}

export interface OAcademicaEtapa1Pregrado {
  oa_sies_id: string 
  acreditacion: number
  elegible_beca_pedagogia: number
  requisito_ingreso: number
  semestres_reconocidos: number
  area_destino_agricultura: number
  area_destino_ciencias: number
  area_destino_cs_sociales: number
  area_destino_educacion: number
  area_destino_humanidades: number
  area_destino_ingenieria: number
  area_destino_salud: number
  area_destino_servicios: number
  malla_curricular: string
  mail_difusion_carrera: string
}
export interface OAcademicaEtapa3Pregrado {
  oa_sies_id: string 
  acreditacion: number
  malla_curricular: string
  mail_difusion_carrera: string
  formato_valor: string
  valor_matricula_anual: number
  costo_titulacion: number
  valor_certificado_diploma: number
  arancel_anual: number
}
export interface OAcademicaEtapa1PosgradoPostitulo {
  oa_sies_id: string
  regimen: number
  duracion_regimen: number
  cod_demre: string
  acreditacion: number
  elegible_beca_pedagogia: number
  requisito_ingreso: number
  semestres_reconocidos: number
  area_destino_agricultura: number
  area_destino_ciencias: number
  area_destino_cs_sociales: number
  area_destino_educacion: number
  area_destino_humanidades: number
  area_destino_ingenieria: number
  area_destino_salud: number
  area_destino_servicios: number
  ponderacion_nem: number
  ponderacion_ranking: number
  ponderacion_c_lectora: number
  ponderacion_matematica_1: number
  ponderacion_matematica_2: number
  ponderacion_historia: number
  ponderacion_ciencias: number
  ponderacion_otros: number
  vacantes_pace: number
  malla_curricular: string
  perfil_egreso: string
  texto_requerido_ingreso: string
  otros_requisitos: string
  mail_difusion_carrera: string
  vigencia_carrera: string
}
export interface OAcademicaEtapa3PosgradoPostitulo{
  oa_sies_id: string 
  acreditacion: number
  malla_curricular: string
  mail_difusion_carrera: string
  formato_valor: string
  valor_matricula_anual: number
  costo_titulacion: number
  valor_certificado_diploma: number
  arancel_anual: number
}
export enum NivelGlobal {
  Pregrado = "Pregrado",
  PostGradoPostitulo = "PostGradoPostitulo",
}