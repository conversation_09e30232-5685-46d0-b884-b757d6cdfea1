<template>
    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading">
            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <div class='surface-card shadow-3 p-3 border-round'>
                    <!--Titulo-->
                    <div style="display: flex;align-items: center;padding-left: 1rem;">

                        <Button @click.prevent="goBack()"> <- </Button>
                                <h2 style="padding-left: 1rem;">Fechas Matrícula Unificada {{
                                    anio_proceso }}
                                </h2>
                    </div>
                    <br />
                    <DataTable sortField="etapa_proceso" :sortOrder="1" stripedRows showGridlines scrollable
                        :value="mu_pregrado_fecha_data" style="width: 100%;">
                        <template #empty>
                            <div style="text-align: center;"> No se encontraron datos </div>
                        </template>
                        <Column header="Etapa" field="etapa_proceso" style="width: 5%;" class="center-header">
                            <template #body="slotProps">
                                <div>
                                    {{ slotProps.data.etapa_proceso }}
                                </div>
                            </template>
                        </Column>
                        <Column header="Estado" field="estado_etapa" class="center-header">
                            <template #body="slotProps">
                                <div v-if="slotProps.data.estado_etapa == null || slotProps.data.estado_etapa == false"
                                    v-tooltip.bottom="pending_text">
                                    <Button disabled icon="pi pi-hourglass"
                                        style="justify-content: center;color: orangered;" class="p-button-text" />
                                </div>
                                <div v-else v-tooltip.bottom="finalized_text">
                                    <Button disabled icon="pi pi-verified" style="justify-content: center;color: green"
                                        class="p-button-text green-icon" />
                                </div>
                            </template>
                        </Column>
                        <Column header="Fecha inicio" field="fecha_inicio" class="center-header">
                            <template #body="slotProps">
                                <div>
                                    {{ slotProps.data.fecha_inicio }}
                                </div>
                            </template>
                        </Column>
                        <Column header="Fecha termino" field="fecha_termino" class="center-header">
                            <template #body="slotProps">
                                <div>
                                    {{ slotProps.data.fecha_termino }}
                                </div>
                            </template>
                        </Column>
                        <Column header="Editar" field="edit" style="width: 10%;" class="center-header">
                            <template #body="slotProps">
                                <div v-if="hasAccess('Administrador Sistema', 'Administracion PIU') || hasAccess('Encargado', 'Mu-pregrado') || hasAccess('Usuario General', 'Mu-pregrado')">
                                    <Button icon="pi pi-wrench" style="justify-content: center;color: blue;"
                                        class="p-button-text"
                                        @click="dialogVisibleCrear = true, etapaDialog = slotProps.data.etapa_proceso" />
                                </div>
                            </template>
                        </Column>
                    </DataTable>
                </div>
            </div>
        </div>
        <!-- Loading -->
        <div v-else class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
                <Loading />
            </div>
        </div>

    </div>
    <Dialog v-model:visible="dialogVisibleCrear" modal :header="'Definir fechas etapa ' + etapaDialog"
        :style="{ width: '30rem' }" v-on:hide="resetForm()">
        <div v-if="dialogIsLoading && !muIsCorrect">
            <Loading />
        </div>

        <!-- Form to create a new report -->
        <div v-if="!dialogIsLoading && !muIsCorrect && !muHasErrors">
            <div>
                <div class="flex align-items-center gap-3 mb-3" style="justify-content: space-between;">
                    <div>
                        <label for="fechaInicio" class="font-semibold w-6rem">Fecha Inicio </label>
                        <i class="pi pi-info-circle" style="justify-content: center"
                            v-tooltip.bottom="tooltipTextFechaInicio" />
                    </div>
                    <Calendar v-model="fecha_inicio_1" dateFormat="dd/mm/yy" style="width: 58%;"
                        :minDate="minDateFechaInicio" :maxDate="maxDateFechaInicio"
                        :invalid="fecha_inicio_1_isInvalid" />


                </div>
                <div style="text-align: center;" v-if="errorMsg_FechaInicio" class="p-error block mb-3">{{
                    errorMsg_FechaInicio }}
                </div>
                <div class="flex align-items-center gap-3 mb-3" style="justify-content: space-between;">
                    <div>
                        <label for="fechaTermino" class="font-semibold w-6rem">Fecha Término </label>
                        <i class="pi pi-info-circle" style="justify-content: center"
                            v-tooltip.bottom="tooltipTextFechaTermino" />
                    </div>
                    <Calendar v-model="fecha_termino_1" dateFormat="dd/mm/yy" style="width: 58%;"
                        :minDate="minDateFechaTermino" :maxDate="maxDateFechaTermino"
                        :invalid="fecha_termino_1_isInvalid"
                        :year=2024 />

                </div>
            </div>
            <div style="text-align: center;" v-if="errorMsg_FechaTermino" class="p-error block mb-3">{{
                errorMsg_FechaTermino }}
            </div>

            <!-- Buttons -->
            <div class="flex justify-content-end gap-2">
                <Button type="button" label="Cancelar" severity="secondary"
                    @click="dialogVisibleCrear = false"></Button>
                <Button type="button" label="Crear" @click="crearMuPregradoFecha()"></Button>
            </div>
        </div>

        <!-- Success message -->
        <div v-if="!dialogIsLoading && muIsCorrect"
            style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
            <i class="pi pi-check" style="font-size: 5rem; color: blue;"></i>
            <span class="p-text-secondary block mb-5">¡Creación exitosa!</span>
        </div>

        <!-- Error message -->
        <div v-if="!dialogIsLoading && !muIsCorrect && muHasErrors"
            style="display: flex; flex-direction: column; justify-content: center; align-items: center;">
            <i class="pi pi-times" style="font-size: 5rem; color: red;"></i>
            <span class="p-error block mb-5">Ocurrió un error al intentar crear las fechas. Por favor, inténtelo de
                nuevo.</span>
        </div>
    </Dialog>
    <Toast position="bottom-left" group="bl" />
</template>

<script>
import { useAuthStore } from '../../../store/auth';
import { ref, computed, onMounted } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import axios from 'axios';
import Papa from 'papaparse';
import { useToast } from 'primevue/usetoast';
import { useRoute, useRouter } from 'vue-router';


export default {

    props: {
        anio_proceso: {
            type: String,
            required: true
        },
        etapa_actual: {
            type: String,
            required: true
        },
        is_finalized: {
            type: String,
            required: true
        }
    },

    setup(props) {
        const anio_proceso = decryptData(props.anio_proceso);
        const etapa_actual = decryptData(props.etapa_actual);
        const is_finalized = decryptData(props.is_finalized);
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const permissionsList = computed(() => decryptData(authStore.permissionsList));
        const route = useRoute();
        const router = useRouter();
        const authStore = useAuthStore();
        const toast = useToast();
        const mu_pregrado_fecha_data = ref([]);
        const mu_pregrado_data_actual = ref([]);
        const userToken = computed(() => decryptData(authStore.idToken));
        const dialogVisibleCrear = ref(false);
        const dialogIsLoading = ref(false);
        const muIsCorrect = ref(false);
        const isLoading = ref(false);
        const globalLoading = ref(false);
        const etapaDialog = ref("");
        const muHasErrors = ref(false);
        const finalized_text = ref("Finalizado");
        const pending_text = ref("Pendiente");

        // For DialogCrear
        const fecha_inicio_1 = ref();
        const fecha_termino_1 = ref();
        const fecha_inicio_1_isInvalid = ref(false);
        const fecha_termino_1_isInvalid = ref(false);
        const errorMsg_FechaInicio = ref('');
        const errorMsg_FechaTermino = ref('');
        const minDateFechaInicio = computed(() => new Date(anio_proceso - 1, 0, 1));
        const maxDateFechaInicio = computed(() => new Date(anio_proceso + 1, 11, 31));
        const minDateFechaTermino = computed(() => new Date(anio_proceso - 1, 0, 1));
        const maxDateFechaTermino = computed(() => new Date(anio_proceso + 1, 11, 31));
        const tooltipTextFechaInicio = ref("Fecha de inicio de la etapa");
        const tooltipTextFechaTermino = ref("Fecha de término de la etapa");

        const goBack = () => {
            router.push({ path: "/PP-MU-pregrado" });
        };

        onMounted(async () => {
            isLoading.value = true;
            await getMUPregradoAnioActual();
            await getMuPregradoFecha();
            isLoading.value = false;
        });

        const getMUPregradoAnioActual = async () => {
            try {
                isLoading.value = true;

                const response = await axios.get(`${API_BASE_URL}mu_pregrado/${anio_proceso}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });

                if (response.status == 200) {
                    mu_pregrado_data_actual.value = response.data;
                    console.log(mu_pregrado_data_actual.value);
                }
            } catch (error) {
                console.error("Error: ", error);
            } finally {
                isLoading.value = false;
            }
        };

        const getMuPregradoFecha = async () => {
            try {
                isLoading.value = true;

                const response = await axios.get(`${API_BASE_URL}mu_pregrado_fecha/${anio_proceso}`, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    },
                });

                if (response.status == 200) {
                    mu_pregrado_fecha_data.value = response.data;

                    if (mu_pregrado_fecha_data.value.length < 5) {
                        const currentLength = mu_pregrado_fecha_data.value.length;
                        for (let i = currentLength; i < 5; i++) {
                            mu_pregrado_fecha_data.value.push({
                                etapa_proceso: i + 1,
                                fecha_inicio: "Sin definir",
                                fecha_termino: "Sin definir"
                            });
                        }
                    }
                    mu_pregrado_fecha_data.value = mu_pregrado_fecha_data.value.map(item => {
                        return {
                            ...item,
                            estado_etapa: item.etapa_proceso < mu_pregrado_data_actual.value.etapa_actual ? true : false
                        };
                    });
                    console.log(mu_pregrado_fecha_data.value);
                    console.log(is_finalized)
                } else if (response.status == 204) {
                    mu_pregrado_fecha_data.value = [];
                } else {
                    toast.add({
                        severity: 'error',
                        summary: 'Error',
                        detail: 'Error al cargar los datos de la tabla, intentelo nuevamente.',
                        group: "bl",
                        life: 3000
                    });
                    console.error("Error: ", response);
                }
            } catch (error) {
                toast.add({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Error al cargar los datos de la tabla, intentelo nuevamente.',
                    group: "bl",
                    life: 3000
                });
                console.error("Error: ", error);
            } finally {
                isLoading.value = false;
            }
        }

        const resetForm = () => {

            dialogVisibleCrear.value = false; // Close the dialog
            fecha_inicio_1.value = null;
            fecha_termino_1.value = null;
            fecha_inicio_1_isInvalid.value = false;
            fecha_termino_1_isInvalid.value = false;
            errorMsg_FechaInicio.value = "";
            errorMsg_FechaTermino.value = "";
            muIsCorrect.value = false; // Reset the success message
            dialogIsLoading.value = false; // Reset the loading state
            muHasErrors.value = false; // Reset the error state
        }

        const crearMuPregradoFecha = async () => {
            // Clear previous error messages
            errorMsg_FechaInicio.value = '';
            fecha_inicio_1_isInvalid.value = false;
            fecha_termino_1_isInvalid.value = false;
            errorMsg_FechaTermino.value = '';
            let hasError = false;
            console.log(etapaDialog.value);
            // Check if the current etapa has a previous etapa
            const previousEtapa = mu_pregrado_fecha_data.value.find(item => item.etapa_proceso === etapaDialog.value - 1);

            if (previousEtapa && previousEtapa.fecha_termino !== "Sin definir") {
                const previousFechaTermino = new Date(
                    previousEtapa.fecha_termino.split('-')[0],
                    previousEtapa.fecha_termino.split('-')[1] - 1,
                    previousEtapa.fecha_termino.split('-')[2]
                );

                const formattedFechaInicio = new Date(
                    fecha_inicio_1.value.getFullYear(),
                    fecha_inicio_1.value.getMonth(),
                    fecha_inicio_1.value.getDate()
                );

                if (formattedFechaInicio <= previousFechaTermino) {
                    errorMsg_FechaInicio.value = 'La fecha de inicio no puede ser menor o igual a la fecha de término de la etapa anterior';
                    fecha_inicio_1_isInvalid.value = true;
                    hasError = true;
                }
            }

            if (!fecha_inicio_1.value) {
                errorMsg_FechaInicio.value = 'La fecha de inicio es obligatoria';
                fecha_inicio_1_isInvalid.value = true;
                hasError = true;
            }

            if (!fecha_termino_1.value) {
                errorMsg_FechaTermino.value = 'La fecha de término es obligatoria';
                fecha_termino_1_isInvalid.value = true;
                hasError = true;
            }
            // Validate that fecha_termino_1 is greater than fecha_inicio_1
            if (fecha_inicio_1.value && fecha_termino_1.value && fecha_termino_1.value <= fecha_inicio_1.value) {
                errorMsg_FechaTermino.value = 'La fecha de término debe ser mayor que la fecha de inicio';
                fecha_inicio_1_isInvalid.value = true;
                fecha_termino_1_isInvalid.value = true;
                hasError = true;
            }

            if (hasError) {
                return;
            }

            dialogIsLoading.value = true;

            const response = await postMuPregradoFecha();

            if (response == "ok") {
                muIsCorrect.value = true
                // If no match is found, proceed with saving the report
                console.log('Saving report for etapa:', etapaDialog.value);
                await getMuPregradoFecha();
            } else {
                muHasErrors.value = true;
            }
            dialogIsLoading.value = false;


        }

        const postMuPregradoFecha = async () => {
            // Define the request body
            const requestBody = {
                anio_proceso: anio_proceso,
                fecha_inicio: fecha_inicio_1.value,
                fecha_termino: fecha_termino_1.value,
                etapa_actual: etapaDialog.value
            };
            try {
                dialogIsLoading.value = true;
                const response = await axios.post(API_BASE_URL + "mu_pregrado_Fecha", requestBody, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                if (response.status == 200 || response.status == 201) {
                    muIsCorrect.value = true;
                    return "ok";
                } else {
                    return "error";
                }

            } catch (error) {
                console.error("Error: ", error);
            } finally {
                dialogIsLoading.value = false;
            }
        };

        const hasAccess = (allowedRol, requiredRecurso) => {
            const access = permissionsList.value.some(user => user.rol === allowedRol && user.recurso === requiredRecurso);
            if (access) {
                return true;
            } else {
                return false;
            }

        };


        return {
            anio_proceso,
            etapa_actual,
            is_finalized,
            isLoading,
            globalLoading,
            goBack,
            getMuPregradoFecha,
            mu_pregrado_fecha_data,
            dialogVisibleCrear,
            dialogIsLoading,
            muIsCorrect,
            etapaDialog,
            fecha_inicio_1,
            fecha_termino_1,
            fecha_inicio_1_isInvalid,
            fecha_termino_1_isInvalid,
            errorMsg_FechaInicio,
            errorMsg_FechaTermino,
            minDateFechaInicio,
            maxDateFechaInicio,
            minDateFechaTermino,
            maxDateFechaTermino,
            tooltipTextFechaInicio,
            tooltipTextFechaTermino,
            resetForm,
            crearMuPregradoFecha,
            muHasErrors,
            mu_pregrado_data_actual,
            finalized_text,
            pending_text,
            hasAccess,
            permissionsList
        }
    }
}
</script>
<style scoped>
:deep() .center-header {
    text-align: center !important;
    align-content: center !important;
}

:deep() .p-datatable .p-column-header-content {
    display: block !important;

}

.yellow-icon .pi {
    color: yellow;
}

.green-icon .pi {
    color: greenyellow;
}
</style>