<template>
    <div v-if="!matriculaFetched" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <form @submit.prevent="submitForm">
            <div class='surface-card shadow-3 p-3 border-round'>
                <h2>Datos matrícula etapa 1:</h2>
                <div class="formgrid grid" style="align-items:center; gap:0.5rem;">
                    <div class="field col-6 col-5 p-md-6 p-sm-12" style="display: grid;">
                        <label>Ingresar año de consulta:</label>
                        <InputText v-model="anioBusqueda" @input="validateInput" required type="text" maxlength="4">
                        </InputText>
                    </div>
                    <div class="field p-md-12 p-sm-12" style="display: grid; align-self: self-end">
                        <div class="" style="display: flex;">
                            <div v-if="!formIsLoading">
                                <Button label="Buscar" type="submit" style="gap:2rem"></Button>
                            </div>
                            <div v-if="formIsLoading">
                                <ProgressSpinner style="stroke:#2667d6; width: 50px; height: 50px" strokeWidth="2"
                                    animationDuration=".8s" aria-label="Custom ProgressSpinner" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div v-else class="surface-ground py-7 md:px-5 lg:px-6">
        <DataTable :value="dataToDisplay" lazy paginator :first="first" :rows="10" v-model:filters="filters"
            ref="dataTable" dataKey="matricula_id" filterDisplay="menu" :loading="loading" :globalFilterFields="['situacion_matricula', 'anio_ing_act', 'fecha_matricula',
                'Estudiante.rut', 'Estudiante.digito_verificador', 'Estudiante.nacDisplay', 'Carrera.nombre_carrera']"
            @page="onPage($event)" @sort="onSort($event)" @filter="onFilter($event)" :totalRecords="totalRecords"
            v-model:editingRows="editingRows" editMode="row" @rowEditInit="onRowEditInit" @row-edit-save="onRowEditSave"
            @rowEditCancel="onRowEditCancel" :pt="{
                table: { style: 'min-width: 50rem' },
                column: {
                    bodycell: ({ state }) => ({
                        style: state['d_editing'] && 'padding-top: 0.6rem; padding-bottom: 0.6rem'
                    })
                }
            }">
            <template #header>
                <h2 class="h2">Matriculas: </h2>
                <div class="dataTableTOP">
                    <div class="left">
                        <Button type="button" class="shadow-3" icon="pi pi-filter-slash" label="Limpiar" outlined
                            @click="clearFilter()"></Button>
                    </div>
                    <div class="right">
                    </div>
                </div>
            </template>
            <template #empty> No se encontraron estudiantes. </template>
            <template #loading> Cargando informacion de estudiantes. </template>
            <Column field="situacion_matricula" header="Situacion Matricula" style="min-width: 12rem"
                :filter-match-mode-options="filtro_tipo_string" sortable>
                <template #body="{ data, field }">
                    {{ data[field] }}
                </template>
                <template #filter="{ filterModel }">
                    <InputText v-model="filterModel.value" type="text" class="p-column-filter"
                        placeholder="Situacion matricula" />
                </template>
                <template #editor="{ data, field }">
                    <inputText v-model="data.situacion_matricula" />
                </template>
            </Column>
            <Column field="anio_ing_act" header="Año Ingreso actual" style="min-width: 12rem"
                :filter-match-mode-options="filtro_tipo_integer" sortable>
                <template #body="{ data, field }">
                    {{ data[field] }}
                </template>
                <template #filter="{ filterModel }">
                    <InputText v-model="filterModel.value" type="number" class="p-column-filter" b
                        placeholder="Año ingreso" />b
                </template>b
                <template #editor="{ data, field }">
                    <InputText v-model="data[field]" type="number" />
                </template>
            </Column>
            <Column field="fecha_matricula" header="Fecha Matricula" style="min-width: 12rem"
                :filter-match-mode-options="filtro_tipo_integer" sortable>
                <template #body="{ data, field }">
                    {{ data[field] }}
                </template>
                <template #filter="{ filterModel }">
                    <Calendar v-model="filterModel.value" dateFormat="yy-mm-dd" class="p-column-filter"
                        placeholder="Fecha matricula" />
                </template>
                <template #editor="{ data, field }">
                    <Calendar v-model="data.fecha_matricula" dateFormat="yy-mm-dd" />
                </template>
            </Column>
            <Column field="Estudiante.rut" header="Num. Documento" filterField="Estudiante.rut" style="min-width: 12rem"
                :filter-match-mode-options="filtro_tipo_string" sortable>
                <template #body="{ data }">
                    {{ data.Estudiante.rut }}
                </template>
                <template #filter="{ filterModel }">
                    <InputText v-model="filterModel.value" type="text" class="p-column-filter" placeholder="Rut" />
                </template>
                <template #editor="{ data, field }">
                    <InputText v-model="data.Estudiante.rut" />
                </template>
            </Column>
            <Column field="Estudiante.digito_verificador" header="Digito Verificador"
                filterField="Estudiante.digito_verificador" style="min-width: 12rem"
                :filter-match-mode-options="filtro_tipo_string" sortable>
                <template #body="{ data }">
                    {{ data.Estudiante.digito_verificador }}
                </template>
                <template #filter="{ filterModel }">
                    <InputText v-model="filterModel.value" type="text" class="p-column-filter"
                        placeholder="Digito verificador" />
                </template>
                <template #editor="{ data, field }">
                    <InputText v-model="data.Estudiante.digito_verificador" />
                </template>
            </Column>
            <Column field="Estudiante.nacDisplay" header="Nacionalidad" filterField="Estudiante.nacDisplay"
                style="min-width: 12rem" :filter-match-mode-options="filtro_tipo_pais" sortable>
                <template #body="{ data }">
                    {{ data.Estudiante.nacDisplay }}
                </template>
                <template #filter="{ filterModel }">
                    <InputText v-model="filterModel.value" type="text" class="p-column-filter"
                        placeholder="Nacionalidad" />
                </template>
                <template #editor="{ data, field }">
                    <Dropdown v-model="selectedPais" :options="paisList" filter optionLabel="pais"
                        :placeholder=data.Estudiante.nacDisplay class="w-full md:w-14rem"></Dropdown>
                </template>
            </Column>
            <Column field="Carrera.nombre_carrera" header="Carrera" filterField="Carrera.nombre_carrera"
                style="min-width: 12rem" :filter-match-mode-options="filtro_tipo_string" sortable>
                <template #body="{ data }">
                    {{ data.Carrera.nombre_carrera }}
                </template>
                <template #filter="{ filterModel }">
                    <InputText v-model="filterModel.value" type="text" class="p-column-filter" placeholder="Carrera" />
                </template>
                <template #editor="{ data }">
                    <Dropdown v-model="selectedCarrera" :modelValue=data.Carrera.nombre_carrera :options="carreraList"
                        filter optionLabel="nombre_carrera" :placeholder=data.Carrera.nombre_carrera
                        class="w-full md:w-14rem"></Dropdown>
                </template>
            </Column>
            <Column :rowEditor="true" style="width: 10%; min-width: 8rem" bodyStyle="text-align:center"></Column>
            <template #paginatorstart>
                <div style="display: flex; align-items: center;">
                    <Button type="button" icon="pi pi-download" @click="downloadCSV('parcial')"></Button>
                    <h1 style="margin-left: 10px;">Datos parciales</h1>
                </div>
            </template>

            <template #paginatorend>
                <div style="display: flex; align-items: center;">
                    <h1 style="margin-right: 10px;">Total de datos</h1>
                    <Button type="button" icon="pi pi-download" @click="downloadCSV('total')"></Button>
                </div>

            </template>
        </DataTable>
    </div>
    <Dialog v-model:visible="visible" modal header="Enviar Errores" :style="{ width: '25rem' }">
        <label>Comentario:</label>
        <Textarea v-model="value" rows="5" cols="30" />
        <MultiSelect :maxSelectedLabels="2" v-model="selectedCities" :options="cities" optionLabel="name" placeholder="Seleccionar columnas"
             class="w-full md:w-20rem" />

        <br></br>
        <br></br>
        <br></br>
        <div class="flex justify-content-end gap-2">
            <Button type="button" label="Cancelar" severity="secondary" @click="visible = false"></Button>
            <Button type="button" label="Enviar" @click="visible = false"></Button>
        </div>
    </Dialog>
    <Toast position="bottom-left" group="bl" />
</template>

<script>
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import { ref, computed, onMounted } from 'vue';
import { FilterMatchMode, FilterOperator } from 'primevue/api';
import axios from 'axios';
import Papa from 'papaparse';
import { codigo_pais } from '@/utils/codigos_pais';
import InputText from 'primevue/inputtext';
import Dropdown from 'primevue/dropdown';
import { format } from 'date-fns'; // format dates to yyyy-mm-dd
import { useToast } from 'primevue/usetoast'; // display alerts
import Dialog from 'primevue/dialog';

export default {
    setup() {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const toast = useToast();
        const dataTable = ref(null);
        const screenWidth = ref(window.innerWidth);
        const isDesktop = computed(() => screenWidth.value >= 1024);
        const isTablet = computed(() => screenWidth.value >= 768 && screenWidth.value < 1024);
        const dataToDisplay = ref([]);
        const filters = ref({});
        const loading = ref(false);
        const fetchingMatricula = ref(false);
        const matriculaFetched = ref(false);
        const anioBusqueda = ref(null);
        const formIsLoading = ref(false);
        //TODO: UPDATE CSV HEADERS WITH CURRENT DATA HEADERS
        const csvHeaders = ref([
            {
                "periodo_academico": "2025 - ANUAL",
                "situacion_matricula": "MATRICULADO",
                "situacion_simbad_1": "REGUL/REGULAR",
                "tipo_admision": 1,
                "fecha_matricula": "2025-02-16",
                "hora_matricula": "23:40:00",
                "anio_ing_act": null,
                "sem_ing_act": null,
                "anio_ing_act": null,
                "anio_ing_ori": null,
                "sem_ing_ori": null,
                "sit_fon_sol": null,
                "reincorporacion": null,
                "vigencia": null,
                "Estudiante": {
                    "rut": "19102588",
                    "digito_verificador": "1",
                    "apellido_paterno": "Perez",
                    "apellido_materno": "Ochoa",
                    "nombres": "2",
                    "sexo": "2",
                    "fecha_nacimiento": "2001-02-01",
                    "nacionalidad": "2",
                    "pais": "2",
                    "nac": null,
                    "pais_est_sec": null,
                    "anio_ing_ori": null,
                    "sem_ing_ori": null,
                    "tipo_doc": null
                },
                "Pace": {
                    "beneficiario": true
                },
                "Bea": {
                    "beneficiario": false
                },
                "Colegio": {
                    "nombre": "liceo AZ-10",
                    "tipo": "liceo",
                    "region": "Antofagasta",
                    "provincia": "Antofagasta",
                    "comuna": "Antofagasta"
                },
                "InformacionContacto": {
                    "email_UAntof": "<EMAIL>",
                    "email": "<EMAIL>",
                    "telefono_fijo": "(552) 458316",
                    "telefono_celular": "+569 23851565",
                    "domicilio": "Avenida Angamos #610",
                    "region": "2",
                    "comuna_origen": "Antofagasta"
                },
                "Carrera": {
                    "codigo_unico": "I73S1C273J1V3",
                    "codigo_carrera_mineduc": 273,
                    "codigo_ua": null,
                    "llave_vacante": "2024-I73S1C273J1V3",
                    "nombre_carrera": "Carrera 3",
                    "tipo_carrera": "Plan Regular",
                    "nivel_global": "Postgrado",
                    "nivel_carrera": "Doctorado",
                    "anio": 2024,
                    "anio_inicio": 1997,
                    "semestres_reconocidos": 0,
                    "area_conocimiento": "Tecnología",
                    "area_carrera_generica": "Doctorado en Tecnología",
                    "plan_especial": "NO APLICA",
                    "demre": 0,
                    "acreditacion": "Acreditada",
                    "elegibilidad_beca_pedagogia": "No Elegible",
                    "vigencia": "Vigente con Alumnos Nuevos",
                    "pedagogia_medicina_otro": "otro",
                    "requisito_ingreso": "Licenciatura"
                },
                "InformacionCurricular": {
                    "asi_ins_ant": 1,
                    "asi_apr_ant": 1,
                    "prom_pri_sem": 1,
                    "prom_seg_sem": 1,
                    "asi_ins_his": 1,
                    "asi_apr_his": 1,
                    "niv_aca": 1,
                    "sus_pre": 1
                }
            }
        ]);
        const first = ref(0);
        const totalRecords = ref(0);
        const lazyParams = ref({});
        const editingRows = ref([]);
        const rowEditor = ref(false);
        const carreraList = ref();
        const selectedCarrera = ref();
        const paisList = ref();
        const selectedPais = ref();
        const originalData = ref(null);
        const filteredData = ref();
        const initFilters = () => {
            filters.value = {
                situacion_matricula: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.CONTAINS }] },
                anio_ing_act: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.EQUALS }] },
                fecha_matricula: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.EQUALS }] },
                'Estudiante.rut': { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.CONTAINS }] },
                'Estudiante.digito_verificador': { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.CONTAINS }] },
                'Estudiante.nacDisplay': { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.EQUALS }] },
                'Carrera.nombre_carrera': { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.CONTAINS }] },
            };
        };
        const visible = ref(false);
        const selectedCities = ref();
        const cities = ref([
            { name: 'Rut', code: 'NY' },
            { name: 'Fecha Matricula', code: 'RM' },
            { name: 'Fecha Nacimiento', code: 'LDN' },
            { name: 'Estado Fondo Sol', code: 'IST' },
            { name: 'Vigencia', code: 'PRS' }
        ]);

        const validateInput = (event) => {
            let value = event.target.value;
            // Use regex to allow only numeric characters
            value = value.replace(/[^0-9]/g, '');
            // Update the input value
            anioBusqueda.value = value;
            // Manually set the value in the input field to remove unwanted characters
            event.target.value = value;
        };
        initFilters();

        const submitForm = async () => {
            loading.value = true;
            fetchingMatricula.value = true;
            formIsLoading.value = true;
            console.log("getting data")
            initFilters();
            lazyParams.value = {
                first: 0,
                page: 0,
                rows: 10,
                limit: 10,
                sortField: null,
                sortOrder: null,
                filters: filters.value,
                anioBusqueda: anioBusqueda.value
            };
            await getData({ lazyEvent: JSON.stringify(lazyParams.value) });

            if (dataToDisplay.value.length === 0) {
                // Error fetching the data from the back end
                console.log("incorrecto")
                showToastNotification("error", "ERROR", "No se han encontrado registros para al año ingresado.");


            } else {
                console.log("correcto")
                matriculaFetched.value = true;

            }
            fetchingMatricula.value = false;
            formIsLoading.value = false;
        }

        const clearFilter = () => {
            loading.value = true;
            initFilters();
            lazyParams.value = {
                first: 0,
                page: 0,
                rows: 10,
                limit: 10,
                sortField: null,
                sortOrder: null,
                filters: filters.value,
                anioBusqueda: anioBusqueda.value
            };
            loadLazyData();

            loading.value = false;
        };

        const getData = async (params) => {
            try {
                loading.value = true;
                const queryParams = params
                    ? Object.keys(params)
                        .map((k) => encodeURIComponent(k) + '=' + encodeURIComponent(params[k]))
                        .join('&')
                    : '';
                console.log(queryParams)
                const response = await axios.get(API_BASE_URL + "admision/anio/?" + queryParams);

                dataToDisplay.value = response.data.rows;
                totalRecords.value = response.data.count;
                // Create a equivalent to "Nac" with the name of the country
                dataToDisplay.value.forEach(element => {
                    codigo_pais.equivalents.forEach(codigo_pais_data => {
                        if (codigo_pais_data.pais_id == element.Estudiante.nac) {
                            element.Estudiante.nacDisplay = codigo_pais_data.pais

                        }
                    })
                });


            } catch (error) {
                console.error("Error: ", error);
            } finally {
                loading.value = false;
            }
        };

        const getDataForDownload = async (params) => {
            try {
                loading.value = true;
                const queryParams = params
                    ? Object.keys(params)
                        .map((k) => encodeURIComponent(k) + '=' + encodeURIComponent(params[k]))
                        .join('&')
                    : '';
                const response = await axios.get(API_BASE_URL + "admision/anio/?" + queryParams);

                filteredData.value = response.data.rows;

                // Create a equivalent to "Nac" with the name of the country
                filteredData.value.forEach(element => {
                    codigo_pais.equivalents.forEach(codigo_pais_data => {
                        if (codigo_pais_data.pais_id == element.Estudiante.nac) {
                            element.Estudiante.nacDisplay = codigo_pais_data.pais

                        }
                    })
                });


            } catch (error) {
                console.error("Error: ", error);
            } finally {
                loading.value = false;
            }
        };

        const onRowEditInit = async (event) => {
            console.log("display modal");
            visible.value = true
            originalData.value = JSON.parse(JSON.stringify(event.data));
        };

        const onRowEditSave = async (event) => {
            updateRowOnBackend(event.newData, event.index);
        };

        const onRowEditCancel = async (event) => {
            dataToDisplay.value[event.index] = originalData.value
        };

        const checkforUpdateErrors = async (updatedData, index) => {
            if (updatedData.situacion_matricula == "" || updatedData.anio_ing_act == "" || updatedData.fecha_matricula == ""
                || updatedData.rut == "" || updatedData.digito_verificador == "") {
                showToastNotification("error", "ERROR - Campos vacios", "Se detectaron campos vacíos!!!!");
                onRowEditCancel();
            }
        };

        const updateRowOnBackend = async (updatedRow, index) => {
            dataToDisplay.value[index] = updatedRow;

            // Check for 'Thu Mar 24 2022 21:00:00 GMT-0300 (hora de verano de Chile)' format
            if (/^\d{4}-\d{2}-\d{2}$/.test(dataToDisplay.value[index].fecha_matricula)) {

            } else {
                const year = dataToDisplay.value[index].fecha_matricula.getFullYear();
                const month = dataToDisplay.value[index].fecha_matricula.getMonth(); // Note: getMonth() returns 0-11 for Jan-Dec
                const day = dataToDisplay.value[index].fecha_matricula.getDate();
                const dateWithoutTime = new Date(year, month, day + 'T00:00:00');
                dataToDisplay.value[index].fecha_matricula = new Date(dataToDisplay.value[index].fecha_matricula);
                dataToDisplay.value[index].fecha_matricula = format(dataToDisplay.value[index].fecha_matricula, 'yyyy-MM-dd');
            }

            checkforUpdateErrors(dataToDisplay.value[index], index);

            if (selectedPais.value != undefined || selectedPais.value != null) {
                dataToDisplay.value[index].Estudiante.nacDisplay = selectedPais.value.pais;
            }
            if (selectedCarrera.value != undefined || selectedCarrera.value != null) {
                dataToDisplay.value[index].Carrera = selectedCarrera.value;
            }
            try {
                loading.value = true;
                const response = await axios.put(API_BASE_URL + "admision/matricula/" + dataToDisplay.value[index].matricula_id, {
                    body: dataToDisplay.value[index]
                });
                if (response.status === 200) {
                    console.log('Successfully updated');
                    showToastNotification("success", "Correcto", "Matricula actualizada correctamente");
                    loading.value = false;
                }
            } catch (error) {
                console.log(error);
                showToastNotification("error", "Error", "Error al actualizar matricula");
                loading.value = false;
            } finally {
                console.log("Finally");
                loading.value = false;
            }

        };

        const onPage = (event) => {
            event.anioBusqueda = anioBusqueda.value;
            lazyParams.value = event;
            loadLazyData(event);
        };
        const onSort = (event) => {
            event.anioBusqueda = anioBusqueda.value;
            lazyParams.value = event;
            loadLazyData(event);
        };
        const onFilter = (event) => {
            event.anioBusqueda = anioBusqueda.value;
            lazyParams.value.filters = filters.value;
            loadLazyData(event);
        };
        const loadLazyData = (event) => {
            loading.value = true;
            lazyParams.value = { ...lazyParams.value, first: event?.first || first.value };
            getData({ lazyEvent: JSON.stringify(lazyParams.value) });
        };

        // Download data as csv
        const downloadCSV = async (tipoDownload) => {
            const dataforDownload = ref(null);
            console.log("tipo descarga: " + tipoDownload);
            console.log(filters.value)

            lazyParams.value = {
                first: 0,
                page: 0,
                rows: null,
                limit: null,
                sortField: null,
                sortOrder: null,
                filters: filters.value,
                anioBusqueda: anioBusqueda.value
            };
            if (tipoDownload == "parcial") {
                // array to compare with the actual filters
                const filtros = ref({
                    situacion_matricula: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.CONTAINS }] },
                    anio_ing_act: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.EQUALS }] },
                    fecha_matricula: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.EQUALS }] },
                    'Estudiante.rut': { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.CONTAINS }] },
                    'Estudiante.digito_verificador': { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.CONTAINS }] },
                    'Estudiante.nacDisplay': { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.EQUALS }] },
                    'Carrera.nombre_carrera': { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.CONTAINS }] },

                })

                if (deepEqual(filtros.value, filters.value)) {
                    console.log("filtros iguales");
                    showToastNotification("warn", "ADVERTENCIA", "Primero debe seleccionar un filtro");
                    return;
                } else {
                    console.log("filtros distintos");
                }

            } else {
                initFilters();
            }

            await getDataForDownload({ lazyEvent: JSON.stringify(lazyParams.value) });
            dataforDownload.value = filteredData.value;
            if (dataforDownload.value.length == 0) {
                showToastNotification("warn", "ADVERTENCIA", "No se encontraron elementos para descargar");
                // TODO: REVISAR BAJAR TODO CUANDO NO HAY DATOS
                return;
            }

            const flattenedHeaders = getKeys(csvHeaders.value[0]);
            const flattenedData = dataforDownload.value.map(item => getObjectData(item));

            const csvData = Papa.unparse({
                fields: flattenedHeaders, // Using the keys from the first header object
                data: flattenedData.map(row => {
                    return flattenedHeaders.map(header => row[header] === undefined || null ? "NULL" : row[header]);
                })
            });

            // Create a Blob object for the CSV content
            const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });

            // Create a link element, hide it, direct it towards the blob, and then 'click' it programatically
            const link = document.createElement("a");
            if (link.download !== undefined) { // feature detection
                const url = URL.createObjectURL(blob);
                link.setAttribute("href", url);
                link.setAttribute("download", "matricula_data.csv");
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        };

        // Function to perform deep comparison of two objects
        function deepEqual(obj1, obj2) {
            if (obj1 === obj2) return true;

            if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {
                return false;
            }

            let keys1 = Object.keys(obj1);
            let keys2 = Object.keys(obj2);

            if (keys1.length !== keys2.length) return false;

            for (let key of keys1) {
                if (!keys2.includes(key)) return false;

                if (!deepEqual(obj1[key], obj2[key])) return false;
            }

            return true;
        }

        const filtro_tipo_string = [
            { label: 'Contiene', value: FilterMatchMode.CONTAINS },
            { label: 'No contiene', value: FilterMatchMode.NOT_CONTAINS },
            { label: 'Comienza con', value: FilterMatchMode.STARTS_WITH },
            { label: 'Termina con', value: FilterMatchMode.ENDS_WITH },
            { label: 'Igual', value: FilterMatchMode.EQUALS },
            { label: 'No es igual', value: FilterMatchMode.NOT_EQUALS },
        ];

        const filtro_tipo_integer = [
            { label: 'Igual', value: FilterMatchMode.EQUALS },
            { label: 'No es igual', value: FilterMatchMode.NOT_EQUALS },
            { label: 'Menor que', value: FilterMatchMode.LESS_THAN },
            { label: 'Mayor que', value: FilterMatchMode.GREATER_THAN },
            { label: 'Menor igual', value: FilterMatchMode.LESS_THAN_OR_EQUAL_TO },
            { label: 'Mayor igual', value: FilterMatchMode.GREATER_THAN_OR_EQUAL_TO },
        ];

        const filtro_tipo_pais = [
            { label: 'Igual', value: FilterMatchMode.EQUALS },
            { label: 'No es igual', value: FilterMatchMode.NOT_EQUALS },
        ];

        function getKeys(obj, parentKey = '') {
            let keys = [];
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    const newKey = parentKey ? `${parentKey}.${key}` : key;
                    if (typeof obj[key] === 'object' && obj[key] !== null && obj[key] !== "NULL" && !Array.isArray(obj[key])) {
                        keys = keys.concat(getKeys(obj[key], newKey));
                    } else {
                        keys.push(newKey);
                    }
                }
            }
            return keys;
        }

        function getObjectData(obj, parentKey = '', result = {}) {
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    const newKey = parentKey ? `${parentKey}.${key}` : key;
                    if (typeof obj[key] === 'object' && obj[key] !== null && obj[key] !== undefined && !Array.isArray(obj[key])) {
                        getObjectData(obj[key], newKey, result);
                    } else {
                        // Check if the current key exists in the object
                        if (obj[key] !== undefined && obj[key] !== null && obj[key] !== '') {
                            // Convert empty string to null

                            if (!obj[key]) {
                                result[newKey] = "NULL";
                            }
                            result[newKey] = obj[key];
                        } else {
                            // Handle case where obj[key] is undefined, null, or empty string
                            result[newKey] = "NULL"; // Set to null if the value is not present or empty
                        }
                    }
                } else {
                    // Handle case where obj doesn't have the key
                    result[parentKey ? `${parentKey}.${key}` : key] = "NULL"; // Set to null if the key is not present
                }
            }
            return result;
        }

        /**
        * Get the Matriculas data from the back-end to poblate the DataTable
        */
        const getCarreraData = async () => {
            try {
                loading.value = true;
                const response = await axios.get(API_BASE_URL + "carrera/");
                carreraList.value = response.data;
            } catch (error) {
                console.log("Error: " + error);
                error.value = 'Error fetching data';
            } finally {
                loading.value = false;
            }
        }

        /**
         * Show error with message
         */
        const showToastNotification = (severity, summary, detail) => {
            toast.add({ severity: severity, summary: summary, group: 'bl', detail: detail, life: 5000 });
        };

        onMounted(() => {
            loading.value = true;
            getCarreraData();
            paisList.value = codigo_pais.equivalents;
            loading.value = false;
        });

        return {
            toast,
            filters,
            loading,
            clearFilter,
            screenWidth,
            isDesktop,
            isTablet,
            dataToDisplay,
            downloadCSV,
            filteredData,
            dataTable,
            first,
            totalRecords,
            lazyParams,
            loadLazyData,
            onPage,
            onFilter,
            onSort,
            filtro_tipo_string,
            filtro_tipo_integer,
            filtro_tipo_pais,
            onRowEditSave,
            editingRows,
            rowEditor,
            carreraList,
            getCarreraData,
            selectedCarrera,
            paisList,
            selectedPais,
            updateRowOnBackend,
            originalData,
            onRowEditInit,
            checkforUpdateErrors,
            onRowEditCancel,
            getDataForDownload,
            fetchingMatricula,
            matriculaFetched,
            anioBusqueda,
            formIsLoading,
            submitForm,
            validateInput,
            visible,
            cities,
            selectedCities

        };
    }
};
</script>

<style>
/* For phones */
@media screen and (max-width: 767px) {
    .SearchBar {
        /* Make the SearchBar 50% of the container's width */
        width: 50%;
    }
}

/* For tablets */
@media screen and (min-width: 768px) and (max-width: 1024px) {
    .SearchBar {
        /* Make the SearchBar 50% of the container's width */
        width: 70%;
    }
}

.dataTableTOP {
    display: flex;
    justify-content: space-between;

}

.right {
    display: flex;
    align-items: center;
    text-align-last: end;
    /* Vertically center the content */
}

.right-aligned {
    margin-right: auto;
    /* Push the SearchBar to the right */
}

.surface-ground {
    background-color: var(--secundary-color);
}

.dark-lighter-mode {
    background-color: #1c1b1b;
    color: #fff;
}
</style>