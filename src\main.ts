import { createApp } from "vue";
import { create<PERSON><PERSON> } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import { VueQueryPlugin, useQueries } from "@tanstack/vue-query";
import ToastService from "primevue/toastservice";
import PrimeVue from "primevue/config";
import ConfirmationService from "primevue/confirmationservice";
import App from "./App.vue";
import router from "./router/index.js";
import { PublicClientApplication } from "@azure/msal-browser"; // MSAL
import { msalConfig } from "./auth/msalConfig.js"; // MSAL
import { useAuthStore } from "./store/auth.js";
import sideTopBar from "./components/sideTopBar.vue";
import notifications from "./components/Notifications.vue";
import loading from "./views/utils/loading.vue";
import Ripple from "primevue/ripple";
import "primeicons/primeicons.css";
import "primeflex/primeflex.css";

import Chart from "primevue/chart";
import DataView from "primevue/dataview";
import Sidebar from "primevue/sidebar";
import Button from "primevue/button";
import Menubar from "primevue/menubar";
import Badge from "primevue/badge";
import InputGroup from "primevue/inputgroup";
import InputGroupAddon from "primevue/inputgroupaddon";
import InputText from "primevue/inputtext";
import InputNumber from "primevue/inputnumber";
import InputMask from 'primevue/inputmask';
import DataTable from "primevue/datatable";
import Column from "primevue/column";
import ColumnGroup from "primevue/columngroup";
import Row from "primevue/row";
import Avatar from "primevue/avatar";
import StyleClass from "primevue/styleclass";
import Calendar from "primevue/calendar";
import Tag from "primevue/tag";
import Dropdown from "primevue/dropdown";
import ProgressBar from "primevue/progressbar";
import Slider from "primevue/slider";
import MultiSelect from "primevue/multiselect";
import IconField from "primevue/iconfield";
import InputIcon from "primevue/inputicon";
import TriStateCheckbox from "primevue/tristatecheckbox";
import FloatLabel from "primevue/floatlabel";
import Toast from "primevue/toast";
import FileUpload from "primevue/fileupload";
import ScrollPanel from "primevue/scrollpanel";
import Message from "primevue/message";
import InputSwitch from "primevue/inputswitch";
import ProgressSpinner from "primevue/progressspinner";
import Dialog from "primevue/dialog";
import TabView from "primevue/tabview";
import TabPanel from "primevue/tabpanel";
import Textarea from "primevue/textarea";
import Tooltip from 'primevue/tooltip';

import { codigo_pais } from "./utils/codigos_pais.js";

const msalInstance = new PublicClientApplication(msalConfig);
const app = createApp(App);

app.use(PrimeVue, {
  ripple: true,
  locale: {
    // text filter
    matchAll: "Coincidir todo",
    matchAny: "Coincidir alguno",
    startsWith: "Starts With",
    contains: "Contains",
    notContains: "Not Contains",
    endsWith: "Ends With",
    equals: "Equals",
    notEquals: "Not Equals",
    addRule: "Añadir Regla",
    // date filter
    firstDayOfWeek: 0,
    dayNamesMin: ["Lun", "Mar", "Mie", "Jue", "Vie", "Sab", "Dom"],
    dayNames: [
      "Lunes",
      "Martes",
      "Miércoles",
      "Jueves",
      "Viernes",
      "Sábado",
      "Domingo",
    ],
    monthNamesShort: [
      "Ene",
      "Feb",
      "Mar",
      "Abr",
      "May",
      "Jun",
      "Jul",
      "Ago",
      "Sep",
      "Oct",
      "Nov",
      "Dic",
    ],
    monthNames: [
      "Enero",
      "Febrero",
      "Marzo",
      "Abril",
      "Mayo",
      "Junio",
      "Julio",
      "Agosto",
      "Septiembre",
      "Octubre",
      "Noviembre",
      "Diciembre",
    ],
    dateFormat: "yyyy-mm-dd", // Define your desired date format here
    dateIs: "Date Is",
    dateIsNot: "Date is Not",
    dateBefore: "Date is Before",
    dateAfter: "Date is After",
    // general filter
    clear: "Limpiar",
    apply: "Aplicar",
    removeRule: "Quitar Regla",
    // number filter
    lt: "Less than",
    lte: "Less Than Or Equal To",
    gt: "Greater Than",
    gte: "Greater Than Or Equal To",
    // ... más configuraciones
  },
});
app.use(ConfirmationService);
app.use(ToastService);
VueQueryPlugin.install(app, {
  queryClientConfig: {
    defaultOptions: {
      queries: {
        gcTime: 1000 * 60, //2 minutos
      },
    },
  },
});

const pinia = createPinia();

pinia.use(piniaPluginPersistedstate);
app.use(pinia);

// components
app.directive('tooltip', Tooltip);
app.component("DataView",DataView);
app.component("notifications", notifications);
app.component("sideTopBar", sideTopBar);
app.component("Loading", loading);
app.component("Sidebar", Sidebar);
app.component("Button", Button);
app.component("Badge", Badge);
app.component("Menubar", Menubar);
app.component("Chart", Chart);
app.component("InputGroup", InputGroup);
app.component("InputGroupAddon", InputGroupAddon);
app.component("InputText", InputText);
app.component("InputNumber", InputNumber);
app.component("DataTable", DataTable);
app.component("InputMask", InputMask);
app.component("Column", Column);
app.component("ColumnGroup", ColumnGroup);
app.component("Row", Row);
app.component("Avatar", Avatar);
app.component("Calendar", Calendar);
app.component("Tag", Tag);
app.component("Dropdown", Dropdown);
app.component("ProgressBar", ProgressBar);
app.component("Slider", Slider);
app.component("MultiSelect", MultiSelect);
app.component("IconField", IconField);
app.component("InputIcon", InputIcon);
app.component("TriStateCheckbox", TriStateCheckbox);
app.component("FloatLabel", FloatLabel);
app.component("Toast", Toast);
app.component("FileUpload", FileUpload);
app.component("ScrollPanel", ScrollPanel);
app.component("Message", Message);
app.component("InputSwitch", InputSwitch);
app.component("ProgressSpinner", ProgressSpinner);
app.component("Dialog", Dialog);
app.component("TabView", TabView);
app.component("TabPanel", TabPanel);
app.component("Textarea", Textarea);

app.directive("ripple", Ripple);
app.directive("styleclass", StyleClass);
// Variable to get the equivalent name from code
app.config.globalProperties.$codigo_pais = codigo_pais;

// Set MSAL instance in Pinia store
const authStore = useAuthStore();
authStore.setMsalInstance(msalInstance);
app.provide('msalInstance', msalInstance); // Provide the MSAL instance

app.use(router);
async function initializeApp() {
  try {
    console.log("initializing MSAL");
    // Initialize MSAL instance
    await msalInstance.initialize();
  } catch (error) {
    console.error("Redirect error:", error);
  }
}

await initializeApp();
app.mount("#app");
