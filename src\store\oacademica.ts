import { ref } from 'vue'
import { defineStore } from 'pinia'
import type { OAcademica } from '@/oacademica/interfaces/OAcademica'
import type { Etapa } from '@/oacademica/interfaces/Etapa' 
import { set } from 'date-fns'

export const useOacademicaStore = defineStore('oacademica', () => {
  const oacademicas = ref<OAcademica[]>([])
  const totalRecords = ref(0)

  return {
    //state
    oacademicas,
    totalRecords,

    //actions
    setOacademicas(newoacademicas: OAcademica[]) {
      oacademicas.value = newoacademicas
    },
  }
})
