{"name": "frontend-piu-ts", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force"}, "dependencies": {"@azure/msal-browser": "^3.20.0", "@tanstack/vue-query": "^5.51.9", "@tanstack/vue-query-devtools": "^5.51.15", "axios": "^1.8.4", "chart.js": "^4.4.5", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dompurify": "^3.1.3", "exceljs": "^4.4.0", "jquery": "^3.7.1", "papaparse": "^5.4.1", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.3", "primeflex": "^3.3.1", "primeicons": "^7.0.0", "primevue": "^3.51.0", "secure-ls": "^2.0.0", "vee-validate": "^4.14.7", "vue": "^3.4.21", "vue-router": "^4.3.0", "vuex": "^4.0.2", "yup": "^1.5.0"}, "devDependencies": {"@tsconfig/node20": "^20.1.4", "@types/node": "^20.14.5", "@types/vue": "^2.0.0", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vue/tsconfig": "^0.5.1", "npm-run-all2": "^6.2.0", "typescript": "~5.4.0", "vite": "^5.3.1", "vue-tsc": "^2.0.21"}}