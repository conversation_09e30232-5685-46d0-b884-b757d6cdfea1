<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, reactive, watch } from 'vue';
import { FilterMatchMode, FilterOperator } from 'primevue/api';
import useAdmisiones from '../composables/useAdmisiones';

const { admisiones, isLoading, isError, error, isSuccess, status } = useAdmisiones();



const col_global_fil: string[] = [];

const columns = [
    {
        field: 'cod_carrera',
        header: 'Código Unico Carrera'
    },
    {
        field: 'nom_carrera',
        header: 'Carrera'
    },
    {
        field: 'anio_ingreso',
        header: 'Año Ing.'
    },
    {

        field: 'n_doc',
        header: 'N Documento'
    },
    {

        field: 'nombres',
        header: 'Nombres'
    },
    {

        field: 'ap_paterno',
        header: 'Ap. Paterno'
    },
    {

        field: 'ap_materno',
        header: 'Ap. Materno'
    },
    {

        field: 'fecha_nac',
        header: '<PERSON><PERSON> Naci<PERSON>nto'
    },
    {

        field: 'prom_nota_em',
        header: 'Promedio EM'
    },
    {

        field: 'nombre_establecimiento',
        header: 'Colegio'
    },
    {

        field: 'nombre_region',
        header: 'Region'
    },
];
const rowsPerPage = [5, 10, 20, 50];

const filters = ref();
const checkDarkMode = false;
const editingRows = ref([]);

const screenWidth = ref(window.innerWidth);
const isDesktop = computed(() => screenWidth.value >= 1024);
const isTablet = computed(() => screenWidth.value >= 768 && screenWidth.value < 1024);
const loadingTexts = ['cargando', 'cargando.', 'cargando..', 'cargando...'];

const loadingText = ref(loadingTexts[0]);


// PrimeVue format for filters used in the DataTable
const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        codigo_unico: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }] },
        codigo_ua: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }] },
        nombre_carrera: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }] },
        tipo_carrera: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }] },
        codigo_carrera: { operator: FilterOperator.AND, constraints: [{ value: null, matchMode: FilterMatchMode.STARTS_WITH }] },
    };
};
initFilters();
/**
 * Change the format of the date column, to day/month/year for better visibility
 * @param value date of the "estudiante" model
 */
const formatDate = (value: string) => {
    const dateObject1 = new Date(value);
    return dateObject1.toLocaleDateString('es', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
};


</script>

<template>
    <div>
        <div
            :class="{ 'dark-lighter-mode px-4 py-5 md:px-6 lg:px-8': checkDarkMode, 'surface-ground px-4 py-5 md:px-6 lg:px-8': !checkDarkMode }">
            <DataTable id="tableHeader"
                :class="{ 'dataTableClass shadow-4': checkDarkMode, 'shadow-4': !checkDarkMode }"
                v-model:filters="filters" :value="admisiones" v-model:editingRows="editingRows" editMode="row"
                sortMode="multiple" removableSort paginator showGridlines :rows="10" :rowsPerPageOptions="rowsPerPage"
                dataKey="Id" filterDisplay="menu" :loading="isLoading" :globalFilterFields="col_global_fil" :pt="{
                    table: { style: 'min-width: 50rem' }
                }">

                <!-- Button clear and search bar -->
                <template #header>
                    <Message v-if=isError severity="error">{{ error }}
                    </Message>
                    <Message v-if=isSuccess severity="success">{{ status }}</Message>
                    <h2 class="h2">Admisión Matrícula: </h2>

                    <div v-if="isDesktop" class="dataTableTOP">
                        <div class="left">
                            <Button type="button" class="shadow-3" icon="pi pi-filter-slash" label="Limpiar" outlined
                                @click="initFilters()"></Button>
                        </div>
                        <div class="right">
                            <IconField>
                                <InputIcon>
                                    <i class="pi pi-search"></i>
                                </InputIcon>
                                <InputText class="SearchBar" v-model="filters['global'].value" placeholder="Buscar">
                                </InputText>

                            </IconField>
                        </div>
                    </div>
                    <div v-else-if="isTablet" class="dataTableTOP">
                        <div class="left">
                            <Button type="button" class="shadow-3" icon="pi pi-filter-slash" label="Limpiar" outlined
                                @click="initFilters()"></Button>
                        </div>
                        <div class="right">
                            <IconField>
                                <InputIcon>
                                    <i class="pi pi-search"></i>
                                </InputIcon>
                                <InputText class="SearchBar" v-model="filters['global'].value" placeholder="Buscar">
                                </InputText>

                            </IconField>
                        </div>
                    </div>
                    <div v-else class="dataTableTOP">
                        <div class="left">
                            <Button type="button" class="shadow-3" icon="pi pi-filter-slash" label="" outlined
                                @click="initFilters()"></Button>
                        </div>
                        <div class="right">
                            <IconField>
                                <InputIcon>
                                    <i class="pi pi-search"></i>
                                </InputIcon>
                                <InputText class="SearchBar" v-model="filters['global'].value" placeholder="">
                                </InputText>

                            </IconField>
                        </div>
                    </div>
                </template>
                <!-- No data found and loading -->
                <template #empty> No se encontró información. </template>
                <template #loading>
                    <div class="form-row">
                        <div class="form-group">
                            <i class="pi pi-spin pi-cog" style="font-size: 6rem"></i>
                            <br /><br />
                            <div><label class="labelLoading">{{ loadingText }}</label></div>
                        </div>
                    </div>
                </template>
                <Column v-for="col of columns" :key="col.field" :field="col.field" :header="col.header" sortable
                    style="min-width: 12rem">
                    <template #editor="{ data, field }">
                        <InputText v-model="data[field]" />
                    </template>
                    <template #body="{ data, field }">
                        {{ data[field] }}
                    </template>
                    <template #filter="{ filterModel }">
                        <InputText v-model="filterModel.value" type="text" class="p-column-filter"
                            placeholder="Buscar por {{ col.header }}" />
                    </template>
                </Column>

                <Column header="">
                    <template #body="slotProps">
                        <!-- Aquí puedes poner contenido dinámico basado en slotProps.data -->
                        <router-link :to="{ name: 'AdmisionFormView', params: { id: slotProps.data['matId'] } }">
                            Ir a ver
                        </router-link>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>


<style>
/* For phones */
@media screen and (max-width: 767px) {
    .SearchBar {
        /* Make the SearchBar 50% of the container's width */
        width: 50%;
    }
}

/* For tablets */
@media screen and (min-width: 768px) and (max-width: 1024px) {
    .SearchBar {
        /* Make the SearchBar 50% of the container's width */
        width: 70%;
    }
}

.dataTableTOP {
    display: flex;
    justify-content: space-between;

}

.right {
    display: flex;
    align-items: center;
    text-align-last: end;
    /* Vertically center the content */
}

.right-aligned {
    margin-right: auto;
    /* Push the SearchBar to the right */
}

.surface-ground {
    background-color: var(--secundary-color);
}

.dark-lighter-mode {
    background-color: #1c1b1b;
    color: #fff;
}

.p-datatable .p-datatable-tbody>tr {
    background-color: #ffffff;
}
</style>