import { computed, watch, ref } from "vue";
import { useQuery } from "@tanstack/vue-query";
import backEndApi from "@/api/backEndApi";
import { storeToRefs } from "pinia";
import { useOacademicaStore } from "../../store/oacademica";
import type { Etapa } from "../interfaces/Etapa";

const getEtapasByYear = async (year: any): Promise<Etapa[]> => {
  console.log("entro en getEtapasByYear");

  if (year === "") {
    return [];
  }
  const { data } = await backEndApi.get<Etapa[]>(
    `/api/v1/getprocesosiesoabyyear/${year}`
  );
  console.log("data etapas por año: ", data);
  
  return data;
};
const getEtapaActual = async (): Promise<Etapa> => {
  console.log("entro en getEtapaActual");
  const { data } = await backEndApi.get<Etapa>("/api/v1/procesosiesoaactual");

  return data;
};

const useOAcademicaEtapas = () => {
  //const store = useOacademicaStore();
  //const { etapaActual } = storeToRefs(store);
  const year = ref('');

  const etapaActualQuery = useQuery({
    queryKey: ['etapaActual'],
    queryFn: getEtapaActual,
  });

  const etapasQuery = useQuery({
    queryKey: ['etapas', year],
    queryFn: () => getEtapasByYear(year.value),
    enabled: computed(() => year.value !== ""),
  });
  

  return {
    etapasQuery,
    etapaActualQuery,

    //methods
    setYear: (filterYear: string) => {
      console.log("historial por año: ", filterYear);

      year.value = filterYear;
    },
  };
};

export default useOAcademicaEtapas;
