import { watch, ref, computed } from "vue";
import { useQuery } from "@tanstack/vue-query";
import { storeToRefs } from "pinia";
import { useOacademicaStore } from "../../store/oacademica";

import backEndApi from "@/api/backEndApi";
import type { OAcademica } from "../interfaces/OAcademica";
import { set } from "date-fns";

const getOacademicas = async (): Promise<OAcademica[]> => {
  console.log("entro en getOacademicas");
  const { data } = await backEndApi.get<OAcademica[]>("/api/v1/oacademicas/");
  console.log(data);

  return data;
};
const getOacademicasHistorico = async (
  etapaId: string
): Promise<OAcademica[]> => {
  console.log("entro en historico");
  const { data } = await backEndApi.get<OAcademica[]>(
    `/api/v1/oacademicashistorico/${etapaId}`
  );
  console.log(data);

  return data;
};

const getCsvOacademicas = async (): Promise<Blob> => {
  const { data } = await backEndApi.get<Blob>("api/v1/getcsvoacademicas/", {
    responseType: "blob",
  });
  console.log(data);
  return data;
};

const useOAcademicas = () => {
  const etapaId = ref("");
  const isHistorico = computed(() => !!etapaId.value);
  const store = useOacademicaStore();
  const { oacademicas, totalRecords } = storeToRefs(store);
  const queryKey = computed(() =>
    isHistorico.value ? ["oacademicas", etapaId.value] : ["oacademicas"]
  );
  const queryFn = computed(() =>
    isHistorico.value
      ? () => getOacademicasHistorico(etapaId.value)
      : getOacademicas
  );

  const { isLoading, data, error, isError, isSuccess, status } = useQuery({
    queryKey: queryKey,
    queryFn: queryFn,
  });

  watch(
    data,
    (oacademicas) => {
      if (oacademicas) {
        store.setOacademicas(oacademicas);
      }
    },
    { immediate: true }
  );
  return {
    oacademicas,
    isLoading,
    error,
    isError,
    isSuccess,
    status,
    getCsvOacademicas,
    //methods
    setEtapaId: (newEtapaId: string) => {
      etapaId.value = newEtapaId;
    },
  };
};

export default useOAcademicas;
