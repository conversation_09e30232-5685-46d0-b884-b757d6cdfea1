<script setup lang="ts">
import { computed, ref, toRef, watch } from 'vue'
import Panel from 'primevue/panel'
import { useToast } from "primevue/usetoast";
import { Form as VeeForm, useForm } from 'vee-validate'
import Dropdown from 'primevue/dropdown'
import * as yup from 'yup'
import mV from '../../utils/validationMessages'
import type { OAcademicaEtapa1PosgradoPostitulo } from '../interfaces/OAcademica';
import { acreditacionOptions, elegible_beca_pedagogiaOptions, area_destinoOPtions, requisito_ingresoOptions, regimenOptions } from '../helpers/selectValuesOacademica'
import useOAcademica from '../composables/useOAcademica'

interface Props {
    oAcademicaEtapa1PosgradoPostitulo: OAcademicaEtapa1PosgradoPostitulo,
    showDialog: boolean
}

const toast = useToast();
const props = defineProps<Props>();
const emit = defineEmits(['closeDialog']);
const OAcademicaEtapa1PosGradoPostitulo = ref<OAcademicaEtapa1PosgradoPostitulo>({ ...props.oAcademicaEtapa1PosgradoPostitulo });


const { oAcademicaEtapa1PosgradoPostituloMutation } = useOAcademica(props.oAcademicaEtapa1PosgradoPostitulo.oa_sies_id);

watch(oAcademicaEtapa1PosgradoPostituloMutation.isSuccess, (value) => {
    if (value) {
        toast.add({ life: 3000, severity: 'success', summary: 'Datos actualizados', detail: 'Los datos han sido actualizados correctamente' });
        oAcademicaEtapa1PosgradoPostituloMutation.reset();
    }

});

const schema = yup.object({
    regimen: yup.number().typeError(mV.number).required(mV.required).oneOf([1, 2, 3, 4]),
    duracion_regimen: yup.number().typeError(mV.number).required(mV.required),

    acreditacion: yup.number().typeError(mV.number).required(mV.required).oneOf([1, 2], "Debe ser Sí(1) o No(2)"),
    elegible_beca_pedagogia: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1, 2, 3], mV.oneOf),
    requisito_ingreso: yup.number().required(mV.required).oneOf([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], mV.oneOf),
    semestres_reconocidos: yup.number().typeError(mV.number).required(mV.required),
    area_destino_agricultura: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    area_destino_ciencias: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    area_destino_cs_sociales: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    area_destino_educacion: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    area_destino_humanidades: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    area_destino_ingenieria: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    area_destino_salud: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    area_destino_servicios: yup.number().typeError(mV.number).required(mV.required).oneOf([0, 1], mV.oneOf),
    malla_curricular: yup.string().required(mV.required),
    mail_difusion_carrera: yup.string().email(mV.email).required(mV.required),
})

// Configuración del formulario
const { handleSubmit, resetForm, setValues, setFieldValue, errors } = useForm({
    validationSchema: schema,
    initialValues: OAcademicaEtapa1PosGradoPostitulo.value, // Usamos los valores iniciales desde las props
});

// Manejador del formulario
const onSubmit = handleSubmit((values) => {
    console.log('Valores validados:', values);// Emitimos los datos validados al padre
    oAcademicaEtapa1PosgradoPostituloMutation.mutate(values);
});

</script>

<template>
    <div>
        <VeeForm @submit="onSubmit" class="mt-5">
            <div class="formgrid grid">
                <!-- regimen (Dropdown) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <Dropdown id="regimen" v-model="oAcademicaEtapa1PosgradoPostitulo.regimen"
                            :options="regimenOptions" optionLabel="name" optionValue="value"
                            :class="{ 'p-invalid': errors.regimen }"
                            @update:modelValue="setFieldValue('regimen', oAcademicaEtapa1PosgradoPostitulo.regimen)"
                            class="w-full" />
                        <label for="regimen">Régimen</label>
                        <small v-if="errors.regimen" class="p-error">{{ errors.regimen }}</small>
                    </FloatLabel>
                </div>

                <!-- duracion_regimen (InputNumber) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputNumber id="duracion_regimen" v-model="oAcademicaEtapa1PosgradoPostitulo.duracion_regimen"
                            :class="{ 'p-invalid': errors.duracion_regimen }"
                            @input="setFieldValue('duracion_regimen', oAcademicaEtapa1PosgradoPostitulo.duracion_regimen)"
                            class="w-full" />
                        <label for="duracion_regimen">Duración Régimen</label>
                        <small v-if="errors.duracion_regimen" class="p-error">{{ errors.duracion_regimen }}</small>
                    </FloatLabel>
                </div>

                <!-- cod_demre (InputNumber) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputNumber id="cod_demre" v-model="oAcademicaEtapa1PosgradoPostitulo.cod_demre"
                            :class="{ 'p-invalid': errors.cod_demre }"
                            @input="setFieldValue('cod_demre', oAcademicaEtapa1PosgradoPostitulo.cod_demre)"
                            class="w-full" />
                        <label for="cod_demre">Código DEMRE</label>
                        <small v-if="errors.cod_demre" class="p-error">{{ errors.cod_demre }}</small>
                    </FloatLabel>
                </div>

                <!-- acreditacion (Dropdown) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <Dropdown id="acreditacion" v-model="oAcademicaEtapa1PosgradoPostitulo.acreditacion"
                            :options="acreditacionOptions" optionLabel="name" optionValue="value"
                            :class="{ 'p-invalid': errors.acreditacion }"
                            @update:modelValue="setFieldValue('acreditacion', oAcademicaEtapa1PosgradoPostitulo.acreditacion)"
                            class="w-full" />
                        <label for="acreditacion">Acreditación (Dropdown)</label>
                        <small v-if="errors.acreditacion" class="p-error">{{ errors.acreditacion }}</small>
                    </FloatLabel>
                </div>

                <!-- elegible_beca_pedagogia (Dropdown) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <Dropdown id="elegible_beca_pedagogia"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.elegible_beca_pedagogia"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            :class="{ 'p-invalid': errors.elegible_beca_pedagogia }"
                            @update:modelValue="setFieldValue('elegible_beca_pedagogia', oAcademicaEtapa1PosgradoPostitulo.elegible_beca_pedagogia)"
                            class="w-full" />
                        <label for="elegible_beca_pedagogia">Elegible Beca Pedagogía</label>
                        <small v-if="errors.elegible_beca_pedagogia" class="p-error">{{ errors.elegible_beca_pedagogia
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- requisito_ingreso (Dropdown) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <Dropdown id="requisito_ingreso" v-model="oAcademicaEtapa1PosgradoPostitulo.requisito_ingreso"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            :class="{ 'p-invalid': errors.requisito_ingreso }"
                            @update:modelValue="setFieldValue('requisito_ingreso', oAcademicaEtapa1PosgradoPostitulo.requisito_ingreso)"
                            class="w-full" />
                        <label for="requisito_ingreso">Requisito Ingreso (Dropdown)</label>
                        <small v-if="errors.requisito_ingreso" class="p-error">{{ errors.requisito_ingreso }}</small>
                    </FloatLabel>
                </div>

                <!-- semestres_reconocidos (InputNumber) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputNumber id="semestres_reconocidos"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.semestres_reconocidos"
                            :class="{ 'p-invalid': errors.semestres_reconocidos }"
                            @input="setFieldValue('semestres_reconocidos', oAcademicaEtapa1PosgradoPostitulo.semestres_reconocidos)"
                            class="w-full" />
                        <label for="semestres_reconocidos">Semestres Reconocidos</label>
                        <small v-if="errors.semestres_reconocidos" class="p-error">{{ errors.semestres_reconocidos
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- area_destino_agricultura (Dropdown) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <Dropdown id="area_destino_agricultura"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.area_destino_agricultura"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            :class="{ 'p-invalid': errors.area_destino_agricultura }"
                            @update:modelValue="setFieldValue('area_destino_agricultura', oAcademicaEtapa1PosgradoPostitulo.area_destino_agricultura)"
                            class="w-full" />
                        <label for="area_destino_agricultura">Área Destino Agricultura</label>
                        <small v-if="errors.area_destino_agricultura" class="p-error">{{ errors.area_destino_agricultura
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- area_destino_ciencias (Dropdown) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <Dropdown id="area_destino_ciencias"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.area_destino_ciencias"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            :class="{ 'p-invalid': errors.area_destino_ciencias }"
                            @update:modelValue="setFieldValue('area_destino_ciencias', oAcademicaEtapa1PosgradoPostitulo.area_destino_ciencias)"
                            class="w-full" />
                        <label for="area_destino_ciencias">Área Destino Ciencias</label>
                        <small v-if="errors.area_destino_ciencias" class="p-error">{{ errors.area_destino_ciencias
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- area_destino_cs_sociales (Dropdown) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <Dropdown id="area_destino_cs_sociales"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.area_destino_cs_sociales"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            :class="{ 'p-invalid': errors.area_destino_cs_sociales }"
                            @update:modelValue="setFieldValue('area_destino_cs_sociales', oAcademicaEtapa1PosgradoPostitulo.area_destino_cs_sociales)"
                            class="w-full" />
                        <label for="area_destino_cs_sociales">Área Destino Ciencias Sociales</label>
                        <small v-if="errors.area_destino_cs_sociales" class="p-error">{{ errors.area_destino_cs_sociales
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- area_destino_educacion (Dropdown) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <Dropdown id="area_destino_educacion"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.area_destino_educacion"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            :class="{ 'p-invalid': errors.area_destino_educacion }"
                            @update:modelValue="setFieldValue('area_destino_educacion', oAcademicaEtapa1PosgradoPostitulo.area_destino_educacion)"
                            class="w-full" />
                        <label for="area_destino_educacion">Área Destino Educación</label>
                        <small v-if="errors.area_destino_educacion" class="p-error">{{ errors.area_destino_educacion
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- area_destino_humanidades (Dropdown) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <Dropdown id="area_destino_humanidades"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.area_destino_humanidades"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            :class="{ 'p-invalid': errors.area_destino_humanidades }"
                            @update:modelValue="setFieldValue('area_destino_humanidades', oAcademicaEtapa1PosgradoPostitulo.area_destino_humanidades)"
                            class="w-full" />
                        <label for="area_destino_humanidades">Área Destino Humanidades</label>
                        <small v-if="errors.area_destino_humanidades" class="p-error">{{ errors.area_destino_humanidades
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- area_destino_ingenieria (Dropdown) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <Dropdown id="area_destino_ingenieria"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.area_destino_ingenieria"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            :class="{ 'p-invalid': errors.area_destino_ingenieria }"
                            @update:modelValue="setFieldValue('area_destino_ingenieria', oAcademicaEtapa1PosgradoPostitulo.area_destino_ingenieria)"
                            class="w-full" />
                        <label for="area_destino_ingenieria">Área Destino Ingeniería</label>
                        <small v-if="errors.area_destino_ingenieria" class="p-error">{{ errors.area_destino_ingenieria
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- area_destino_salud (Dropdown) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <Dropdown id="area_destino_salud" v-model="oAcademicaEtapa1PosgradoPostitulo.area_destino_salud"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            :class="{ 'p-invalid': errors.area_destino_salud }"
                            @update:modelValue="setFieldValue('area_destino_salud', oAcademicaEtapa1PosgradoPostitulo.area_destino_salud)"
                            class="w-full" />
                        <label for="area_destino_salud">Área Destino Salud</label>
                        <small v-if="errors.area_destino_salud" class="p-error">{{ errors.area_destino_salud }}</small>
                    </FloatLabel>
                </div>

                <!-- area_destino_servicios (Dropdown) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <Dropdown id="area_destino_servicios"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.area_destino_servicios"
                            :options="area_destinoOPtions" optionLabel="name" optionValue="value"
                            :class="{ 'p-invalid': errors.area_destino_servicios }"
                            @update:modelValue="setFieldValue('area_destino_servicios', oAcademicaEtapa1PosgradoPostitulo.area_destino_servicios)"
                            class="w-full" />
                        <label for="area_destino_servicios">Área Destino Servicios (Dropdown)</label>
                        <small v-if="errors.area_destino_servicios" class="p-error">{{ errors.area_destino_servicios
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- ponderacion_nem (InputNumber) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputNumber id="ponderacion_nem" v-model="oAcademicaEtapa1PosgradoPostitulo.ponderacion_nem"
                            :class="{ 'p-invalid': errors.ponderacion_nem }"
                            @input="setFieldValue('ponderacion_nem', oAcademicaEtapa1PosgradoPostitulo.ponderacion_nem)"
                            class="w-full" />
                        <label for="ponderacion_nem">Ponderación NEM</label>
                        <small v-if="errors.ponderacion_nem" class="p-error">{{ errors.ponderacion_nem }}</small>
                    </FloatLabel>
                </div>

                <!-- ponderacion_ranking (InputNumber) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputNumber id="ponderacion_ranking"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.ponderacion_ranking"
                            :class="{ 'p-invalid': errors.ponderacion_ranking }"
                            @input="setFieldValue('ponderacion_ranking', oAcademicaEtapa1PosgradoPostitulo.ponderacion_ranking)"
                            class="w-full" />
                        <label for="ponderacion_ranking">Ponderación Ranking</label>
                        <small v-if="errors.ponderacion_ranking" class="p-error">{{ errors.ponderacion_ranking
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- ponderacion_c_lectora (InputNumber) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputNumber id="ponderacion_c_lectora"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.ponderacion_c_lectora"
                            :class="{ 'p-invalid': errors.ponderacion_c_lectora }"
                            @input="setFieldValue('ponderacion_c_lectora', oAcademicaEtapa1PosgradoPostitulo.ponderacion_c_lectora)"
                            class="w-full" />
                        <label for="ponderacion_c_lectora">Ponderación C. Lectora</label>
                        <small v-if="errors.ponderacion_c_lectora" class="p-error">{{ errors.ponderacion_c_lectora
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- ponderacion_matematica_1 (InputNumber) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputNumber id="ponderacion_matematica_1"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.ponderacion_matematica_1"
                            :class="{ 'p-invalid': errors.ponderacion_matematica_1 }"
                            @input="setFieldValue('ponderacion_matematica_1', oAcademicaEtapa1PosgradoPostitulo.ponderacion_matematica_1)"
                            class="w-full" />
                        <label for="ponderacion_matematica_1">Ponderación Matemática 1</label>
                        <small v-if="errors.ponderacion_matematica_1" class="p-error">{{ errors.ponderacion_matematica_1
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- ponderacion_matematica_2 (InputNumber) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputNumber id="ponderacion_matematica_2"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.ponderacion_matematica_2"
                            :class="{ 'p-invalid': errors.ponderacion_matematica_2 }"
                            @input="setFieldValue('ponderacion_matematica_2', oAcademicaEtapa1PosgradoPostitulo.ponderacion_matematica_2)"
                            class="w-full" />
                        <label for="ponderacion_matematica_2">Ponderación Matemática 2</label>
                        <small v-if="errors.ponderacion_matematica_2" class="p-error">{{ errors.ponderacion_matematica_2
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- ponderacion_historia (InputNumber) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputNumber id="ponderacion_historia"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.ponderacion_historia"
                            :class="{ 'p-invalid': errors.ponderacion_historia }"
                            @input="setFieldValue('ponderacion_historia', oAcademicaEtapa1PosgradoPostitulo.ponderacion_historia)"
                            class="w-full" />
                        <label for="ponderacion_historia">Ponderación Historia</label>
                        <small v-if="errors.ponderacion_historia" class="p-error">{{ errors.ponderacion_historia
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- ponderacion_ciencias (InputNumber) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputNumber id="ponderacion_ciencias"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.ponderacion_ciencias"
                            :class="{ 'p-invalid': errors.ponderacion_ciencias }"
                            @input="setFieldValue('ponderacion_ciencias', oAcademicaEtapa1PosgradoPostitulo.ponderacion_ciencias)"
                            class="w-full" />
                        <label for="ponderacion_ciencias">Ponderación Ciencias</label>
                        <small v-if="errors.ponderacion_ciencias" class="p-error">{{ errors.ponderacion_ciencias
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- ponderacion_otros (InputNumber) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputNumber id="ponderacion_otros"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.ponderacion_otros"
                            :class="{ 'p-invalid': errors.ponderacion_otros }"
                            @input="setFieldValue('ponderacion_otros', oAcademicaEtapa1PosgradoPostitulo.ponderacion_otros)"
                            class="w-full" />
                        <label for="ponderacion_otros">Ponderación Otros</label>
                        <small v-if="errors.ponderacion_otros" class="p-error">{{ errors.ponderacion_otros }}</small>
                    </FloatLabel>
                </div>

                <!-- vacantes_pace (InputNumber) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputNumber id="vacantes_pace" v-model="oAcademicaEtapa1PosgradoPostitulo.vacantes_pace"
                            :class="{ 'p-invalid': errors.vacantes_pace }"
                            @input="setFieldValue('vacantes_pace', oAcademicaEtapa1PosgradoPostitulo.vacantes_pace)"
                            class="w-full" />
                        <label for="vacantes_pace">Vacantes PACE</label>
                        <small v-if="errors.vacantes_pace" class="p-error">{{ errors.vacantes_pace }}</small>
                    </FloatLabel>
                </div>

                <!-- malla_curricular (InputText) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputText id="malla_curricular" v-model="oAcademicaEtapa1PosgradoPostitulo.malla_curricular"
                            :class="{ 'p-invalid': errors.malla_curricular }"
                            @input="setFieldValue('malla_curricular', oAcademicaEtapa1PosgradoPostitulo.malla_curricular)"
                            class="w-full" />
                        <label for="malla_curricular">Malla Curricular</label>
                        <small v-if="errors.malla_curricular" class="p-error">{{ errors.malla_curricular }}</small>
                    </FloatLabel>
                </div>

                <!-- perfil_egreso (InputText) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputText id="perfil_egreso" v-model="oAcademicaEtapa1PosgradoPostitulo.perfil_egreso"
                            :class="{ 'p-invalid': errors.perfil_egreso }"
                            @input="setFieldValue('perfil_egreso', oAcademicaEtapa1PosgradoPostitulo.perfil_egreso)"
                            class="w-full" />
                        <label for="perfil_egreso">Perfil Egreso</label>
                        <small v-if="errors.perfil_egreso" class="p-error">{{ errors.perfil_egreso }}</small>
                    </FloatLabel>
                </div>

                <!-- texto_requerido_ingreso (InputText) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputText id="texto_requerido_ingreso"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.texto_requerido_ingreso"
                            :class="{ 'p-invalid': errors.texto_requerido_ingreso }"
                            @input="setFieldValue('texto_requerido_ingreso', oAcademicaEtapa1PosgradoPostitulo.texto_requerido_ingreso)"
                            class="w-full" />
                        <label for="texto_requerido_ingreso">Texto Requerido Ingreso</label>
                        <small v-if="errors.texto_requerido_ingreso" class="p-error">{{ errors.texto_requerido_ingreso
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- otros_requisitos (InputText) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputText id="otros_requisitos" v-model="oAcademicaEtapa1PosgradoPostitulo.otros_requisitos"
                            :class="{ 'p-invalid': errors.otros_requisitos }"
                            @input="setFieldValue('otros_requisitos', oAcademicaEtapa1PosgradoPostitulo.otros_requisitos)"
                            class="w-full" />
                        <label for="otros_requisitos">Otros Requisitos</label>
                        <small v-if="errors.otros_requisitos" class="p-error">{{ errors.otros_requisitos }}</small>
                    </FloatLabel>
                </div>

                <!-- mail_difusion_carrera (InputText) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputText id="mail_difusion_carrera"
                            v-model="oAcademicaEtapa1PosgradoPostitulo.mail_difusion_carrera"
                            :class="{ 'p-invalid': errors.mail_difusion_carrera }"
                            @input="setFieldValue('mail_difusion_carrera', oAcademicaEtapa1PosgradoPostitulo.mail_difusion_carrera)"
                            class="w-full" />
                        <label for="mail_difusion_carrera">Mail Difusión Carrera</label>
                        <small v-if="errors.mail_difusion_carrera" class="p-error">{{ errors.mail_difusion_carrera
                            }}</small>
                    </FloatLabel>
                </div>

                <!-- vigencia_carrera (InputText) -->
                <div class="field col-12 md:col-6 lg:col-3 mt-3">
                    <FloatLabel>
                        <InputText id="vigencia_carrera" v-model="oAcademicaEtapa1PosgradoPostitulo.vigencia_carrera"
                            :class="{ 'p-invalid': errors.vigencia_carrera }"
                            @input="setFieldValue('vigencia_carrera', oAcademicaEtapa1PosgradoPostitulo.vigencia_carrera)"
                            class="w-full" />
                        <label for="vigencia_carrera">Vigencia Carrera</label>
                        <small v-if="errors.vigencia_carrera" class="p-error">{{ errors.vigencia_carrera }}</small>
                    </FloatLabel>
                </div>
            </div>

            <div class="flex justify-content-end gap-2">
                <ProgressSpinner v-if="oAcademicaEtapa1PosgradoPostituloMutation.isPending.value" style="width:38px; height:38px;" strokeWidth="7"
                    aria-label="Loading" />
                <Button v-if="!oAcademicaEtapa1PosgradoPostituloMutation.isPending.value" type="button" label="Cancelar" severity="secondary"
                    @click="emit('closeDialog')" />
                <Button v-if="!oAcademicaEtapa1PosgradoPostituloMutation.isPending.value" type="submit" label="Guardar" />
            </div>
        </VeeForm>
    </div>
</template>


<style scoped></style>