<template>
    <!-- Loading -->
    <div v-if="globalLoading" class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
        <div class='surface-card shadow-3 p-3 border-round'>
            <Loading />
        </div>
    </div>
    <div v-else>
        <!-- Cards -->
        <div v-if="!isLoading">

            <div class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
                <div class='surface-card shadow-3 p-3 border-round'>
                    <form @submit.prevent="submitForm">
                        <div
                            style="display: flex;justify-content: space-between;align-items: center;padding-left: 1rem;">
                            <div style="display: flex;justify-content: space-between;align-items: center;">
                                <Button @click.prevent="goBack()"> <- </Button>
                                        <h2 style="padding-left: 1rem;">Detalle Inmueble Permanente - Proceso {{
                                            anio_proceso }}
                                        </h2>
                            </div>

                            <Button
                                v-if="infraestructuraRecursoData.is_finalized != true && (hasAccess('Encargado', 'OTI') || hasAccess('Usuario General', 'OTI') || hasAccess('Administrador Sistema', 'Administracion PIU'))"
                                :disabled="editForm" @click="editingForm">Editar</Button>
                        </div>
                        <br />

                        <!--Informacion Basica-->
                        <div>
                            <h3 style="padding-left: 1rem;">Información básica</h3>
                            <div style="margin-left: 1rem;margin-right: 1rem;">
                                <div v-if="true" class="formgrid grid">
                                    <div class="field col-6" v-for="(field, index) in formFormat1" :key="field.id">
                                        <label :for="field.name" style="padding-right: 1rem ;">{{ field.displayName
                                            }}</label>
                                        <i class="pi pi-info-circle" v-if="!field.dontNeedtoolTip"
                                            style="justify-content: center " v-tooltip.bottom="field.tooltip" />
                                        <InputText :class="{
                                            'w-full': true, 'p-invalid': (formData[field.name] == null || formData[field.name] == '') && submitted
                                        }" v-if="field.contenedor === 'inputText'" :id=field.name autocomplete="off"
                                            :placeholder=field.placeholder :type=field.type style="height:2.6rem"
                                            v-model="formData[field.name]" @input="handleInput($event, field.name)"
                                            :disabled="field.disabled" :maxlength="200">
                                        </InputText>
                                        <Dropdown :class="{
                                            'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                                        }" v-if="field.contenedor === 'dropdown'" v-model="formData[field.name]" filter
                                            :placeholder="field.placeholder" style="height:2.6rem;margin-bottom: 1rem"
                                            :options="getOptions(field.options)" :optionLabel="field.optionLabel"
                                            :optionValue="field.optionValue" class="w-full" :disabled="field.disabled">
                                        </Dropdown>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--Situación del inmueble-->
                        <div>
                            <h3 style="padding-left: 1rem;">Situación del inmueble</h3>
                            <div style="margin-left: 1rem;margin-right: 1rem;">
                                <div v-if="true" class="formgrid grid">
                                    <div class="field col-6" v-for="(field, index) in formFormat2" :key="field.id">
                                        <label
                                            v-if="field.contenedor != 'inputNumberConditional' && field.contenedor != 'inputTextConditional'"
                                            :for="field.name" style="padding-right: 1rem ;">{{ field.displayName
                                            }}</label>

                                        <i v-if="field.contenedor != 'inputNumberConditional' && field.contenedor != 'inputTextConditional'"
                                            class="pi pi-info-circle" style="justify-content: center "
                                            v-tooltip.bottom="field.tooltip" />
                                        <label
                                            v-if="formData.uso_exclusivo == 2 && (field.contenedor == 'inputNumberConditional' || field.contenedor == 'inputTextConditional')"
                                            :for="field.name" style="padding-right: 1rem ;">{{ field.displayName
                                            }}</label>
                                        <i v-if="formData.uso_exclusivo == 2 && (field.contenedor == 'inputNumberConditional' || field.contenedor == 'inputTextConditional')"
                                            class="pi pi-info-circle" style="justify-content: center "
                                            v-tooltip.bottom="field.tooltip" />

                                        <Dropdown :class="{
                                            'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                                        }" v-if="field.contenedor === 'dropdown'" v-model="formData[field.name]" filter
                                            :placeholder="field.placeholder" style="height:2.6rem;margin-bottom: 1rem"
                                            :options="getOptions(field.options)" :optionLabel="field.optionLabel"
                                            :optionValue="field.optionValue" class="w-full" :disabled="field.disabled">
                                        </Dropdown>
                                        <Calendar :class="{
                                            'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                                        }" v-if="field.contenedor === 'calendar'" id="calendar"
                                            class="w-full custom-dropdown" style="margin-top: 0.05rem;"
                                            v-model="formData[field.name]" :placeholder="field.placeholder"
                                            dateFormat="yy" view="year" :disabled="field.disabled" :minDate="minDate"
                                            :maxDate="maxDate">
                                        </Calendar>
                                        <InputGroup>
                                            <InputNumber :class="{
                                                'w-full': true, 'p-invalid': formData[field.name] == null &&
                                                    submitted
                                            }" v-if="field.contenedor === 'inputNumberConditional' && formData.uso_exclusivo == 2"
                                                inputId="withoutgrouping" :useGrouping="false" id="inputNumber"
                                                class="w-full inputNumber" style="margin-top: 0.05rem;"
                                                v-model="formData[field.name]" :placeholder="field.placeholder"
                                                :inputProps="{ autocomplete: 'off' }" :min="1" :max="99"
                                                :disabled="field.disabled">
                                            </InputNumber>
                                            <InputGroupAddon
                                                v-if="field.contenedor === 'inputNumberConditional' && formData.uso_exclusivo == 2">
                                                <i class="pi pi-percentage"></i>
                                            </InputGroupAddon>
                                        </InputGroup>
                                        <InputText :class="{
                                            'w-full': true, 'p-invalid': (formData[field.name] == null || formData[field.name] == '') && submitted
                                        }" v-if="field.contenedor === 'inputTextConditional' && formData.uso_exclusivo == 2"
                                            :id=field.name autocomplete="off" :placeholder=field.placeholder
                                            :type=field.type style="height:2.6rem" v-model="formData[field.name]"
                                            :modelValue="field.modelValue" @input="handleInput($event, field.name)"
                                            :disabled="field.disabled" :maxlength="200">
                                        </InputText>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--CONDICIONAL: Informacion del inmueble que no es propiedad de la institución-->
                        <div v-if="formData.situacion_tenencia != 1 && formData.situacion_tenencia != null">
                            <h3 style="padding-left: 1rem;">Información del inmueble que no es propiedad de la
                                institución
                            </h3>
                            <div style="margin-left: 1rem;margin-right: 1rem;">
                                <div v-if="true" class="formgrid grid">
                                    <div class="field col-6" v-for="(field, index) in formFormat3" :key="field.id">
                                        <label
                                            v-if="formData.situacion_tenencia > 1 && formData.situacion_tenencia < 7 && field.contenedor === 'calendarConditional1'"
                                            :for="field.name" style="padding-right: 1rem">{{
                                                field.displayName }}</label>
                                        <label
                                            v-if="formData.situacion_tenencia > 1 && formData.situacion_tenencia < 6 && field.contenedor === 'calendarConditional2'"
                                            :for="field.name" style="padding-right: 1rem">{{
                                                field.displayName }}</label>
                                        <label
                                            v-if="formData.situacion_tenencia == 6 && field.contenedor === 'inputTextConditional'"
                                            :for="field.name" style="padding-right: 1rem">{{
                                                field.displayName }}</label>
                                        <i v-if="formData.situacion_tenencia > 1 && formData.situacion_tenencia < 7 && field.contenedor === 'calendarConditional1'"
                                            class=" pi pi-info-circle" style="justify-content: center"
                                            v-tooltip.bottom="field.tooltip" />
                                        <i v-if="formData.situacion_tenencia > 1 && formData.situacion_tenencia < 6 && field.contenedor === 'calendarConditional2'"
                                            class=" pi pi-info-circle" style="justify-content: center"
                                            v-tooltip.bottom="field.tooltip" />
                                        <i v-if="formData.situacion_tenencia == 6 && field.contenedor === 'inputTextConditional'"
                                            class=" pi pi-info-circle" style="justify-content: center"
                                            v-tooltip.bottom="field.tooltip" />
                                        <Calendar :class="{
                                            'w-full': true, 'p-invalid': ((formData[field.name] == null || formData[field.name] == '') || inicioEsMayorTermino) && submitted
                                        }" v-if="formData.situacion_tenencia > 1 && formData.situacion_tenencia < 7 && field.contenedor === 'calendarConditional1'"
                                            id="calendar" class="w-full custom-dropdown" style="margin-top: 0.05rem;"
                                            v-model="formData[field.name]" :placeholder="field.placeholder" view="month"
                                            dateFormat="yy/mm" :disabled="field.disabled" :minDate="minDate">
                                        </Calendar>
                                        <Calendar :class="{
                                            'w-full': true, 'p-invalid': ((formData[field.name] == null || formData[field.name] == '') || inicioEsMayorTermino || fechaTerminoInvalid) && submitted
                                        }" v-if="formData.situacion_tenencia > 1 && formData.situacion_tenencia < 6 && field.contenedor === 'calendarConditional2'"
                                            id="calendar" class="w-full custom-dropdown" style="margin-top: 0.05rem;"
                                            v-model="formData[field.name]" :placeholder="field.placeholder" view="month"
                                            dateFormat="yy/mm" :disabled="field.disabled" :minDate="minDate">
                                        </Calendar>
                                        <InputText :class="{
                                            'w-full': true, 'p-invalid': (formData[field.name] == null || formData[field.name] == '') && submitted
                                        }" v-if="formData.situacion_tenencia == 6 && field.contenedor === 'inputTextConditional'"
                                            :id=field.name :placeholder=field.placeholder :type=field.type
                                            style="height:2.6rem" v-model="formData[field.name]"
                                            :modelValue="field.modelValue" autocomplete="off"
                                            @input="handleInput($event, field.name)" :disabled="field.disabled"
                                            :maxlength="200">
                                        </InputText>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Principal funcion del inmueble -->
                        <div>
                            <h3 style="padding-left: 1rem;">Principal función del inmueble</h3>
                            <div style="margin-left: 1rem;margin-right: 1rem;">
                                <div v-if="true" class="formgrid grid">
                                    <div class="field col-6" v-for="(field, index) in formFormat4" :key="field.id">
                                        <label style="padding-right: 1rem;"
                                            v-if="field.contenedor !== 'inputTextConditional'" :for="field.name">{{
                                                field.displayName }}</label>
                                        <i v-if="field.contenedor !== 'inputTextConditional'" class=" pi pi-info-circle"
                                            style="justify-content: center" v-tooltip.bottom="field.tooltip" />
                                        <label style="padding-right: 1rem;"
                                            v-if="formData.funcion.includes('funcion_otras') && field.contenedor === 'inputTextConditional'"
                                            :for="field.name">{{ field.displayName }}</label>
                                        <i v-if="formData.funcion.includes('funcion_otras') && field.contenedor === 'inputTextConditional'"
                                            class=" pi pi-info-circle" style="justify-content: center"
                                            v-tooltip.bottom="field.tooltip" />
                                        <Dropdown :class="{
                                            'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                                        }" v-if="field.contenedor === 'dropdown'" v-model="formData[field.name]" filter
                                            :placeholder="field.placeholder" style="height:2.6rem;margin-bottom: 1rem"
                                            :options="getOptions(field.options)" :optionLabel="field.optionLabel"
                                            :optionValue="field.optionValue" class="w-full" :disabled="field.disabled">
                                        </Dropdown>
                                        <MultiSelect :class="{
                                            'w-full': true, 'p-invalid': ((formData[field.name] || []).length === 0) && submitted
                                        }" v-if="field.contenedor === 'multiselect'" v-model="formData[field.name]"
                                            :placeholder="field.placeholder" style="height:2.6rem;margin-bottom: 1rem"
                                            :options="getOptions(field.options)" :optionLabel="field.optionLabel"
                                            :optionValue="field.optionValue" class="w-full" :disabled="field.disabled">
                                            <template #option="slotProps">

                                                <div>{{ slotProps.option.options }}</div>
                                                <i class="pi pi-info-circle ml-2"
                                                    v-tooltip.bottom="slotProps.option.tooltip" v-if="true"
                                                    style="cursor: pointer"></i>

                                            </template>
                                        </MultiSelect>
                                        <InputText :class="{
                                            'w-full': true, 'p-invalid': (formData[field.name] == null || formData[field.name] == '') && submitted
                                        }" v-if="formData.funcion.includes('funcion_otras') && field.contenedor === 'inputTextConditional'"
                                            :id=field.name :placeholder=field.placeholder :type=field.type
                                            style="height:2.6rem" v-model="formData[field.name]"
                                            :modelValue="field.modelValue" autocomplete="off"
                                            @input="handleInput($event, field.name)" :disabled="field.disabled"
                                            :maxlength="200">
                                        </InputText>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Características del inmueble -->
                        <div>
                            <h3 style="padding-left: 1rem;">Características del inmueble</h3>
                            <div style="margin-left: 1rem;margin-right: 1rem;">
                                <div v-if="true" class="formgrid grid">
                                    <div class="field col-6" v-for="(field, index) in formFormat5" :key="field.id">
                                        <label style="padding-right: 1rem;" :for="field.name">{{ field.displayName
                                            }}</label>
                                        <i style="justify-content: center" class=" pi pi-info-circle"
                                            v-tooltip.bottom="field.tooltip" />
                                        <InputNumber :class="{
                                            'w-full': true, 'p-invalid': formData[field.name] == null &&
                                                submitted || ((field.name === 'total_m2_salas_clases' || field.name === 'total_m2_edificados') && salasConstruidosMayorTotalTerreno)
                                        }" v-if="field.contenedor === 'inputNumberDecimal'" inputId="minmaxfraction"
                                            :maxFractionDigits="1" id="inputNumberDecimal"
                                            class="w-full inputNumberDecimal" style="margin-top: 0.05rem;"
                                            v-model="formData[field.name]" :placeholder="field.placeholder" :min="0"
                                            :inputProps="{ autocomplete: 'off' }" :disabled="field.disabled"
                                            :max="9999999">
                                        </InputNumber>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Vigencia -->
                        <div>
                            <h3 style="padding-left: 1rem;">Vigencia de los registros de infraestructura informados</h3>
                            <div style="margin-left: 1rem;margin-right: 1rem;">
                                <div v-if="true" class="formgrid grid">
                                    <div class="field col-6" v-for="(field, index) in formFormat6" :key="field.id">
                                        <label style="padding-right: 1rem;" :for="field.name">{{ field.displayName
                                            }}</label>
                                        <i style="justify-content: center" class=" pi pi-info-circle"
                                            v-tooltip.bottom="field.tooltip" />
                                        <Dropdown :class="{
                                            'w-full': true, 'p-invalid': formData[field.name] == null && submitted
                                        }" v-if="field.contenedor === 'dropdown'" v-model="formData[field.name]" filter
                                            :placeholder="field.placeholder" style="height:2.6rem;margin-bottom: 1rem"
                                            :options="getOptions(field.options)" :optionLabel="field.optionLabel"
                                            :optionValue="field.optionValue" class="w-full" :disabled="field.disabled">
                                        </Dropdown>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Buttons actualizar - cancelar -->
                        <div
                            style="display: flex; justify-content: center; align-items: center; padding-top: 0.5rem; padding-bottom: 0.5rem;">
                            <Button v-if="editForm" label="Actualizar" @click.prevent="submitForm" />
                            <Button v-if="editForm" severity="danger" @click="cancelEditingForm"
                                style="margin-left: 1rem;">Cancelar</Button>
                        </div>
                    </form>
                </div>
            </div>
            <Toast position="bottom-left" group="bl" />
        </div>
        <div v-else class='surface-ground px-4 py-5 md:px-6 lg:px-8'>
            <div class='surface-card shadow-3 p-3 border-round'>
                <Loading />
            </div>
        </div>
    </div>
</template>

<script>
import { useAuthStore } from '../../../store/auth';
import { ref, computed, onMounted, watch } from 'vue';
import { encryptData, decryptData } from '@/utils/crypto';
import axios from 'axios';
import InputText from 'primevue/inputtext';
import { useToast } from 'primevue/usetoast';
import { useRouter } from 'vue-router';
import { max, min } from 'date-fns';

export default {
    props: {
        inmueblePermanente_id: {
            type: String,
            required: true,
        },
    },

    setup(props) {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        const router = useRouter();
        const authStore = useAuthStore();
        const toast = useToast();
        // Obtain the stored user data from pinia and the global loading state
        const userName = computed(() => decryptData(authStore.userName));
        const userEmail = computed(() => decryptData(authStore.userEmail));
        const permissionsList = computed(() => decryptData(authStore.permissionsList));
        const isUserAuthenticated = computed(() => decryptData(authStore.userIsAuthenticated));
        const globalLoading = computed(() => decryptData(authStore.isLoading));
        const userToken = computed(() => decryptData(authStore.idToken));
        const listaComunas =  ref([]);

        // Variable to store InfraestructuraRecurso data
        const inmueblePermanenteData = ref(null);
        const infraestructuraRecursoData = ref({});
        const fechaTerminoInvalid = ref(false);
        const inicioEsMayorTermino = ref(false);
        const salasConstruidosMayorTotalTerreno = ref(false);
        const minDate = ref(new Date(1800, 0, 1));
        const maxDate = ref(null);

        // Theme for changing the actual theme from dark to light or viceversa
        const theme = ref("dark");
        const editForm = ref(false);

        // Local "Loading" for the current view
        const isLoading = ref(false);
        const formIsLoading = ref(false);

        const anio_proceso = ref(null); // Access the param
        const submitted = ref(false);

        const hasAccess = (allowedRol, requiredRecurso) => {
            const access = permissionsList.value.some(user => user.rol === allowedRol && user.recurso === requiredRecurso);
            return access;
        };

        // Define initial form data
        const initialFormData = {
            tipo_infraestructura: "Inmueble de uso Permanente",
            nombre_identificacion: null,
            comuna: null,
            direccion_inmueble: null,
            situacion_tenencia: null,
            anio_inicio_uso_inmueble: null,
            uso_exclusivo: null,
            porcentaje_uso: null,
            nombre_institucion_comparte: "",
            fecha_inicio_tenencia: "",
            fecha_termino: "",
            descripcion_otra_tenencia: "",
            funcion: [],
            desc_otras_funciones: "",
            total_m2_terreno: null,
            total_m2_edificados: null,
            total_salas_clases: null,
            capacidad_salas_clases: null,
            total_m2_salas_clases: null,
            total_auditorios: null,
            capacidad_auditorios: null,
            total_m2_auditorios: null,
            total_laboratorios: null,
            total_m2_laboratorios: null,
            total_talleres: null,
            total_m2_talleres: null,
            total_pc_nb_disponible: null,
            total_m2_casinos_cafeterias: null,
            total_m2_areas_verdes: null,
            vigencia: 1,
            creador_email: userEmail.value,
            creador_id: "sin asignar",
            anio_proceso: anio_proceso
        };

        const formData = ref({ ...initialFormData });
        const formFormat1 = computed(() => [
            { id: 1, placeholder: '', name: 'tipo_infraestructura', displayName: 'Seleccione tipo de infraestructura', type: 'text', contenedor: 'inputText', disabled: 'true', dontNeedtoolTip: true, disabled: true },
            { id: 2, placeholder: 'Nombre inmueble', name: 'nombre_identificacion', displayName: 'Ingrese nombre o identificación del inmueble', type: 'text', contenedor: 'inputText', tooltip: 'Nombre con que la institución identifica el inmueble. Varios inmuebles cercanos pueden tener el mismo nombre o identificación.', disabled: !editForm.value },
            { id: 3, placeholder: 'Comuna', name: 'comuna', displayName: 'Seleccione comuna', type: 'text', contenedor: 'dropdown', options: 'listaComunas', optionLabel: 'nombre_comuna', optionValue: "nombre_comuna", tooltip: 'Corresponde a la comuna en que se encuentra el inmueble que está siendo informado.', disabled: !editForm.value },
            { id: 4, placeholder: 'Dirección inmueble', name: 'direccion_inmueble', displayName: 'Ingrese dirección del inmueble', type: 'text', contenedor: 'inputText', tooltip: 'Indicación del nombre de la calle y la numeración completa que corresponden a la ubicación física del inmueble que se está informando.', disabled: !editForm.value },
        ]);
        const formFormat2 = computed(() => [
            { id: 5, placeholder: 'Situacion tenencia', name: 'situacion_tenencia', displayName: 'Seleccione situacion de tenencia del inmueble', type: 'text', contenedor: 'dropdown', options: 'situacion_tenencia_list', optionLabel: 'options', optionValue: "value", tooltip: 'Refiere a la figura bajo la cual la institución mantiene posesión y/o tenencia del inmueble informado.', disabled: !editForm.value },
            { id: 6, placeholder: 'Año de inicio de uso', name: 'anio_inicio_uso_inmueble', displayName: 'Ingrese año de inicio de uso del inmueble', contenedor: 'calendar', tooltip: 'Corresponde al año en que la institución comenzó a utilizar el inmueble que se está informando.', disabled: !editForm.value },
            { id: 7, placeholder: 'Tipo de uso', name: 'uso_exclusivo', displayName: 'Ingrese tipo de uso del inmueble', type: 'text', contenedor: 'dropdown', options: 'uso_exclusivo_list', optionLabel: 'options', optionValue: "value", tooltip: 'Si el inmueble que se está informando es de uso exclusivo de la institución o si lo comparte con alguna otra institución u organización.', disabled: !editForm.value },
            { id: 8, placeholder: 'Porcentaje de uso', name: 'porcentaje_uso', displayName: 'Ingrese porcentaje de uso', contenedor: 'inputNumberConditional', tooltip: 'Estimación respecto del porcentaje de uso que la institución realiza del inmueble, donde lo que usa la institución, más los porcentajes de uso que hace(n) la(s) otra(s) institución con quien se comparte el inmueble, suma el 100%.', disabled: !editForm.value },
            { id: 9, placeholder: 'Nombre intitución compartida', name: 'nombre_institucion_comparte', displayName: 'Ingrese nombre de la institución con quien comparte inmueble', type: 'text', contenedor: 'inputTextConditional', tooltip: 'Nombre de la(s) institución(es) con la(s) que se comparte el inmueble que se está informando.', disabled: !editForm.value },
        ]);
        const formFormat3 = computed(() => [
            { id: 10, placeholder: 'Fecha inicio tenencia', name: 'fecha_inicio_tenencia', displayName: 'Ingrese la fecha de inicio de la tenencia del inmueble', contenedor: 'calendarConditional1', tooltip: 'Corresponde a la fecha en que se inició el comodato del inmueble según lo establecido en el contrato correspondiente.', disabled: !editForm.value },
            { id: 11, placeholder: 'Fecha termino tenencia', name: 'fecha_termino', displayName: 'Ingrese la fecha de termino de la tenencia del inmueble', contenedor: 'calendarConditional2', tooltip: 'Corresponde a la fecha en que expira el arriendo, comodato, contrato de usufructo o contrato firmado de leasing o leaseback del inmueble según lo establecido en el contrato correspondiente.', disabled: !editForm.value },
            { id: 12, placeholder: 'Descripción tenencia', name: 'descripcion_otra_tenencia', displayName: 'Descripción otra tenencia', type: 'text', contenedor: 'inputTextConditional', tooltip: 'Corresponde a la explicación clara de situación de tenencia del inmueble informado como "Otro"', disabled: !editForm.value },
        ]);
        const formFormat4 = computed(() => [
            { id: 13, placeholder: 'Seleccionar funcion del inmueble', name: 'funcion', displayName: 'Seleccione la funcion principal del inmueble', type: 'text', contenedor: 'multiselect', options: 'funcion_list', optionLabel: 'options', optionValue: "value", tooltip: '', disabled: !editForm.value, tooltip: 'Seleccionar uno o más elementos' },
            { id: 14, placeholder: 'Descripción otras funciones', name: 'desc_otras_funciones', displayName: 'Descripción otras funciones del inmueble', type: 'text', contenedor: 'inputTextConditional', tooltip: 'Corresponde a una breve descripción de las funciones que se desarrollan en el inmueble informado.', disabled: !editForm.value },

        ]);
        const formFormat5 = computed(() => [
            { id: 15, placeholder: 'Total de m2', name: 'total_m2_terreno', displayName: 'Ingrese total de m2 del inmueble', contenedor: 'inputNumberDecimal', tooltip: 'Corresponde al total de metros cuadrados de terreno del inmueble.', disabled: !editForm.value },
            { id: 16, placeholder: 'Total de m2 edificados', name: 'total_m2_edificados', displayName: 'Ingrese total de m2 edificados del inmueble', contenedor: 'inputNumberDecimal', tooltip: 'Corresponde al total de metros cuadrados edificados del inmueble.', disabled: !editForm.value },
            { id: 17, placeholder: 'N° total de salas de clases', name: 'total_salas_clases', displayName: 'Ingrese N° total de salas de clases', contenedor: 'inputNumberDecimal', tooltip: 'Corresponde a la suma de todas las salas de clases con que cuenta el inmueble.', disabled: !editForm.value },
            { id: 18, placeholder: 'Capacidad total salas de clases', name: 'capacidad_salas_clases', displayName: 'Ingrese capacidad total salas de clases', contenedor: 'inputNumberDecimal', tooltip: 'Corresponde a la suma de las capacidades (en nº de estudiantes) de todas las salas de clases con que cuenta el inmueble. La capacidad total de las salas debe medirse por la cantidad de pupitres o sillas disponibles simultáneamente.', disabled: !editForm.value },
            { id: 19, placeholder: 'Total de m2 de salas de clase', name: 'total_m2_salas_clases', displayName: 'Ingrese total de m2 de salas de clase', contenedor: 'inputNumberDecimal', tooltip: 'Corresponde a la suma de los metros cuadrados de todas las salas de clases con que cuenta el inmueble.', disabled: !editForm.value },
            { id: 20, placeholder: 'N° total de auditorios', name: 'total_auditorios', displayName: 'Ingrese N° total de auditorios', contenedor: 'inputNumberDecimal', tooltip: 'Corresponde a la suma de todos los auditorios con que cuenta el inmueble.', disabled: !editForm.value },
            { id: 21, placeholder: 'Capacidad total de los auditorios', name: 'capacidad_auditorios', displayName: 'Ingrese capacidad total de los auditorios', contenedor: 'inputNumberDecimal', tooltip: 'Corresponde a la suma de las capacidades (en nº de asistentes sentados) de todos los auditorios con que cuenta el inmueble.', disabled: !editForm.value },
            { id: 22, placeholder: 'N° total de m2 de auditorios', name: 'total_m2_auditorios', displayName: 'Ingrese N° total de m2 de auditorios', contenedor: 'inputNumberDecimal', tooltip: 'Corresponde a la suma de los metros cuadrados de todos los auditorios con que cuenta el inmueble.', disabled: !editForm.value },
            { id: 23, placeholder: 'N° total de laboratorios', name: 'total_laboratorios', displayName: 'Ingrese N° total de laboratorios', contenedor: 'inputNumberDecimal', tooltip: 'Corresponde a la suma de todos los laboratorios con que cuenta el inmueble.', disabled: !editForm.value },
            { id: 24, placeholder: 'N° total de m2 de laboratorios', name: 'total_m2_laboratorios', displayName: 'Ingrese N° total de m2 de laboratorios', contenedor: 'inputNumberDecimal', tooltip: 'Corresponde a la suma de los metros cuadrados de laboratorios con que cuenta el inmueble.', disabled: !editForm.value },
            { id: 25, placeholder: 'N° total de talleres', name: 'total_talleres', displayName: 'Ingrese N° total de talleres', contenedor: 'inputNumberDecimal', tooltip: 'Corresponde a la suma de todos los talleres con que cuenta el inmueble.', disabled: !editForm.value },
            { id: 26, placeholder: 'N° total de m2 de talleres', name: 'total_m2_talleres', displayName: 'Ingrese N° total de m2 de talleres', contenedor: 'inputNumberDecimal', tooltip: 'Corresponde a la suma de los metros cuadrados de talleres con que cuenta el inmueble.', disabled: !editForm.value },
            { id: 27, placeholder: 'N° de PC Y NB disponibles para estudiantes', name: 'total_pc_nb_disponible', displayName: 'Ingrese N° de PC Y Notebooks disponibles para estudiantes', contenedor: 'inputNumberDecimal', tooltip: 'Corresponde a la suma de los computadores (PC’s y Notebooks) disponibles para los estudiantes con que cuenta el inmueble.', disabled: !editForm.value },
            { id: 28, placeholder: 'N° de m2 de casinos,patios de comida o cafetarias', name: 'total_m2_casinos_cafeterias', displayName: 'Ingrese N° de m2 de casinos,patios de comida o cafetarias', contenedor: 'inputNumberDecimal', tooltip: 'Corresponde a la suma de metros cuadrados de casinos, patios de comida y cafeterías disponibles dentro del inmueble.', disabled: !editForm.value },
            { id: 29, placeholder: 'N° de m2 de áreas verdes y de esparcimiento', name: 'total_m2_areas_verdes', displayName: 'Ingrese N° de m2 de áreas verdes y de esparcimiento', contenedor: 'inputNumberDecimal', tooltip: 'Corresponde a la suma de metros cuadrados de áreas verdes y de esparcimiento con que cuenta el inmueble.', disabled: !editForm.value },
        ]);
        const formFormat6 = computed(() => [
            { id: 30, placeholder: 'Vigencia del inmueble', name: 'vigencia', displayName: 'Seleccione la vigencia del inmueble', contenedor: 'dropdown', options: 'vigencia_list', optionLabel: 'options', optionValue: "value", tooltip: 'Variable utilizada para mantener o eliminar un registro cargado.', disabled: !editForm.value },
        ]);
        const comuna_list = ref([
            { options: 'Antofagasta', value: "Antofagasta" },
            { options: 'Mejillones', value: "Mejillones" },
            { options: 'Sierra Gorda', value: "Sierra Gorda" },
            { options: 'Taltal', value: "Taltal" },
            { options: 'Calama', value: "Calama" },
            { options: 'San Pedro de Atacama', value: "San Pedro de Atacama" },
            { options: 'Tocopilla', value: "Tocopilla" },
            { options: 'María Elena', value: "María Elena" },
            { options: 'Providencia', value: "Providencia" },
        ]);
        const situacion_tenencia_list = ref([
            { options: 'Propio', value: 1 },
            { options: 'Arrendado', value: 2 },
            { options: 'En Comodato', value: 3 },
            { options: 'En Usufructo', value: 4 },
            { options: 'Leasing o LeaseBack', value: 5 },
            { options: 'Otro', value: 6 },
        ]);
        const uso_exclusivo_list = ref([
            { options: 'Uso Exclusivo de la Institución', value: 1 },
            { options: 'Uso Compartido con otra Institución', value: 2 },
        ]);
        const funcion_list = ref([
            { options: 'Docencia', value: "funcion_docencia", tooltip: "Corresponde al inmueble destinado al desarrollo de actividades relacionadas directamente con el proceso de enseñanza-aprendizaje, principalmente en espacios como aulas, laboratorios, talleres, bibliotecas u otros similares." },
            { options: 'Investigación', value: "funcion_investigacion", tooltip: "Corresponde al inmueble destinado al desarrollo de actividades relacionadas con la investigación académica." },
            { options: 'Extensión', value: "funcion_extension", tooltip: "Corresponde al inmueble destinado al desarrollo de actividades de extensión de la institución (desarrollo artístico, cultural, formativo, etc.)." },
            { options: 'Administración u oficinas', value: "funcion_adm_oficinas", tooltip: "Corresponde al inmueble destinado al desarrollo de actividades de administración o gestión de la institución (oficinas)." },
            { options: 'Otras', value: "funcion_otras", tooltip: "Corresponde al inmueble destinado al desarrollo de actividades distintas a las anteriores, que incluyen actividades tales como administración general, deportes u otros." },

        ]);
        const vigencia_list = ref([
            { options: 'Eliminar registro cargado', value: 0 },
            { options: 'Mantener registro cargado', value: 1 },

        ]);

        const goBack = () => {
            router.go(-1); // Navigate to the previous route using Vue Router
        };

        /**
        * Get options for the dropdown field based on the field options string
        */
        const getOptions = (optionKey) => {
            if (optionKey === 'listaComunas') {
                return listaComunas.value;
            }
            if (optionKey === 'situacion_tenencia_list') {
                return situacion_tenencia_list.value;
            }
            if (optionKey === 'uso_exclusivo_list') {
                return uso_exclusivo_list.value;
            }
            if (optionKey === 'funcion_list') {
                return funcion_list.value;
            }
            if (optionKey === 'vigencia_list') {
                return vigencia_list.value;
            }
            return [];
        }

        // Function to submit the form
        const submitForm = async () => {
            submitted.value = true;
            fechaTerminoInvalid.value = false;
            inicioEsMayorTermino.value = false;
            salasConstruidosMayorTotalTerreno.value = false;
            var validarForm = false;
            validarForm = validateForm();
            if (!validarForm) {
                // Notify the user about the empty fields
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Existen campos vacios', life: 5000 });
            }
            if (fechaTerminoInvalid.value != "" && fechaTerminoInvalid.value == true) {
                // Notify the user about the empty fields
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: '"Fecha de termino de tenencia" debe ser igual o mayor al año a informar', life: 5000 });
            }
            if (inicioEsMayorTermino.value == true) {
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'La "Fecha de inicio de tenecia" no debe ser mayor que la "Fecha de termino de tenencia"', life: 5000 });
            }
            if (salasConstruidosMayorTotalTerreno.value == true) {
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Los metros cuadrados de salas de clases no pueden ser mayor que los metros cuadrados construidos del terreno', life: 5000 });
            }
            // Agregar validacionde que la fecha de inicio de tenenencia no puede ser mayor que la fecha de termino de tenencia
            if (!validarForm || (fechaTerminoInvalid.value != "" && fechaTerminoInvalid.value == true) || (inicioEsMayorTermino.value == true) || (salasConstruidosMayorTotalTerreno.value == true)) {
                return;
            }

            try {
                isLoading.value = true;

                const success = await patchInmueblePermanente(formData.value);

                if (success) {
                    // Notify the user of successful form submission
                    toast.add({ severity: 'success', summary: 'Correcto', group: 'bl', detail: 'Formulario actualizado correctamente', life: 5000 });
                    submitted.value = false; // Reset submitted state

                    // Redirect to the previous route with a success message
                    router.push({
                        name: 'Pagina-detalle-oti',
                        params: { anio_proceso: encryptData(anio_proceso.value) }, // Replace with the actual route name or path
                        query: { success: 'Formulario actualizado correctamente' }
                    });
                }
            } catch (error) {
                toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'Hubo un problema al enviar el formulario', life: 5000 });
            } finally {
                isLoading.value = false;
            }
        };

        const validateForm = () => {
            let hasErrors = false;

            // Iterate through formData keys and validate non-conditional fields
            Object.keys(formData.value).forEach((key) => {
                const value = formData.value[key];
                // Skip conditional fields that are checked later
                if (
                    key === "porcentaje_uso" ||
                    key === "nombre_institucion_comparte" ||
                    key === "fecha_inicio_tenencia" ||
                    key === "fecha_termino" ||
                    key === "descripcion_otra_tenencia" ||
                    key === "desc_otras_funciones" ||
                    key === "creador_id"
                ) {
                    return;
                }

                // Check if the value is null or empty
                if (value === null || value === "") {
                    hasErrors = true;
                }
            });

            // Check conditional fields based on specific conditions
            if (formData.value.uso_exclusivo == 2) {
                console.log("uso_exclusivo igual a 2")
                if (formData.value.porcentaje_uso === null || formData.value.nombre_institucion_comparte === "") {
                    hasErrors = true;
                }
            } else {
                formData.porcentaje_uso = null;
                formData.nombre_institucion_comparte = null;
            }

            if (formData.value.situacion_tenencia > 1 && formData.value.situacion_tenencia < 6) {
                console.log("situacion_tenencia entre 1 y 6")
                if (formData.value.fecha_inicio_tenencia === "" || formData.value.fecha_termino === "") {
                    hasErrors = true;
                }
            } else if (formData.value.situacion_tenencia == 1) {
                formData.fecha_inicio_tenencia = "";
                formData.fecha_termino = "";
                formData.descripcion_otra_tenencia = null;
            }

            if (formData.value.situacion_tenencia == 6) {
                console.log("situacion_tenencia igual a 6")
                if (formData.value.fecha_inicio_tenencia === "" || formData.value.descripcion_otra_tenencia === "" || formData.value.fecha_inicio_tenencia === null || formData.value.descripcion_otra_tenencia === null) {
                    hasErrors = true;
                }
                formData.fecha_termino = "";
            }
            if (formData.value.funcion.length === 0) {
                hasErrors = true;
            }

            if (formData.value.funcion.includes("funcion_otras")) {
                console.log("funcion_otras")
                if (formData.value.desc_otras_funciones === "" || formData.value.desc_otras_funciones === null) {
                    hasErrors = true;
                }

            } else {
                formData.value.desc_otras_funciones = null;
            }
            if (formData.value.fecha_termino != "") {
                // Validation for fecha_termino has to be after anio_proceso-1
                if ((formData.value.fecha_termino.getFullYear() < (anio_proceso.value - 1))) {
                    fechaTerminoInvalid.value = true;
                }
            }
            if (formData.value.fecha_inicio_tenencia != "" && formData.value.fecha_termino != "") {
                // Validation for anio_inicio_uso_inmueble has to be lower than fecha_termino 
                if (formData.value.fecha_inicio_tenencia >= formData.value.fecha_termino) {
                    inicioEsMayorTermino.value = true;
                }
            }
            if (formData.value.total_m2_salas_clases > formData.value.total_m2_edificados) {
                salasConstruidosMayorTotalTerreno.value = true;

            }
            if (hasErrors) {
                // Return false if there are any errors
                return false;
            }
            return true;
        };

        const editingForm = () => {
            editForm.value = true;
        }

        const cancelEditingForm = async () => {
            editForm.value = false;
            await fetchInmueblePermanenteData(props.inmueblePermanente_id);
            formData.value = initialFormData;
            formData.value.tipo_infraestructura = "Inmueble Permanente";
        }


        const patchInmueblePermanente = async (formDataCompleted) => {
            isLoading.value = true; // Start loading
            try {
                if (formDataCompleted.nombre_identificacion != null && formDataCompleted.nombre_identificacion != "") {
                    formDataCompleted.nombre_identificacion = formDataCompleted.nombre_identificacion.trim();
                }
                if (formDataCompleted.direccion_inmueble != null && formDataCompleted.direccion_inmueble != "") {
                    formDataCompleted.direccion_inmueble = formDataCompleted.direccion_inmueble.trim();
                }
                if (formDataCompleted.nombre_institucion_comparte != null && formDataCompleted.nombre_institucion_comparte != "") {
                    formDataCompleted.nombre_institucion_comparte = formDataCompleted.nombre_institucion_comparte.trim();
                }
                if (formDataCompleted.descripcion_otra_tenencia != null && formDataCompleted.descripcion_otra_tenencia != "") {
                    formDataCompleted.descripcion_otra_tenencia = formDataCompleted.descripcion_otra_tenencia.trim();
                }
                if (formDataCompleted.desc_otras_funciones != null && formDataCompleted.desc_otras_funciones != "") {
                    formDataCompleted.desc_otras_funciones = formDataCompleted.desc_otras_funciones.trim();
                }
                formDataCompleted.inmueblePermanente_id = inmueblePermanenteData.value.inmueblePermanente_id
                formDataCompleted.infraestructuraRecurso_id = inmueblePermanenteData.value.infraestructuraRecurso_id
                formDataCompleted.version_lock = inmueblePermanenteData.value.version_lock
                formDataCompleted.userToken = "Bearer " + userToken.value;
                const response = await axios.patch(API_BASE_URL + `inmueblePermanente/${formDataCompleted.inmueblePermanente_id}`, formDataCompleted, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    },
                });

                // Check if the response is successful (status code 200)
                if (response.status === 200) {
                    console.log("Success:", response.data); // Handle the response data if needed
                    return true; // Return true on success
                } else {
                    console.error("Unexpected response status:", response.status);
                    return false; // Return false if not 200 OK
                }
            } catch (error) {
                if (error.status === 403) {
                    toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El registro no puede ser modificado', life: 5000 });
                }
                if (error.status === 409) {
                    toast.add({ severity: 'error', summary: 'Error', group: 'bl', detail: 'El registro fue modificado por otro usuario, inténtelo más tarde', life: 10000 });
                }
                console.error("Error fetching InfraestructuraRecurso data:", error.response || error);
                return false; // Return false on error
            } finally {
                isLoading.value = false; // End loading
            }
        };

        // On component mount, fetch data
        onMounted(async () => {
            console.log("token");
            console.log(userToken);

            if (props.inmueblePermanente_id) {
                await fetchInmueblePermanenteData(props.inmueblePermanente_id);
                formData.value = initialFormData;
                formData.value.tipo_infraestructura = "Inmueble Permanente";
                anio_proceso.value = initialFormData.anio_proceso;
                maxDate.value = new Date(anio_proceso.value - 1, 11, 31);
                await fetchInfraestructuraRecursoData(anio_proceso.value);
                await fetchListaComunas();
            }
        });
        // Function to fetch InfraestructuraRecurso data based on the year
        const fetchInfraestructuraRecursoData = async (year) => {

            isLoading.value = true; // Start loading
            try {
                const response = await axios.get(API_BASE_URL + "infraestructuraRecurso/" + year, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                infraestructuraRecursoData.value = response.data;
                console.log(infraestructuraRecursoData.value);
            } catch (error) {
                console.error("Error fetching InfraestructuraRecurso data:", error);
            } finally {
                isLoading.value = false; // End loading
            }
        };

        // Function to fetch inmueblePermanente data based on the id
        const fetchInmueblePermanenteData = async (inmueblePermanente_id) => {
            console.log(userToken.value);
            isLoading.value = true; // Start loading
            try {
                const response = await axios.get(API_BASE_URL + "inmueblePermanente/" + inmueblePermanente_id, {
                    headers: {
                        Authorization: `Bearer ${userToken.value}`
                    }
                });
                inmueblePermanenteData.value = response.data;
                console.log(inmueblePermanenteData.value);
                // Map fetched data to initialFormData dynamically
                Object.keys(initialFormData).forEach(key => {
                    if (key == "funcion") {
                        let variableName = null;
                        // Iterate through the object keys
                        for (const key in inmueblePermanenteData.value) {
                            if (key.includes("funcion_") && inmueblePermanenteData.value[key] == "X") {
                                variableName = key; // Assign the key name if the value is "X"
                                break; // Exit the loop once we find the first match
                            }
                        }
                        initialFormData.funcion = variableName;
                    }
                    if (inmueblePermanenteData.value[key] !== undefined) {
                        if (key == "anio_inicio_uso_inmueble") {
                            const date = new Date(Date.UTC(inmueblePermanenteData.value[key], 0, 1, 12, 0, 0));
                            initialFormData[key] = date;
                        } else if (key == "fecha_inicio_tenencia") {
                            if (inmueblePermanenteData.value[key] == null || inmueblePermanenteData.value[key] == "") {
                                initialFormData[key] = "";
                            } else {
                                const date = new Date(inmueblePermanenteData.value[key]);
                                const adjustedDate = new Date(date.getFullYear(), date.getMonth() + 1, 1);
                                initialFormData[key] = adjustedDate;
                            }
                        }
                        else if (key == "fecha_termino") {
                            if (inmueblePermanenteData.value[key] == null || inmueblePermanenteData.value[key] == "") {
                                initialFormData[key] = "";
                            } else {
                                const date = new Date(inmueblePermanenteData.value[key]);
                                const adjustedDate = new Date(date.getFullYear(), date.getMonth() + 1, 1);
                                initialFormData[key] = adjustedDate;
                            }
                        }
                        else {
                            initialFormData[key] = inmueblePermanenteData.value[key];
                        }

                    }
                });
            } catch (error) {
                console.error("Error fetching InfraestructuraRecurso data:", error);
            } finally {
                isLoading.value = false; // End loading
            }
        };

        // Function to fetch InfraestructuraRecurso data based on the year
        const fetchListaComunas = async () => {
            isLoading.value = true; // Start loading
            try {
                const response = await axios.get(API_BASE_URL + "comunas-OTI/", {
                    headers: {
                        'Authorization': "Bearer " + userToken.value
                    }
                });
                listaComunas.value = response.data;
                console.log(listaComunas.value);
            } catch (error) {
                console.error("Error fetching InfraestructuraRecurso data:", error);
            } finally {
                isLoading.value = false; // End loading
            }
        };

        return {
            isLoading,
            globalLoading,
            anio_proceso,
            inmueblePermanenteData,
            fetchInmueblePermanenteData,
            fetchListaComunas,
            listaComunas,
            submitted,
            formData,
            formFormat1,
            formFormat2,
            formFormat3,
            formFormat4,
            formFormat5,
            formFormat6,
            comuna_list,
            situacion_tenencia_list,
            uso_exclusivo_list,
            funcion_list,
            getOptions,
            submitForm,
            validateForm,
            formIsLoading,
            submitted,
            patchInmueblePermanente,
            router,
            editForm,
            editingForm,
            cancelEditingForm,
            vigencia_list,
            goBack,
            infraestructuraRecursoData,
            fechaTerminoInvalid,
            inicioEsMayorTermino,
            hasAccess,
            permissionsList,
            userToken,
            salasConstruidosMayorTotalTerreno,
            minDate,
            maxDate
        }
    },
    methods: {
        handleInput(event, fieldName) {
            let value = event.target.value;

            let sanitizedValue = value.replace(/^( |[^a-zA-Z0-9 ])|[^a-zA-Z0-9 ]/g, '') // Remove invalid characters and prevent leading space
                .replace(/\s+/g, ' '); // Replace multiple spaces with a single space

            // Update the formData with the sanitized value
            this.formData[fieldName] = sanitizedValue;
        }
    },
}
</script>