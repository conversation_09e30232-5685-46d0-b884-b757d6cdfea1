<template>
    <div class="surface-ground py-7 md:px-5 lg:px-6">
        <div class="card">
            <TabView>
                <TabPanel header="Crear Nueva Matrícula">
                    <h2>Archivo Csv para crear nuevas matrículas en sistema</h2>
                    <LoadCsvFile>

                    </LoadCsvFile>
                </TabPanel>
                <TabPanel header="" :disabled="true">
                    <p class="m-0">

                    </p>
                </TabPanel>
                <TabPanel header="" :disabled="true">
                    <p class="m-0">

                    </p>
                </TabPanel>
                <TabPanel header="" :disabled="true">
                    <p class="m-0">

                    </p>
                </TabPanel>
                <TabPanel header="" :disabled="true">
                    <p class="m-0">

                    </p>
                </TabPanel>
                <TabPanel header="" :disabled="true">
                    <p class="m-0">

                    </p>
                </TabPanel>
                <TabPanel header="" :disabled="true">
                    <p class="m-0">

                    </p>
                </TabPanel>
                <TabPanel header="Editar Matrículas Existentes">
                    <h2>Archivo Csv para actualizar matrículas en sistema</h2>
                    <LoadCsvFile>

                    </LoadCsvFile>
                </TabPanel>
            </TabView>
        </div>

    </div>
    <Toast position="bottom-left" group="bl" />
</template>

<script setup>
import LoadCsvFile from './LoadCsvFile.vue';

</script>