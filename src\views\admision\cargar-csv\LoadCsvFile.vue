<template>

    <div class='surface-card shadow-3 p-3 border-round'>
        <Message v-if=finishedPost severity="success" @close="handleSuccessMessageClose()">
            Archivo cargado correctamente en sistema</Message>
        <div class="row" v-if="!finishedPost">
            <Toast position="bottom-left" group="bl" />
            <div v-if="!isLoading && !fileHasDuplicates">
                <input lang="esp" id="inputFileCSV" class='custom-input-file' type="file" @change="handleFileUpload"
                    accept=".csv" style="margin-right:1%">
                <Tag v-if="fileisCorrect" icon="pi pi-check" severity="success" value="Archivo Correcto"></Tag>
                <Tag v-if="hasEmptyField || hasInvalidField" icon="pi pi-times" severity="danger"
                    value="Error de carga"></Tag>
            </div>
            <br />
            <div v-if="isLoading">
                <div class="form-row">
                    <div class="form-group">
                        <i class="pi pi-spin pi-cog" style="font-size: 6rem"></i>
                        <div><label class="labelLoading">{{ loadingText }}</label></div>
                    </div>
                </div>
            </div>
            <div v-if="fileisCorrect && !fileHasDuplicates">
                <div class="form-row">
                    <h4>Archivo correcto: Se detectaron {{ csvData.length }} filas</h4>
                </div>
                <div class="form-row">
                    <Button v-if="!isLoading" label="Cargar datos" severity="primary" @click="PostDataToServer()"
                        outlined></Button>
                </div>
                <br />
            </div>
            <div v-if="fileHasDuplicates">
                <div class="form-row">
                    <Message severity="error" @close="handleSuccessMessageClose()">
                        Se detectaron ruts repetidos en sistema</Message>
                </div>
                <div style="text-align: center;padding-bottom:2%;">
                    <label>Descargar detalles de errores</label>
                </div>
                <div class=" form-row">
                    <Button v-if="!isLoading" label="Descargar" severity="primary" @click="downloadTextFileDuplicated()"
                        outlined></Button>
                </div>
                <br />
            </div>
            <div v-if="!fileisCorrect && (hasEmptyField || hasInvalidField)">
                <div class="form-row">
                    <h4>Descargar detalles de errores</h4>
                </div>
                <div class="form-row">
                    <Button v-if="!isLoading" label="Descargar" severity="primary" @click="downloadTextFile"
                        outlined></Button>
                </div>
                <br />
            </div>
        </div>
    </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue';
import Papa from 'papaparse';
import axios from 'axios';
import { useToast } from "primevue/usetoast";

export default {
    name: 'AdmisionCsvFile',

    setup() {
        const API_BASE_URL = import.meta.env.VITE_BACKEND_BASE_URL;
        // Obtain the value from localStorage to check the day or night mode
        // Loading "GIF" variables
        const loadingTexts = ['cargando', 'cargando.', 'cargando..', 'cargando...'];
        const loadingIndex = ref(0);
        const loadingText = ref(loadingTexts[0]);
        // Toast notification variable
        const toast = useToast();
        // Loading variable when the site  is calculating something
        const isLoading = ref(false);
        // After checking all the .csv varibables this value should be true or false if is correct or incorrect
        const fileisCorrect = ref(null);
        // Where the .csv data is saved
        const csvData = ref([]);
        // Array of string where the data errors is saved
        const errorLocations = ref([]);
        // Changes to true if a variable is empty
        const hasEmptyField = ref(false);
        // Changes to true if a variable is invalid
        const hasInvalidField = ref(false);
        // Changes to true when the csv file is uploaded to the DB correctly
        const finishedPost = ref(false);
        // Variable to check if the backend already has an unique rut stroed
        const fileHasDuplicates = ref(false);
        // A list of true/false for each rut send to the backend
        const repeatedRuts = ref([]);

        

        /** Constructor for Toast notification  */
        const showToast = (severity, summary, detail, life) => {
            toast.add({ severity: severity, summary: summary, group: 'bl', detail: detail, life: life });
        };
        /** Handles the file uppload using the library papaparser
         * it will read the data of the .csv file and will try to create the data type of the field (example:
         * if the data is only text it will be a "string", if only number the variable will be a "int").
         * Checks if each field in the .csv file is empty or invalid.
         */
        const handleFileUpload = (event) => {
            isLoading.value = true;
            const file = event.target.files[0];
            fileisCorrect.value = false;
            hasEmptyField.value = false;
            hasInvalidField.value = false;
            errorLocations.value = [null];
            let data;
            if (file) {

                const reader = new FileReader();

                reader.onload = () => {
                    const fileContent = reader.result;
                    Papa.parse(fileContent, {
                        header: true, // Treat the first row of the CSV file as headers
                        dynamicTyping: true, // Convert values to appropriate types
                        complete: (results) => {
                            data = results.data;

                            // Check if the file is empty or only have the headers
                            if (data.length == 0) {
                                fileisCorrect.value = false;
                                hasInvalidField.value = true;
                                isLoading.value = false;
                                errorLocations.value.push("Archivo vacio");
                                return;
                                // If not empty check if it has fewer headers than necessary
                            } else {
                                var fileHeaders = Object.keys(data[0]);
                                if (fileHeaders.length < 5) {
                                    fileisCorrect.value = false;
                                    hasInvalidField.value = true;
                                    isLoading.value = false;
                                    errorLocations.value.push("Numero de columnas en la cabecera inferior al requerido");
                                    return;
                                }
                                if (fileHeaders.includes("Nombre") && fileHeaders.includes("Apellido") &&
                                    fileHeaders.includes("Rut") && fileHeaders.includes("Fecha") && fileHeaders.includes("Carrera")) {
                                    // Check if the last row is null or empty
                                    const lastRow = data[data.length - 1];
                                    if (Object.values(lastRow).every(value => value === null || value === "")) {
                                        // If last row is null or empty, remove it 
                                        data.pop();
                                    }
                                    checkForEmptyColumns(data);
                                    csvData.value = data;
                                    isLoading.value = false;
                                } else {
                                    if (data[0].hasOwnProperty("__parsed_extra")) {
                                        data[0]["contenido_Extra"] = data[0]["__parsed_extra"];

                                    }
                                    fileisCorrect.value = false;
                                    hasInvalidField.value = true;
                                    isLoading.value = false;
                                    errorLocations.value.push("No se encontraron las cabeceras necesarias en el archivo");
                                    return;
                                }
                            }
                        },
                        error: (error) => {
                            isLoading.value = false;
                            fileisCorrect.value = false;
                            showToast("error", "Error al cargar el archivo",
                                "Ocurrio un error al intentar cargar el archivo .csv", 5000);

                        }
                    });
                };

                reader.readAsText(file);

            } else {
                isLoading.value = false;
            }
        };
        /**
         * This method will remove the blank spaces between the target string
         * @param {string} data 
         */
        const removeblankSpaceBetweenData = (data) => {
            data.forEach((row, rowIndex) => {
                Object.entries(row).forEach(([key, value]) => {
                    if (typeof value == "string")
                        value = value.trim();
                })
            })
        };

        /**
         * Check if each field in the .csv file is empty or invalid the update the "hasEmptyField" or "hasInvalidField".
         * "FileisCorrect is used at the end of the method to set if the .csv is correct or has errors"
         * 
         * @param {array of .csv variables} data 
         */
        const checkForEmptyColumns = (data) => {
            data.forEach((row, rowIndex) => {

                if (row.hasOwnProperty("__parsed_extra")) {
                    row["contenido_Extra"] = row["__parsed_extra"]; // Change name to the key of extra content
                    delete row["__parsed_extra"]; // Remove the old property

                }
                const emptyData = ref('');
                Object.entries(row).forEach(async ([key, value], columnIndex) => {
                    const invalidString = ref('');
                    // Campo vacio
                    if (!value) {
                        emptyData.value = (`Campo vacio en la Fila: ${rowIndex + 1}, Identificador: ${key} `);
                        hasEmptyField.value = true;
                        // No esta vacio        
                    } else {
                        // Ver si el dato es valido
                        switch (key) {
                            case "Nombre":
                                if (!containsOnlyLetters(value)) {
                                    invalidString.value = invalidString.value + "Campo invalido en la Fila: " + (rowIndex + 1) + ", Identificador: " + key;
                                    hasInvalidField.value = true;
                                }
                                break;
                            case "Apellido":
                                if (!containsOnlyLetters(value)) {
                                    invalidString.value = invalidString.value + "Campo invalido en la Fila: " + (rowIndex + 1) + ", Identificador: " + key;
                                    hasInvalidField.value = true;
                                }
                                break;
                            case "Rut":
                                if (!validarRut(value)) {
                                    invalidString.value = invalidString.value + "Campo invalido en la Fila: " + (rowIndex + 1) + ", Identificador: " + key;
                                    hasInvalidField.value = true;
                                }
                                else if (!checksubmitedRut(data, value, rowIndex)) {
                                    invalidString.value = invalidString.value + "Campo repetido en la Fila: " + (rowIndex + 1) + ", Identificador: " + key;
                                    hasInvalidField.value = true;
                                }
                                break;
                            case "Fecha":
                                if (!isValidDate(value)) {
                                    invalidString.value = invalidString.value + "Campo invalido en la Fila: " + (rowIndex + 1) + ", Identificador: " + key;
                                    hasInvalidField.value = true;
                                }
                                break;
                            case "Carrera":
                                if (!containsOnlyLetters(value)) {
                                    invalidString.value = invalidString.value + "Campo invalido en la Fila: " + (rowIndex + 1) + ", Identificador: " + key;
                                    hasInvalidField.value = true;
                                }
                                break;
                            default:
                                invalidString.value = invalidString.value + "Campo invalido en la Fila: " + (rowIndex + 1) + ", Identificador: " + key;
                                hasInvalidField.value = true;
                                break;
                        }
                    }
                    if ((hasEmptyField.value || hasInvalidField.value) && (emptyData.value != '' || invalidString.value != '')) {
                        errorLocations.value.push(emptyData.value + invalidString.value);
                        emptyData.value = "";
                        invalidString.value = "";
                    }

                });
            })

            //Final comprobation if the "if statement is true" then the .csv file has errors
            if (hasEmptyField.value || hasInvalidField.value) {
                showToast("error", "Error al cargar el archivo", "El archivo contiene errores", 5000);
                isLoading.value = false;
                fileisCorrect.value = false;
                //File is correct
            } else {
                fileisCorrect.value = true;
            }
        };
        /**
         * check if the given date is in the following format DD/MM/YYYY
         * @param {string} dateString 
         */
        function isValidDate(dateString) {
            // Regular expression for DD/MM/YYYY format
            var regex = /^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/;

            if (regex.test(dateString)) {
                // Input date string
                var inputDate = dateString.toString();
                // Split the date string into day, month, and year
                var dateComponents = inputDate.split("/");
                // Rearrange the components to form the desired format (yyyy-mm-dd)
                dateString = dateComponents[2] + "-" + dateComponents[1] + "-" + dateComponents[0];

                var dateObject = new Date(dateString);
                if (dateObject.getTime() && dateString === dateObject.toISOString().slice(0, 10)) {
                    return true;
                } else {
                    return false;
                }

            } else {
                return false;
            }
        }

        /**
         * check if the given string only contains letters and accents marks
         * @param {String} inputString 
         */
        function containsOnlyLetters(inputString) {
            // Regular expression pattern to match only letters including accent marks
            var pattern = /^[a-zA-Z\u00C0-\u017F\s]+$/;

            // Check if the input string matches the pattern
            if (pattern.test(inputString)) {
                return true;
            } else {
                return false;
            }
        }
        /** First check if the given rut can be converted to string then
         * removes the "." and "-" from the given rut */
        function validarRut(rut) {
            if (rut !== undefined && typeof rut.toString === 'function') {

                rut = rut.toString();
                rut = rut.replace(/[.-]/g, ''); // Remover puntos y guiones
                if (!/^[0-9]+[0-9kK]{1}$/.test(rut)) return false; // Formato incorrecto

                // separar digito verificador
                var num = rut.substring(0, rut.length - 1);
                var dig = rut.substring(rut.length - 1);

                // Calcular dígito verificador
                var suma = 0;
                var multiplo = 2;

                // Para cada dígito del número, de derecha a izquierda
                for (var i = num.length - 1; i >= 0; i--) {
                    suma += parseInt(num.charAt(i)) * multiplo;
                    if (multiplo === 7) multiplo = 2;
                    else multiplo++;
                }

                // Calcular el resto de la división
                var resto = suma % 11;
                var dv = 11 - resto;

                // Verificar el dígito verificador
                if (dv === 11) dv = 0;
                else if (dv === 10) dv = 'k';
                else dv = dv.toString();
                //return true si el digito verificador y el digito calculado son iguales
                return dv == dig.toLowerCase() || dv == dig.toUpperCase();
            } else {
                return false
            }
        }
        /**
         * check if the given rut is repeated on the same csv file
         */
        const checksubmitedRut = (data, stringRut, rutIndex) => {
            stringRut = stringRut.toString().replace(/[.\-\s]/g, '');
            for (let i = 0; i < data.length; i++) {
                const row = data[i];
                if (i !== rutIndex) {
                    var rowRut = row.Rut.toString().replace(/[.\-\s]/g, '');
                    if (stringRut == rowRut) {
                        return false;
                    }
                }
            }
            return true;
        };

        /** This method make the string "Loading" behave like a gif */
        const updateLoadingText = () => {
            setInterval(() => {
                loadingIndex.value = (loadingIndex.value + 1) % loadingTexts.length;
                loadingText.value = loadingTexts[loadingIndex.value];
            }, 500);
        };

        /** Using the "errorLocation array" genarates a downloadable .txt*/
        const downloadTextFile = () => {
            // Content of the text file
            if (errorLocations.value.length > 0) {
                const textContent = errorLocations.value.join('\n');

                // Create a blob object from the text content
                const blob = new Blob([textContent], { type: 'text/plain' });

                // Create a link element
                const link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);

                // Set the filename for the downloaded file
                link.download = 'Errores.txt';

                // Append the link to the body
                document.body.appendChild(link);

                // Programmatically click the link to trigger the download
                link.click();

                // Remove the link from the body 
                document.body.removeChild(link);

            } else {

            }
        };

        /**
         * Saves the duplicated ruts in a array an then add the errors log to "errorLocation" so the user can download it
         */
        const downloadTextFileDuplicated = () => {

            var duplicatedRuts = [];

            repeatedRuts.value.forEach((element, rowIndex) => {
                if (element == true) {
                    duplicatedRuts.push(rowIndex);
                }
            });
            console.log(duplicatedRuts);
            var duplicatedRutErrorText = [];
            duplicatedRuts.forEach((element, rowIndex) => {
                duplicatedRutErrorText.push("El siguiente rut se encuentra duplicado: " + csvData.value[element].Rut + " , en la fila: " + (parseInt(element) + 2));

            })

            // Content of the text file
            if (errorLocations.value.length > 0) {

                const textContent = duplicatedRutErrorText.join('\n');

                // Create a blob object from the text content
                const blob = new Blob([textContent], { type: 'text/plain' });

                // Create a link element
                const link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);

                // Set the filename for the downloaded file
                link.download = 'Errores.txt';

                // Append the link to the body
                document.body.appendChild(link);

                // Programmatically click the link to trigger the download
                link.click();

                // Remove the link from the body 
                document.body.removeChild(link);


            } else {

            }

        }
        /**
         * First Check if the variable "csvData" array has repeated values ("RUT") in 
         * the DB and if not POST the data to the database
         */
        const PostDataToServer = async () => {
            var rutArray = [];
            csvData.value.forEach(element => {

                element.Rut = element.Rut.toString().replace(/[.\-\s]/g, '');
                rutArray.push(element.Rut.toString().replace(/[.\-\s]/g, ''));
                // Input date string
                var inputDate = element.Fecha.toString();
                // Split the date string into day, month, and year
                var dateComponents = inputDate.split("/");
                // Rearrange the components to form the desired format (yyyy-mm-dd)
                element.Fecha = dateComponents[2] + "-" + dateComponents[1] + "-" + dateComponents[0];
            });
            repeatedRuts.value = await checkRut(rutArray);
            var existInDB = false;
            await repeatedRuts.value.forEach(element => {
                if (element == true) {
                    existInDB = true;
                } else {
                }
            })
            if (existInDB == true) {
                fileHasDuplicates.value = true;
            } else {
                postCsvData();
            }
        }

        /** 
         * Check in the backend if the given array contains a repeated value ("rut")
         * @param Array Array of the given value to check in the Backend */
        const checkRut = async (array) => {
            try {
                isLoading.value = true;
                const response = await axios.post(API_BASE_URL + "estudiante/check", {
                    body: array
                });
                if (response.status === 200) {
                    console.log('Post successful');
                    console.log(response.data.data);
                    isLoading.value = false;
                    return response.data.data;
                }
            } catch (error) {
                error.value = 'Error fetching data';
                isLoading.value = false;
                return false;
            } finally {
                console.log("Finally");
                isLoading.value = false;
            }
        }

        /** 
        * Send the data to the backend to be stored in the DB, utilizes the variabel "csvData"
        */
        const postCsvData = async () => {
            try {
                isLoading.value = true;
                const response = await axios.post(API_BASE_URL + "estudiante/csvData", {
                    body: csvData.value
                });
                if (response.status === 201) {
                    console.log('Post successful');
                    isLoading.value = false;
                    finishedPost.value = true;
                    return response.data.data;
                }
            } catch (error) {
                if (error.response.data.status === 409) {
                    errorMessage.value = "El rut del estudiante ya se encuentra en sistema";
                    return response.data.data;
                }
                error.value = 'Error fetching data';
                isLoading.value = false;
                return false;
            } finally {
                console.log("Finally");
                isLoading.value = false;
            }
        }
        /**
         * When pressing the closing button call this function 
         * and reload the page
         */
        const handleSuccessMessageClose = () => {
            // Function to execute when message is closed
            console.log('Message closed');
            location.reload();

        };

        // When the application is created
        onMounted(() => {
            // Instanciate the loading texts
            updateLoadingText();
        });

        return {
            isLoading,
            fileisCorrect,
            csvData,
            handleFileUpload,
            loadingTexts,
            loadingIndex,
            loadingText,
            updateLoadingText,
            checkForEmptyColumns,
            errorLocations,
            hasEmptyField,
            finishedPost,
            downloadTextFile,
            downloadTextFileDuplicated,
            hasInvalidField,
            isValidDate,
            containsOnlyLetters,
            validarRut,
            removeblankSpaceBetweenData,
            checkRut,
            PostDataToServer,
            handleSuccessMessageClose,
            checksubmitedRut,
            fileHasDuplicates,
            repeatedRuts
        };
    },
};
</script>


<style>
@media screen and (max-width: 450px) {
    .custom-input-file {
        background-color: transparent;
        border-radius: 5px;
        border: 2px solid #0061C2;
        padding: 0.5em 1em;
        cursor: pointer;
        font-size: 10px;
    }
}

@media screen and (min-width: 451px) {
    .custom-input-file {
        background-color: transparent;
        border-radius: 5px;
        border: 2px solid #0061C2;
        border: "black";
        padding: 0.5em 1em;
        cursor: pointer;
    }
}

.form-row {
    display: flex;
    justify-content: space-around;
    margin-bottom: 10px;
}

.form-group {
    flex: 0 0 0;

}

.pi-spin {
    color: #0071e3;
}

#input .custom-input-file {
    background-color: transparent;
}

/* Optional: Hover style */
.custom-input-file:hover {
    background-color: #e0e0e055;
}
</style>